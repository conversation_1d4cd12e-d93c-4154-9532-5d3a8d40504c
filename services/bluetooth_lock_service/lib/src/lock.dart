import 'dart:async';

import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:log/log.dart';
import 'package:service_interface/service_interface.dart';

import './utils.dart';

class Lock implements LockBase {
  static final _userAccessKey = [0xB3, 0x84, 0xFA, 0xE7];
  static final _userAccessChar = "70000000";
  static final _userIdentificationChar = "70010000";
  static final _openingChar = "70020000";
  static final _tag = "Lock";
  static final _timeout = const Duration(seconds: 15);

  late BluetoothDevice _device;
  late String _id;
  late String _name;
  late int _lastSignalStrength;
  final List<BluetoothService> _services = [];

  Lock(this._device, int signalStrength)
      : _id = _device.remoteId.str,
        _name = _device.platformName,
        _lastSignalStrength = signalStrength;

  Lock.fromScanResult(ScanResult result) {
    _device = result.device;
    _id = _device.remoteId.str;
    _name = _device.platformName;
    _lastSignalStrength = result.rssi;
  }

  @override
  String get id => _id;

  @override
  String get name => _name;

  @override
  int get lastSignalStrength => _lastSignalStrength;

  @override
  Future<bool> connect() async {
    // We add more seconds to the timeout because the same timeout is used inside of the method.
    return await _connect().timeout(_timeout.addSeconds(3)).catchError((e) {
      Log().error("[$_tag] [connect] $e");
      return false;
    });
  }

  Future<bool> _connect() async {
    _debugLog("[_connect] _device: ${_device.platformName}");
    // bool _isConnected = false;
    try {
      // We need to clean this to allow to connect again without rescanning.
      _services.clear();

      if (await isConnected()) return true;

      await _device.connect(timeout: _timeout, autoConnect: false);
    } catch (e) {
      Log().error("[$_tag] [_connect] $e");
      return false;
    }
    _debugLog("[_connect] isConnected: true");
    return true;
  }

  @override
  Future<bool> disconnect() async {
    _debugLog("[disconnect] _device: ${_device.platformName}");
    try {
      Stream<BluetoothConnectionState> responseStream = _device.connectionState
          .where((s) => s == BluetoothConnectionState.disconnected);
      Future<BluetoothConnectionState> futureState = responseStream.first;

      await _device.disconnect();

      bool _isDisconnected =
          (await futureState) == BluetoothConnectionState.disconnected;
      _debugLog("[disconnect] isDisconnected: $_isDisconnected");

      return _isDisconnected;
    } catch (e) {
      Log().error("[$_tag] [disconnect] $e");
      return false;
    }
  }

  @override
  Future<bool> open(int userId) async {
    return await _open(userId).timeout(_timeout).catchError((e) {
      Log().error("[$_tag] [open] $e");
      return false;
    });
  }

Future<bool> _open(int userId) async {
  _debugLog("[_open] _device: ${_device.platformName} - userId: $userId");
  bool opened = false;
  try {
    BluetoothCharacteristic? c = await _getChar(_userIdentificationChar);
    if (c == null) {
      throw Exception("Characteristic not found for user identification.");
    }

    await _grantUserServices();

    List<int> key = List.filled(5, 0);
    Utils.writeInt32(key, 0, userId);
    key[4] = Utils.calcChecksumXOR(key, 4);

    await c.write(
      key,
      withoutResponse: c.properties.writeWithoutResponse,
    );

    Stream<bool> stateStream = await onStateChange();
    opened = await stateStream.firstWhere((s) => s == true).timeout(
      Duration(seconds: 5),
      onTimeout: () => false,
    );
  } catch (e) {
    Log().error("[$_tag] [_open] $e");
  } finally {
    try {
      await disconnect();
    } catch (e) {
      Log().error("[$_tag] [disconnect] $e");
    }
  }
  _debugLog("[_open] opened: $opened");
  return opened;
}

  @override
  Future<bool> isConnected() async {
    _debugLog("[isConnected] _device: ${_device.platformName}");
    bool isConnected = false;
    try {
      final connectedDevices = await FlutterBluePlus.connectedDevices;
      _debugLog("[isConnected] connected devices: ${connectedDevices.length}");

      if (connectedDevices.isFilled) {
        // Si hay otros dispositivos conectados, los desconecta.
        connectedDevices.forEach((d) {
          if (d != _device) {
            d.disconnect();
            _debugLog("[isConnected] disconnecting device: ${d.platformName}");
          } else {
            isConnected = true;
          }
        });
      }
    } catch (e) {
      Log().error("[$_tag] [isConnected] $e");
    }
    _debugLog("[isConnected] isConnected: $isConnected");
    return isConnected;
  }

  @override
  Future<int> getSignalStrength({bool updated = false}) async {
    // debugLog("[getSignalStrength] _device: ${_device.platformName} - updated: $updated");
    try {
      if (updated) _lastSignalStrength = await _device.readRssi();
    } catch (e) {
      Log().error("[$_tag] [getSignalStrength] $e");
    }
    // debugLog("[getSignalStrength] _lastSignalStrength: $_lastSignalStrength");
    return _lastSignalStrength;
  }

  @override
  Future<bool> isOpen() async {
    _debugLog("[isOpen] _device: ${_device.platformName}");
    bool isOpen = false;
    try {
      if (!await isConnected()) return false;

      BluetoothCharacteristic? c = await _getChar(_openingChar);
      List<int>? state = await c?.read();
      // denegado = 255 (-1), close = 0, open = 1
      isOpen = state?.first == 1;
    } catch (e) {
      Log().error("[$_tag] [isOpen] $e");
    }
    _debugLog("[isOpen] isOpen: $isOpen");

    return isOpen;
  }

@override
Future<Stream<bool>> onStateChange() async {
  _debugLog("[onLockStateChange] _device: ${_device.platformName}");
  try {
    BluetoothCharacteristic? bluetoothCharacteristic = await _getChar(_openingChar);
    if (bluetoothCharacteristic == null) {
      throw Exception("Opening characteristic not found.");
    }

    bool notifyValue = await bluetoothCharacteristic.setNotifyValue(true);
    if (!notifyValue) {
      throw Exception("Failed to enable notifications on characteristic.");
    }

    final stateStream = bluetoothCharacteristic.lastValueStream.map((event) {
      if (event.isEmpty) return false;
      bool open = event.first == 1;
      _debugLog("[onLockStateChange] event: $event");
      return open;
    });

    return stateStream;
  } catch (e) {
    Log().error("[$_tag] [onStateChange] $e");
    return const Stream.empty();
  }
}


  /// Método privado que otorga acceso a los servicios de usuario al dispositivo.
  Future<void> _grantUserServices() async {
    try {
      BluetoothCharacteristic? c = await _getChar(_userAccessChar);
      await c?.write(
        _userAccessKey,
        withoutResponse: c.properties.writeWithoutResponse,
      );
    } catch (e) {
      Log().error("[$_tag] [_grantUserServices] $e");
    }
  }

  /// Método privado para obtener la característica Bluetooth a partir de un UUID.
  Future<BluetoothCharacteristic?> _getChar(String uuid) async {
    BluetoothCharacteristic? characteristic;
    try {
      if (_services.isEmpty) _services.addAll(await _device.discoverServices());

      for (final service in _services) {
        for (final c in service.characteristics) {
          if (c.uuid.toString().startsWith(uuid)) {
            characteristic = c;
          }
        }
      }
    } catch (e) {
      Log().error("[$_tag] [_grantUserServices] $e");
    }
    return characteristic;
  }

  void _debugLog(String message) {
    Log().debug("[$_tag] $message");
  }

  @override
  String toString() {
    return 'Lock{name: $_name, lastSignalStrength: $_lastSignalStrength}, id: $_id';
  }
}
