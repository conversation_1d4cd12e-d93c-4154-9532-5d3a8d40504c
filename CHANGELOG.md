#

- ✨ [EID-19 HU 4.3 - Footer y botón de Identificación](https://youtrack.movisat.com:8081/issue/EID-19). Botones inferiores en la página de inicio.
- ✨ [EID-23 HU 4.1 Añadir nueva dirección](https://youtrack.movisat.com:8081/issue/EID-23). Proceso completo de vinculación de una nueva dirección y tarjeta.
- ✨ [EID-26 HU 4.1 Login: mostrar alerta si está fuera del ámbito geográfico/marco de actividad](https://youtrack.movisat.com:8081/issue/EID-26).
- ✨ [EID-27 HU 4.1 Registro: poder crear accesos a otros municipios desde el perfil de usuario](https://youtrack.movisat.com:8081/issue/EID-27). Desde la página de perfil, el ciudadano puede registrarse en otros municipios para poder acceder una vez vuelva a iniciar sesión.
- ✨ [EID-37 HU 4.3 Interacción con la Cerradura](https://youtrack.movisat.com:8081/issue/EID-37). Añadido mensaje adicional de error cuando no se encuentran calendarios asociados a una cerradura en el proceso de apertura.
- ✨ [EID-41 HU 4.5 APP EcoIdentificación IoT - Menú](https://youtrack.movisat.com:8081/issue/EID-41). Diseño del menú lateral.
- ✨ [EID-42 OnBoarding](https://youtrack.movisat.com:8081/issue/EID-42). Tutorial inicial.
- ✨ [EID-50 Configuración en página de perfil](https://youtrack.movisat.com:8081/issue/EID-50). Añadida página del perfil.
- ✨ [EID-60 HU 4.1 Registro: implementar el nuevo modelo de municipios y reemplazar el anterior que tenía limitaciones geográficas](https://youtrack.movisat.com:8081/issue/EID-60). Añadido selector de municipio (sin restricciones GPS) para crear un acceso de rol ciudadano, en la página de creación de cuenta y al identificarse, si el usuario no tenía acceso a la aplicación actual.
- ✨ [EID-61 Apertura de contenedor por Bluetooth por proximidad](https://youtrack.movisat.com:8081/issue/EID-61). Proceso completo de apertura de cerraduras cercanas.
- ✨ [EID-68 HU 4.1 Control de campo validador](https://youtrack.movisat.com:8081/issue/EID-68). Integrar la llamada al servidor para obtener la dirección a partir del dato validador en el proceso de vinculación de dirección.
- ✨ [EID-69 Home sin domicilio asignado](https://youtrack.movisat.com:8081/issue/EID-69). Cuando no hay dirección asignada, se deshabilitan los botones inferiores y aparece en el menú lateral la opción de dar de alta domicilio.
- ✨ [EID-71 Listado de domicilios asociados a un usuario](https://youtrack.movisat.com:8081/issue/EID-71). Desde la página de perfil, se pueden ver las direcciones del ciudadano y añadir una dirección nueva.


# 0.1.1+9
- Componente de terminos y condiciones incluido en el registro de cuenta
- Se agrega página de navegación
- Se actualiza a flutter 3

# 0.1.0
- Actualización de HERE

# 0.0.11
- Migración de flutter-develop-3 con diseño nuevo solucionando error en modelo SSO
- Agregar botones de crear cuenta y cambiar contraseña genéricos
- Configuración flavor's para despliegues
<!-- flutter build apk --release -t lib/main_test.dart  --flavor=dev -->
<!-- flutter build web --release --target=lib/main_test.dart -->

<!-- flutter build appbundle -t lib/main_release.dart --flavor=prod -->
<!-- flutter build ios -t lib/main_release.dart  --flavor=prod -->

# 0.0.10
- [0006091: Errores bluetooth](https://bugs.movisat.com/view.php?id=6091)
```
- Solucionado error que impedía reintentar conectar con la cerradura bluetooth hasta que no se reescaneaban todas las cerraduras.
- ℹ️ Durante las pruebas no se ha observado que el bluetooth se quede bloqueado cuando el dispositivo se queda en suspensión.
```
- Corrección. No se muestran siempre el mismo número de contenedores en "Mis contenedores".
- Corrección. No se centra en la posición GPS en el primer arranque.

# 0.0.9
- Página de listado de cerraduras. Ahora en esta página no se re-alizan automáticamente las cerraduras bluetooth. Se ha añadido un botón a la barra superior para realizar la acción manualmente. Este cambio se ha realizado para mejorar el rendimiento de la aplicación y la usabilidad.
- TEST. Página de listado de mis contenedores. Añadido botón con documentación funcional e información de los datos locales. Esto se ha hecho como documentación complementaria y facilitar las pruebas. Para habilitarlo se ha de activar la opción "Mostrar documentación TEST" del menú izquierdo de la página principal.
- Mejoras visuales generales.


# 0.0.8
- TEST. Página principal. Menú izquierdo. Añadido visualizador de algunas tablas de la base de datos local.
- Página principal. Menú izquierdo. Añadido botón para restablecer datos.

# 0.0.7
- 0006085: Correcciones versión 0.0.6.
```
- Si el ciudadano está borrado, no permitir acceder.  
- Página histórico de identificaciones. Que se puedan ver inicialmente las de los últimos 15 días. Añadir un botón para filtrar por día, mes y año (como EcoSAT web) (imagen adjunta).  
- Página histórico de identificaciones. No se filtran las identificaciones por usuarios.  
- Página inicial. El botón del menú izquierdo se cambia por una flecha y deja de poder abrirse el menú izquierdo.
```
- Añadidas animaciones.

# 0.0.6
- Página de listado de cerraduras. Mejoras en la UI. Mostrar los datos del elemento asociado si se encuentra, indicador de distancia respecto al elemento e icono de cerradura que indica si se puede abrir o no.

# 0.0.5
- 0006083: Correcciones vistas durante la reunión 10/07/20203.
```
- Bluetooth no funciona en android 12+
- Página de registro. Aparece un mensaje de error incorrecto "No contiene números" en el campo del nombre.
- El marco geográfico parece no aplicarse en el primer arranque de la app.
- Ciudadanos duplicados.
- Página de identificaciones (historial). Mostrar solo los del último mes y ordenar por fecha descendiente.
- Página inicial. Añadir filtro para ver / ocultar elementos que el ciudadano no puede abrir (los que tienen el icono más pequeño)
- Página de mis contenedores y detalles del contenedor. El botón "Cómo llegar" no funciona.
- Mapa. Si el contenedor tiene cerradura pero el ciudadano no puede abrirlo, mostrarlo con el icono pequeño (actualmente se muestran en grande y con el indicador verde o rojo).
- Mapa. Poner el indicador que se muestra sobre el contenedor (círculo verde o rojo) arriba a la derecha, más separado y reemplazar por un icono del calendario (en vez de un círculo).
```
- Página principal. Menú izquierdo. Añadidos logos de Movisat y del proyecto actual.