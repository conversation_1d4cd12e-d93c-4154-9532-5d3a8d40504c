// import 'package:device_preview/device_preview.dart';
import 'package:ecoidentificacion_iot_app/ecoidentificacion_iot_app.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  Environment().ssoEnvironment = SSOEnvironment.pre;

  EnvironmentDebug(
    isRelease: false,
    isShowedConsoleLog: true,
    // urlServer: isReallyDebugMode() ? "https://newp.api.movisat.com" : null,
    // loginDefault: {
    //   // Usuario de producción. CUIDADO.
    //   // if (isReallyDebugMode()) "<EMAIL>": "Jpalaopalao1.",
    //   "<EMAIL>": "12345678aA#",
    //   "<EMAIL>": "Testapp@2022",
    // },
  );

  // if (isReallyDebugMode()) {
  //   runApp(
  //     DevicePreview(
  //       enabled: !kReleaseMode,
  //       builder: (context) => GlobalPage(
  //         useInheritedMediaQuery: true,
  //         locale: DevicePreview.locale(context),
  //         builder: DevicePreview.appBuilder,
  //       ),
  //     ),
  //   );
  // } else {
  runApp(GlobalPage());
  // }
}
