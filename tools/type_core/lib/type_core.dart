enum SSOEnvironment {
  dev,
  pre,
  pro,
}

enum SSOApplication {
  EcoCompostaje(4),
  EcoReutiliza(8),
  EcoMovil(12),
  EcoSATPuntoLimpio(16),
  // Esta aplicación no existe en la NP al no tener login en el SSO.
  EcoCiudadanoNP(-2),
  EcoIdentificacionIoT(18),
  EcoInstalaciones(19),
  Formulasat(22);

  const SSOApplication(this.id);

  final int id;

  static SSOApplication? fromId(int id) {
    for (final item in SSOApplication.values) {
      if (item.id == id) return item;
    }
    return null;
  }

  /// Indica si la aplicación tiene acceso virtual desde el SSO.
  ///
  /// El acceso virtual se utiliza principalmente para el acceso de ciudadanos.
  bool get hasVirtualAccess {
    switch (this) {
      case SSOApplication.EcoMovil:
      case SSOApplication.EcoSATPuntoLimpio:
      case SSOApplication.EcoInstalaciones:
      case SSOApplication.Formulasat:
        return false;
      case SSOApplication.EcoCompostaje:
      case SSOApplication.EcoReutiliza:
      case SSOApplication.EcoCiudadanoNP:
      case SSOApplication.EcoIdentificacionIoT:
        return true;
    }
  }

  bool get hasCitizenUsers {
    switch (this) {
      case SSOApplication.EcoMovil:
      case SSOApplication.EcoSATPuntoLimpio:
      case SSOApplication.EcoInstalaciones:
      case SSOApplication.Formulasat:
        return false;
      case SSOApplication.EcoCompostaje:
      case SSOApplication.EcoReutiliza:
      case SSOApplication.EcoCiudadanoNP:
      case SSOApplication.EcoIdentificacionIoT:
        return true;
    }
  }
}

enum SSORole {
  // Shared

  /// Administrador.
  admin(3),

  /// ciudadano a nivel de sso.
  citizen(10),

  // Punto limpio
  /// Encargado -Punto Limpio.
  manager(14),

  /// Gestor de punto limpio -Punto Limpio.
  managerCleanPoint(16),

  // EcoCompostaje
  /// Maestro compostador - EcoCompostaje
  composterMaster(11),

  /// Comunitario - EcoCompostaje
  communitary(12),

  /// Gran productor - EcoCompostaje
  bigProducer(13),

  /// Centro educativo - EcoCompostaje
  educationalCenter(17);

  const SSORole(this.id);

  final int id;

  static SSORole fromId(int id) => fromIdOrNull(id) ?? SSORole.citizen;
  static SSORole? fromIdOrNull(int? id) => id == null || id < 0
      ? null
      : _firstWhere2<SSORole>(SSORole.values, (m) => m.id == id);

  bool isAny(List<SSORole> roles) => roles.contains(this);
  bool isNotAny(List<SSORole> roles) => !isAny(roles);

  bool isFromApp(SSOApplication app) {
    switch (app) {
      case SSOApplication.EcoCompostaje:
        return isAny([
          SSORole.admin,
          SSORole.composterMaster,
          SSORole.citizen,
          SSORole.communitary,
          SSORole.bigProducer,
          SSORole.educationalCenter,
        ]);
      case SSOApplication.EcoReutiliza:
        return isAny([SSORole.citizen]);
      case SSOApplication.EcoSATPuntoLimpio:
        return isAny([
          SSORole.admin,
          SSORole.manager,
          SSORole.managerCleanPoint,
        ]);
      case SSOApplication.EcoCiudadanoNP:
        return isAny([SSORole.citizen]);
      case SSOApplication.EcoIdentificacionIoT:
        // TODO: añadir el rol de técnico cuando esté.
        return isAny([SSORole.citizen, SSORole.admin]);
      case SSOApplication.EcoMovil:
      case SSOApplication.EcoInstalaciones:
      case SSOApplication.Formulasat:
        return true;
    }
  }

  /// Indica la jerarquía de los roles.
  ///
  /// Los roles con mayor jerarquía tienen un valor mayor.
  int get hierarchy {
    switch (this) {
      case SSORole.admin:
        return 1;
      case SSORole.manager:
      case SSORole.managerCleanPoint:
      case SSORole.composterMaster:
        return 0;
      case SSORole.citizen:
      case SSORole.communitary:
      case SSORole.bigProducer:
      case SSORole.educationalCenter:
        return -1;
    }
  }

  bool get isCitizen {
    switch (this) {
      case SSORole.citizen:
      case SSORole.communitary:
      case SSORole.bigProducer:
      case SSORole.educationalCenter:
        return true;
      case SSORole.admin:
      case SSORole.manager:
      case SSORole.managerCleanPoint:
      case SSORole.composterMaster:
        return false;
    }
  }
}

T? _firstWhere2<T>(List<T> list, bool Function(T m) match) {
  for (T e in list) {
    if (match(e)) return e;
  }
  return null;
}

enum SyncOperationType {
  send,
  receive,
}

class OnSyncModelData {
  final SyncOperationType operation;
  final int length;

  OnSyncModelData(this.operation, this.length);
}

extension SSORoleListExt2 on List<SSORole> {
  void sortByHierarchy() {
    sort((a, b) => a.hierarchy.compareTo(b.hierarchy));
  }
}
