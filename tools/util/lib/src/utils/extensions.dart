import 'dart:math';

import '../_imports.dart';

// El V2 es porque ya existe el nombre en algunas dependencias.
extension IterableExtensionV2<T> on Iterable<T> {
  /// Alias de [firstWhere] que no requiere asignar [orElse].
  ///
  /// Devuelve null si no se encuentra.
  T? firstWhere2(bool Function(T m) match) => toList().firstWhere2(match);
  List<T> where2(bool Function(T m) match) => where(match).toList();
  bool get isOne => length == 1;
  bool hasIndex(int index) {
    if (isEmpty) return false;
    return index >= 0 && index < length;
  }

  T getIndexOr(int index, T Function() or) =>
      hasIndex(index) ? elementAt(index) : or();
}

extension ListExtensionV3 on List<String?> {
  void removeEmpties() => removeWhere((m) => m == null || m.trim().isEmpty);
}

extension DurationExt1 on Duration {
  Duration addSeconds(int seconds) => Duration(seconds: inSeconds + seconds);
}

extension ListExtensionV5<T> on List<T?>? {
  List<T> notNulls<T>() {
    if (this == null) return [];
    List<T> result = [];
    for (final i in this!) {
      if (i == null) continue;
      result.add(i as T);
    }
    return result;
  }
}

extension ListExtensionV4<T> on List<T>? {
  void addIfNotNull(T? e) {
    if (this == null) return;
    if (e == null) return;
    this!.add(e);
  }

  T? get firstOrNull => isFilled ? this!.first! : null;

  bool get isFilled => this != null && this!.isNotEmpty;
  bool get isNullOrEmpty => this == null || this!.isEmpty;

  /// Check if two lists have the same values (not necesary in the same order).
  bool hasSameValues(List<T>? other) {
    if (this == null && other == null) return true;
    if (this == null || other == null) return false;
    if (this!.length != other.length) return false;

    for (var i = 0; i < this!.length; i++) {
      if (!other.contains(this![i])) return false;
    }

    return true;
  }

  /// Si el valor existe lo eliminará y si no existe lo añadirá.
  void addOrRemove(T e) {
    if (this == null) return;
    if (this!.contains(e))
      this!.remove(e);
    else
      this!.add(e);
  }
}

// El V2 es porque ya existe el nombre en algunas dependencias.
extension ListExtensionV2<T> on List<T> {
  /// Recorre todos los modelos y devuelve el modelo que cumpla con [isMatch]
  /// y posteriormente con [isReplaceable] al compararlo con el anterior que cumplió [isMatch].
  ///
  /// Esta función se utiliza para temas de rendimiento, para poder establecer
  /// 2 condiciones en una sola iteración, una general y otra de comparación entre modelos.
  T? whereReplaceable(bool Function(T m) isMatch,
      bool Function(T original, T replacement) isReplaceable) {
    T? mf;

    for (final m in this) {
      if (isMatch(m)) {
        if (mf == null || isReplaceable(mf, m)) mf = m;
      }
    }

    return mf;
  }

  bool containsAny(List<T> other) {
    for (final o in other) {
      if (contains(o)) return true;
    }
    return false;
  }

  List<T> sortByText(String Function(T o) getText) {
    if (isEmpty) return this;
    sort(
        (a, b) => getText(a).toLowerCase().compareTo(getText(b).toLowerCase()));
    return this;
  }

  List<T> intercalate(T middle) {
    if (isEmpty) return this;
    List<T> result = [];
    for (var i = 0; i < length; i++) {
      result.add(this[i]);
      if (i < (length - 1)) result.add(middle);
    }
    return result;
  }

  double sumDouble(double Function(T m) getDouble) {
    double result = 0;
    for (final m in this) {
      result += getDouble(m);
    }
    return result;
  }

  int sumInt(int Function(T m) getInt) {
    int result = 0;
    for (final m in this) {
      result += getInt(m);
    }
    return result;
  }

  /// Devuelve la lista sin duplicados.
  List<T> distinct() => toSet().toList();

  T? getByIndexNullable(int index) {
    if (isNullOrLesserZero(index)) return null;
    if (index >= length) return null;
    return this[index];
  }

  void removeNulls() => removeWhere((m) => m == null);

  /// Esto ordena las fechas de más antigua a más reciente.
  void sortByDateAsc(DateTime? Function(T o) getDate) =>
      _sortByDate(getDate, isAscendent: true);

  /// Esto ordena las fechas de más reciente a más antigua.
  void sortByDateDesc(DateTime? Function(T o) getDate) =>
      _sortByDate(getDate, isAscendent: false);

  void _sortByDate(DateTime? Function(T o) getDate, {bool isAscendent = true}) {
    if (isListNullOrEmpty(this)) return;

    sort((m1, m2) {
      final d1 = getDate(m1);
      final d2 = getDate(m2);
      if (d1 == null && d2 == null) return 0;
      if (d1 == null) return isAscendent ? 1 : -1;
      if (d2 == null) return isAscendent ? -1 : 1;
      return isAscendent ? d1.compareTo(d2) : d2.compareTo(d1);
    });
  }

  /// Alias de [firstWhere] que no requiere asignar [orElse].
  ///
  /// Devuelve null si no se encuentra.
  T? firstWhere2(bool Function(T m) match) {
    for (T e in this) {
      if (match(e)) return e;
    }
    return null;
  }

  bool exists(bool Function(T m) match) {
    final o = this.firstWhere2(match);
    return o != null;
  }

  void addOrUpdate(T m1, bool Function(T m1, T m2) match) {
    final index = indexWhere((m2) => match(m1, m2));
    if (index < 0)
      add(m1);
    else
      this[index] = m1;
  }

  void addNew(T m1, bool Function(T m1, T m2) match) {
    final index = indexWhere((m2) => match(m1, m2));
    if (index < 0) add(m1);
  }

  /// Alias de [firstWhere] que no requiere asignar [orElse].
  ///
  /// Devuelve null si no se encuentra.
  List<T> where2(bool Function(T m) match) => where(match).toList();
  void forEach2(void Function(T m) funct) => forEach(funct);
  List<E> map2<E>(E Function(T e) funct) => map<E>(funct).toList();
  bool get isOne => length == 1;
  bool get isGreaterOne => length >= 2;
  bool get isOneOrLess => isListNullOrEmpty(this) || length == 1;

  /// Devuelve elementos únicos de la lista.

  List<T> getUniques({
    required bool Function(T object1, T object2) isEquals,
  }) {
    final result = <T>[];
    for (final m in this) {
      if (result.exists((m2) => isEquals(m, m2))) continue;
      result.add(m);
    }
    return result;
  }

  /// Obtiene los elementos del listado paginados.
  ///
  /// [pageIndex] empieza por valor 0.
  List<T> getPaginated(int pageIndex, int itemsByPage) {
    if (isEmpty) return this;

    int start = pageIndex * itemsByPage;
    if (length <= start) return [];

    int end = min((pageIndex + 1) * itemsByPage, length);
    if (end == start) return [];

    return getRange(start, end).toList();
  }

  /// Devuelve la lista que esta envuelta por otras listas. Por ejemplo:
  /// ```
  /// List<List<List<List<double>>>> list =
  /// [
  ///  [
  ///   [
  ///    [1, 2, 3],
  ///    [4, 5, 6],
  ///    [7, 8, 9]
  ///   ]
  ///  ]
  /// ];
  /// ```
  ///
  /// ```dart
  /// final result = list.flatten();
  /// ```
  /// Devuelve:
  /// ```
  /// [ [1, 2, 3], [4, 5, 6], [7, 8, 9] ]
  /// ```
  List<dynamic> flatten() => _flatten(this);

  List<dynamic> _flatten(List<dynamic> lista) {
    if (lista.length == 1 && lista[0] is List) return _flatten(lista[0]);
    return lista;
  }

  T? getWithGreaterNum(
    num? Function(T object) getNum,
  ) {
    T? result;
    for (final m in this) {
      if (result == null) {
        if (getNum(m) != null) result = m;
      } else {
        final temp = getNum(m);
        if (temp == null) continue;
        if (temp <= getNum(result)!) continue;
        result = m;
      }
    }
    return result;
  }

  T? getWithCloserToZero(
    num? Function(T object) getNum,
  ) {
    T? result;
    for (final m in this) {
      if (result == null) {
        if (getNum(m) != null) result = m;
      } else {
        final temp = getNum(m)?.abs();
        if (temp == null) continue;
        if (temp >= getNum(result)!.abs()) continue;
        result = m;
      }
    }
    return result;
  }

  T? getLatestDate(DateTime Function(T o) getDate) {
    if (isEmpty) return null;
    // Clonamos el listado para que no altere el orden original.
    final ms = toList();
    ms.sortByDateDesc(getDate);
    return ms.first;
  }
}

extension ModelSyncExtV2 on ModelSync {
  Future<List<String>> getImages(
    List<String> imagesOrIdsMd5,
    Future<String> Function(String imageOrIdMd5) getImage,
  ) async {
    if (imagesOrIdsMd5.isEmpty) return [];

    List<String> result = [];

    for (int i = 0; i < imagesOrIdsMd5.length; i++) {
      String image = '';

      if (isStringFilled(imagesOrIdsMd5[i])) {
        if (imagesOrIdsMd5[i].isMd5())
          image = await getImage(imagesOrIdsMd5[i]);
        else
          image = imagesOrIdsMd5[i];
      }
      if (isStringFilled(image)) result.add(image);
    }
    return result;
  }
}

extension ModelSyncListIntExtensionV2 on List<int> {
  void sortDesc() => sort((p1, p2) => p2.compareTo(p1));
  void sortAsc() => sort((p1, p2) => p1.compareTo(p2));
}

extension ModelSyncListExtensionV2<T extends ModelSync> on List<T> {
  /// Ordena las fechas de modificación de más antigua a más reciente.
  void sortByModifyDateAsc() {
    sort((p1, p2) => p1.modifyDate.compareTo(p2.modifyDate));
  }

  /// Ordena las fechas de modificación de más reciente a más antigua.
  void sortByModifyDateDesc() {
    sort((p1, p2) => p2.modifyDate.compareTo(p1.modifyDate));
  }

  /// Ordena las fechas de creación de más antigua a más reciente.
  void sortByCreationDateAsc() {
    sort((p1, p2) => p1.creationDate.compareTo(p2.creationDate));
  }

  /// Ordena las fechas de creación de más reciente a más antigua.
  void sortByCreationDateDesc() {
    sort((p1, p2) => p2.creationDate.compareTo(p1.creationDate));
  }

  /// Get the models with a difference between [date] and [getDate] lesser than [days].
  ///
  /// The [date] is neccesary to be able to use the server date (not only local date).
  ///
  /// This ignore the time to calculate the difference.
  List<T> getDiffLesserDays(
    DateTime? date,
    int days,
    DateTime? Function(T m) getDate,
  ) {
    if (date == null) return [];
    date = date.resetTime();

    List<T> modelFinals = [];

    for (final m in this) {
      final modelDate = getDate(m)?.resetTime();
      if (modelDate == null) continue;

      final diff = modelDate.diffDaysAbs(date);
      if (diff > days) continue;

      modelFinals.add(m);
    }
    return modelFinals;
  }

  /// Get the models in the same week of [date]. That include the next month.
  ///
  /// The [date] is neccesary to be able to use the server date (not only local date).
  List<T> getInSameWeek(
    DateTime? date,
    DateTime? Function(T model) getDate,
  ) {
    if (date == null) return [];

    List<T> modelFinals = [];

    // Get the start of the current week (Sunday).
    DateTime startOfWeek =
        date.subtract(Duration(days: date.weekday - 1)).resetTime();

    for (final m in this) {
      final modelDate = getDate(m)?.resetTime();
      if (modelDate == null) continue;

      final diff = modelDate.diffDays(startOfWeek);
      if (diff < 0) continue;
      if (diff > 6) continue;

      modelFinals.add(m);
    }
    return modelFinals;
  }

  /// Get the models in the same month of [date].
  ///
  /// The [date] is neccesary to be able to use the server date (not only local date).
  List<T> getInSameMonth(
    DateTime? date,
    DateTime? Function(T model) getDate,
  ) {
    if (date == null) return [];

    List<T> modelFinals = [];

    for (final m in this) {
      final modelDate = getDate(m);
      if (modelDate == null) continue;
      if (date.month != modelDate.month) continue;
      if (date.year != modelDate.year) continue;

      modelFinals.add(m);
    }
    return modelFinals;
  }

  /// Get the models in the same day of [date].
  ///
  /// The [date] is neccesary to be able to use the server date (not only local date).
  List<T> getInSameDay(
    DateTime? date,
    DateTime? Function(T model) getDate,
  ) {
    if (date == null) return [];

    List<T> modelFinals = [];

    for (final m in this) {
      final modelDate = getDate(m);
      if (modelDate == null) continue;
      if (date.month != modelDate.month) continue;
      if (date.year != modelDate.year) continue;
      if (date.day != modelDate.day) continue;

      modelFinals.add(m);
    }
    return modelFinals;
  }
}

extension UInt8ListExtensionV2 on Uint8List {
  double getKbytes() => (elementSizeInBytes * length) / 1024;
}

// El V2 es porque ya existe el nombre en algunas dependencias.
extension StringExtensionV2 on String {
  /// Checks if string is MD5 hash.
  bool isMd5() => isEmpty ? false : RegExp(r'^[a-f0-9]{32}$').hasMatch(this);
  String toMd5() => CryptoUtil.md5(this);

  Result<String> toResult() => Result.error(this);

  String reverse() => split('').reversed.join();

  String reversePairs() {
    if (isStringNullOrEmpty(this)) return this;
    final array = split('');
    bool isFinish = false;
    int position = 0;
    String text = "";
    do {
      if ((array.length - 1) < (position + 1)) {
        // Si el array es impar, añadimos el último valor.
        if (array.length % 2 != 0) text += array.last;
        isFinish = true;
      } else {
        text += array[position + 1] + array[position];
        position += 2;
      }
    } while (!isFinish);
    return text;
  }

  String toDateNP() {
    String date = this;
    date = date.replaceFirst(" ", "T");
    date = StringUtil.removeUTCLocalDate(date);
    return date;
  }

  String toDateNPOld() {
    String date = this;
    date = date.replaceFirst(" ", "T");
    if (date.contains(".")) date = date.split(".").first;
    return date;
  }

  String colon(String text) => StringUtil.colon(this, text);
  String point() => StringUtil.point(this);
  String jump() => "$this\n";
  String plus(String text) => "$this$text";
  String concat(String text) => "$this $text";
  String parenthesis() => "($this)";
  String parenthesisConcat(String? other) =>
      other.isNullOrEmpty ? this : "$this ($other)";
  String ellipsis() => "$this...";
  String asterisk([bool isEditing = true]) => isEditing ? "$this *" : this;

  /// Muestra un asterisco junto al texto si es requerido y está en modo edición.
  String required([bool isRequired = true, bool isEditing = true]) {
    if (!isEditing) return this;
    if (!isRequired) return this;
    return "$this *";
  }

  /// Añade ceros a la izquierda para que se pueda ordenar un texto numérico.
  String viewSort() => padLeft(20, '0');

  /// Realiza un split si el valor no está vacío, si lo está devuelve una lista vacía.
  ///
  /// Si el [separator] no existe, devolverá una lista con un único elemento con el valor original.
  List<String> split2(String separator) => isEmpty ? [] : split(separator);

  int toIntUnique() {
    if (isEmpty) return 0;

    var hash = 0xcbf29ce484222000;

    var i = 0;
    while (i < length) {
      final codeUnit = codeUnitAt(i++);
      hash ^= codeUnit >> 8;
      hash *= 0x100000001b3;
      hash ^= codeUnit & 0xFF;
      hash *= 0x100000001b3;
    }

    return hash;
  }

  /// Devuelve el texto si no está vacío, si lo está devuelve el texto por defecto.
  replaceIfEmpty(String text) => isStringNullOrEmpty(trim()) ? text : this;

  /// Compares two strings and returns true if they are equal. Ignores case.
  bool equalsIgnoreCase(String text) => toLowerCase() == text.toLowerCase();

  /// Compares two strings and returns true if they are equal. Ignores case and accents.
  bool equalsIgnoreCaseAndAccents(String text) {
    final text1 = toLowerCase();
    final text2 = text.toLowerCase();
    return StringUtil.removeAccents(text1) == StringUtil.removeAccents(text2);
  }

  String removeAccents() => StringUtil.removeAccents(this);
  String replaceEnie() {
    if (isEmpty) return this;
    return replaceAll("ñ", "n").replaceAll("Ñ", "N");
  }

  String replaceMultipleSpacesTogether() {
    if (isEmpty) return this;
    return replaceAll(RegExp(r'\s+'), ' ');
  }
}

extension BoolExtV3 on bool? {
  bool get isTrue => this != null && this!;
  bool get isFalse => this != null && !this!;
  bool get isNullOrFalse => this == null || !this!;
}

extension StringListExtensionV3 on List<String?> {
  String get firstFilled {
    for (final s in this) {
      if (s?.trim().isFilled == true) return s!;
    }
    return "";
  }

  String? get firstFilledOrNull {
    final r = firstFilled;
    return r.isFilled ? r : null;
  }

  String joinFilled([String separator = " - "]) =>
      where((element) => element.isFilled).join(separator);

  List<String> notNullOrEmpty() {
    if (isEmpty) return [];

    List<String> result = [];

    for (final i in this!) {
      if (i == null) continue;
      if (i.isEmpty) continue;
      result.add(i);
    }

    return result;
  }
}

extension StringExtensionV3 on String? {
  String getIfFilledOr([String text = ""]) => isNullOrEmpty ? text : this!;
  bool get isFilled => this != null && this!.trim().isNotEmpty;
  bool get isNullOrEmpty => this == null || this!.trim().isEmpty;
  String shorterDefault() => shorter(
        3,
        ignoreWordsLesserThan: 3,
      );
  String shorter(
    int lengthEachWord, {
    int? maxWords,
    int? ignoreWordsLesserThan,
  }) {
    if (isStringNullOrEmpty(this)) return "";
    final parts = this!.split(" ");
    List<String> result = [];
    for (String p in parts) {
      if (ignoreWordsLesserThan != null && p.length < ignoreWordsLesserThan)
        continue;

      p = p.capitalizeFirst();
      if (p.length > lengthEachWord) p = p.substring(0, lengthEachWord);
      result.add(p);
      if (maxWords != null && maxWords == result.length) break;
    }
    return result.join(".");
  }

  bool isModelRemoved() {
    DateTime? date;
    try {
      date = DateTime.tryParse(this ?? "");
    } catch (e) {
      return false;
    }
    // La fecha de baja viene siempre informada, pero cuando realmente no
    // ha sido asignada, viene con todo a 1.
    return !isNullLesserOrEqualZero(date?.year) && date!.year > 2000;
  }

  String capitalizeFirst() {
    if (this == null) return "";
    if (this!.trim().isEmpty) return "";
    return this![0].toUpperCase() + this!.substring(1);
  }

  String uncapitalizeFirst() {
    if (this == null) return "";
    if (this!.trim().isEmpty) return "";
    return this![0].toLowerCase() + this!.substring(1);
  }

  String get first {
    if (isNullOrEmpty) return "";
    return this![0];
  }

  String ifNullOrEmpty(String text) => isNullOrEmpty ? text : this!;
}

// El V2 es porque ya existe el nombre en algunas dependencias.
extension DateExtensionV2 on DateTime {
  String? toDatabase() => DateUtil.databaseFormat(this);

  /// Formato de fecha de la antigua plataforma.
  String? toDatabaseAP() => DateUtil.databaseFormat(this);

  /// Formato de fechas por defecto del backend de la nueva plataforma.
  String? toDatabaseNP() => DateUtil.databaseFormatNP(this);

  bool isAfterIgnoringTime(DateTime? other) {
    if (other == null) return false;
    final current = resetTime();
    final other2 = other.resetTime();
    return current.isAfter(other2) || current.isAtSameMomentAs(other2);
  }

  bool isBeforeIgnoringTime(DateTime? other) {
    if (other == null) return false;
    final current = resetTime();
    final other2 = other.resetTime();
    return current.isBefore(other2) || current.isAtSameMomentAs(other2);
  }

  DateTime resetSecondMiliMicro() => copyWith(
        second: 0,
        millisecond: 0,
        microsecond: 0,
      );

  DateTime resetTime() => copyWith(
        hour: 0,
        minute: 0,
        second: 0,
        millisecond: 0,
        microsecond: 0,
      );

  DateTime setLastTimeOfDay() => copyWith(
        hour: 23,
        minute: 59,
        second: 59,
        millisecond: 999,
        microsecond: 999,
      );

  DateTime resetDate() => copyWith(
        day: 0,
        year: 0,
        month: 0,
      );

  String viewSort() =>
      "${year.toString().padLeft(4, '0')}-${month.toString().padLeft(2, '0')}-${day.toString().padLeft(2, '0')}-${hour.toString().padLeft(2, '0')}-${minute.toString().padLeft(2, '0')}-${second.toString().padLeft(2, '0')}-${millisecond.toString().padLeft(2, '0')}";

  String view() {
    final d = toLocal();
    return "${d.day.toString().padLeft(2, '0')}/${d.month.toString().padLeft(2, '0')}/${d.year.toString().padLeft(4, '0')} ${d.hour.toString().padLeft(2, '0')}:${d.minute.toString().padLeft(2, '0')}";
  }

  /// Fecha en formato vista sin converir a hora local.
  String viewRaw() {
    final d = this;
    return "${d.day.toString().padLeft(2, '0')}/${d.month.toString().padLeft(2, '0')}/${d.year.toString().padLeft(4, '0')} ${d.hour.toString().padLeft(2, '0')}:${d.minute.toString().padLeft(2, '0')}";
  }

  String viewDate() {
    final d = toLocal();
    return "${d.day.toString().padLeft(2, '0')}/${d.month.toString().padLeft(2, '0')}/${d.year.toString().padLeft(4, '0')}";
  }

  String viewDateSort() =>
      "${year.toString().padLeft(4, '0')}-${month.toString().padLeft(2, '0')}-${day.toString().padLeft(2, '0')}";

  String viewDateNoYear() {
    final d = toLocal();
    return "${d.day.toString().padLeft(2, '0')}/${d.month.toString().padLeft(2, '0')}";
  }

  String viewTime() {
    final d = toLocal();
    return "${d.hour.toString().padLeft(2, '0')}:${d.minute.toString().padLeft(2, '0')}";
  }

  String viewTimeSort() =>
      "${hour.toString().padLeft(2, '0')}-${minute.toString().padLeft(2, '0')}-${second.toString().padLeft(2, '0')}-${millisecond.toString().padLeft(2, '0')}";

  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }

  bool get isYesterday {
    final now = DateTime.now();
    final yesterday = now.subtract(const Duration(days: 1));
    return year == yesterday.year &&
        month == yesterday.month &&
        day == yesterday.day;
  }

  /// Comprueba la diferencia en días de la fecha indicada.
  ///
  /// Puede resultar en un valor menor a 0.
  int diffDays(DateTime? other) {
    if (other == null) return 0;
    return difference(other).inDays;
  }

  /// Return the absolute value of [diffDays].
  int diffDaysAbs(DateTime? other) => diffDays(other).abs();

  /// Comprueba la diferencia en minutos de la fecha indicada.
  ///
  /// Puede resultar en un valor menor a 0.
  int diffMinutes(DateTime? other) {
    if (other == null) return 0;
    return difference(other).inMinutes;
  }

  int diffMicroseconds(DateTime? other) {
    if (other == null) return 0;
    return difference(other).inMicroseconds;
  }

  /// Indica si han pasado cierta cantidad de días respecto a la hora actual.
  bool isPassedDays(int days) {
    final n = (isUtc ? now.toUtc() : now).subtract(Duration(days: days));
    return n.isAfter(this);
  }

  DateTime clone() => DateTime(
        year,
        month,
        day,
        hour,
        minute,
        second,
        millisecond,
        microsecond,
      );

  DateTime copyDate(DateTime d) => copyWith(
        year: d.year,
        month: d.month,
        day: d.day,
      );

  DateTime copyTime(DateTime d) => copyWith(
        hour: d.hour,
        minute: d.minute,
        second: d.second,
        millisecond: d.millisecond,
        microsecond: d.microsecond,
      );

  DateTime copyWith({
    int? year,
    int? month,
    int? day,
    int? hour,
    int? minute,
    int? second,
    int? millisecond,
    int? microsecond,
  }) =>
      DateTime(
        year ?? this.year,
        month ?? this.month,
        day ?? this.day,
        hour ?? this.hour,
        minute ?? this.minute,
        second ?? this.second,
        millisecond ?? this.millisecond,
        microsecond ?? this.microsecond,
      );

  DateTime removeMicroMilliSeconds() => copyWith(
        millisecond: 0,
        microsecond: 0,
      );

  String toStringHourMinuteSecond() =>
      "${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')}";
}

extension NumExtension on num? {
  bool get isGreaterZero => this != null && this! > 0;
  bool get isNullOrLesserOne => this == null || this! < 1;
  bool get isNullOrZero => this == null || this! == 0;
  num? minMax(num min, num max) => this == null ? null : this!.clamp(min, max);
  String toStringNotNull() => this == null ? "" : toString();
}

extension IntExtensionV2 on int {
  String viewSort() => toString().viewSort();
  String view() => toString();
}

extension DoubleExtensionV2 on double {
  String view() => toStringAsFixed(2);
  String viewSort() => toString().viewSort();
  bool get isGreaterZero => this > 0.0;
  double minMax(double min, double max) {
    // If the min is greater that max, we return the max.
    if (min > max) return max;
    return clamp(min, max).toDouble();
  }
}

extension DoubleExtensionV3 on double? {
  double? minMax(double min, double max) =>
      this == null ? null : this!.clamp(min, max);
}

extension MapExt2 on Map<String, dynamic> {
  T? get<T>() {
    T? t;
    for (final e in values) {
      if (e is T) {
        t = e;
        break;
      }
    }
    return t;
  }

  Map<String, dynamic> notNull() {
    List<String> keysToRemove = [];
    for (var k in keys) {
      final v = this[k];
      if (v == null) keysToRemove.add(k);
      // Si es un objeto anidado, aplicamos el cambio al hijo.
      if (v is Map<String, dynamic>) this[k] = v.notNull();
      if (v is List<Map<String, dynamic>>) this[k] = v.map2((e) => e.notNull());
    }

    for (var k in keysToRemove) {
      remove(k);
    }
    return this;
  }

  Map<String, dynamic> parse(
      List<String> keys, dynamic Function(dynamic value) parser) {
    for (var k in this.keys) {
      this[k] = parser(this[k]);
    }
    return this;
  }

  Map<String, dynamic> only(List<String> keys) {
    Map<String, dynamic> map = {};
    for (var k in this.keys) {
      if (keys.contains(k)) map[k] = this[k];
    }
    return map;
  }

  Map<String, dynamic> onlyIndex(List<int> indexes) {
    Map<String, dynamic> map = {};

    for (int i = 0; i < keys.length; i++) {
      if (indexes.contains(i)) map[keys.elementAt(i)] = values.elementAt(i);
    }
    return map;
  }

  /// Devuelve un único Map con todos los valores de los hijos que sean otros Map o List<Map>.
  Map<String, dynamic> collapseNested() {
    Map<String, dynamic> map = {};

    int duplicateSuffix = 2;

    for (final e in entries) {
      if (e.value is Map<String, dynamic>) {
        e.value.forEach((key2, value2) => map[key2] = value2);
      } else if (e.value is List<Map<String, dynamic>>) {
        for (final v2 in e.value as List<Map<String, dynamic>>) {
          for (final e2 in v2.entries) {
            if (map.containsKey(e2.key)) {
              map["${e2.key}_$duplicateSuffix"] = e2.value;
              duplicateSuffix++;
            } else {
              map[e2.key] = e2.value;
            }
          }
        }
      } else {
        map[e.key] = e.value;
      }
    }
    return map;
  }
}
