import 'package:log/log.dart';

class ApiResponse {
  final int? errorCode;
  final dynamic response;

  factory ApiResponse.error(int errorCode) => ApiResponse(errorCode, null);
  factory ApiResponse.valid(dynamic response) => ApiResponse(null, response);
  ApiResponse(this.errorCode, this.response);

  bool get isError => errorCode != null;
  bool get hasResponse => response != null;

  bool get isErrorNotFound => isError && errorCode == 404;

  /// Indica si el servidor ha solicitado que el modelo se borre localmente.
  bool get isErrorRemoveLocal => isError && errorCode == 409;

  @override
  String toString() =>
      'ApiResponse(errorCode: $errorCode, response: $response)';

  String getResponseString() {
    if (isError) return "";
    if (response == null) return "";

    return response.toString();
  }

  List<T> getResponseListDto<T>(T Function(Map<String, dynamic>) fromJson) {
    if (isError) return [];
    if (response == null) return [];

    try {
      return List<T>.from(
        response.map<T>((e) => fromJson(e as Map<String, dynamic>)),
      );
    } catch (e) {
      Log().error("Error al convertir la respuesta a una lista de DTOs: $e");
      throw e;
    }
  }
}
