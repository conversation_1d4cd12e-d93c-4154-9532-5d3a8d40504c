// ignore_for_file: missing_return

import '_imports.dart';

class SyncService<PERSON>ock implements SyncServiceBase {
  @override
  void addSynchronizable(SynchronizableBase synchro) {}

  @override
  Future<void> reset() async {
    Future;
  }

  @override
  Future<void> forceSync() async {
    Future;
  }

  @override
  bool get isSyncInProgress => true;

  @override
  get isUserValid => () => true;

  get onFirstSyncCompleted => null;

  @override
  Stream<Null> get onSyncInProgress => const Stream.empty();
  @override
  Stream<Null> get onSyncEachSynchronizer => const Stream.empty();
  @override
  Stream<Null> get onFirstSyncFinish => const Stream.empty();

  @override
  StreamCustom<OnSyncModelData> get onSyncModels =>
      StreamCustom<OnSyncModelData>();

  @override
  Future<void> start({
    Future<void> Function()? preSync,
    Future<void> Function()? postSync,
  }) async {
    Future;
  }

  @override
  Future<bool> hasInternetConnection() {
    return Future.value(true);
  }

  @override
  Future<void> disable() async {
    Future;
  }

  @override
  Future<void> enable() async {
    Future;
  }

  @override
  bool get isEnabled => true;

  @override
  bool get isAnySyncExecuted => true;

  @override
  bool get isFirstSyncCompleted => true;

  @override
  void addSendDataPendingCheker(Future<bool> Function() func) {}

  @override
  Future<bool> hasSendDataPending() async => false;

  @override
  Future<bool> canSync() async => true;

  @override
  bool isCompletedOnceSynchronizable<S extends SynchronizableBase>() {
    return true;
  }
}
