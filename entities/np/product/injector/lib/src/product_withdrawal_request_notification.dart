import '_imports.dart';

class ProductRequestWithdrawalNotification {
  static final _delayed =
      DelayedRenewableOperationV2(delay: const Duration(seconds: 2));
  static List<ProductWithdrawalRequestModel> _pendings = [];
  static bool _isStarted = false;

  static void start(
    String Function(List<ProductWithdrawalRequestModel> models)
        getNotificationId,
  ) {
    try {
      if (_isStarted) return;
      _isStarted = true;

      final user = injector.get<UserNPBase>();
      final notificationService = injector.get<NotificationServiceBase>();

      productWithdrawalRequestUseCase.onChange.listen((event) async {
        final p = event.after;

        if (event.type == OnChangedType.remove) return;

        List<String> cleanPointGuids = [];

        if (user.isCleanPointManager) {
          final cp = await cleanPointBuildingUseCase.getByPk(p.cleanPointGuid);
          if (cp != null) cleanPointGuids.add(cp.guid);
        }

        if (!p.isNeccesaryNotify(user.role!)) return;

        _pendings.add(event.after);

        _delayed.execute(() async {
          if (isListNullOrEmpty(_pendings)) return;

          if (!notificationService.isEnable) {
            clean();
            return;
          }

          if (_pendings.length == 1)
            await notificationService.show(
              title: p.getNotificationTitle(),
              message: _pendings.first.name,
              id: getNotificationId(_pendings),
            );
          else
            await notificationService.show(
              title: tt(TTProduct
                  .nuevasSolicitudesDeReutilizablesPendientesDeRevision),
              message: _pendings.length.toString() +
                  " " +
                  tt(TTProduct.solicitudesPendientes).toLowerCase(),
              id: getNotificationId(_pendings),
            );

          _pendings.clear();
        });
      });
    } catch (e) {
      Log().error("[ProductWithdrawalRequestNotification] [start] $e");
    }
  }

  static void clean() {
    _pendings.clear();
  }
}
