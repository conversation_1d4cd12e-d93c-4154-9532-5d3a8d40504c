// ignore_for_file: public_member_api_docs, sort_constructors_first

import '../_imports.dart';

class ProductWithdrawalRequestApi {
  Future<List<ProductoWithdrawalSolicitudDto>> getAll({
    required String url,
    required int companyId,
    required String token,
    required String date,
  }) async {
    // TODO: filtrar si es ciudadano o no para llamar a un endpoint diferente.
    // TODO: este endpoint actualmente no existe pero tienen que implementarlo.
    final response = await Api().get(
      url:
          "$url/api/producto_solicitud_retiro/lista?fecha=$date&empresa=$companyId",
      header: Api().getHeaderToken(token),
      isPrinterInLog: false,
    );

    if (response.isError ||
        response.response == null ||
        (response.response is String && isStringNullOrEmpty(response.response)))
      return [];

    return List<ProductoWithdrawalSolicitudDto>.generate(
        response.response.length,
        (i) => ProductoWithdrawalSolicitudDto.fromMap(response.response[i]));
  }

  Future<ProductoWithdrawalSolicitudDto?> getByPk({
    required String url,
    required String token,
    required String guid,
  }) async {
    final response = await Api().get(
      url: "$url/api/producto_solicitud_retiro?guid=$guid",
      header: Api().getHeaderToken(token),
    );
    if (response.isError ||
        response.response == null ||
        (response.response is String && isStringNullOrEmpty(response.response)))
      return null;

    return ProductoWithdrawalSolicitudDto.fromMap(response.response);
  }

  Future<ApiResponse> save({
    required String url,
    required String token,
    required ProductoWithdrawalSolicitudDto dto,
  }) {
    return Api().post(
      url: "$url/api/producto_solicitud_retiro",
      header: Api().getHeaderToken(token),
      body: dto.toMap(),
    );
  }

  Future<ApiResponse> remove({
    required String url,
    required String token,
    required String guid,
  }) {
    return Api().delete(
      url: "$url/api/producto_solicitud_retiro?guid=$guid",
      header: Api().getHeaderToken(token),
    );
  }

  Future<String?> getImage(
    String imageIdMd5,
    String url,
    String token,
  ) async {
    final response = await Api().get(
      url: "$url/api/producto_solicitud_retiro/imagen?imagenMd5Id=$imageIdMd5",
      header: Api().getHeaderToken(token),
      isPrinterInLog: false,
    );

    if (response.isError ||
        response.response == null ||
        (response.response is String && isStringNullOrEmpty(response.response)))
      return null;

    return response.response;
  }
}

class ProductoWithdrawalSolicitudDto {
  final int? empresa;
  final String? personaGuid;
  final String? gestorComentario;
  final int? estado;
  final String? nombre;
  final String? descripcion;
  final int? id;
  final String? guid;
  final String? puntoLimpioGuid;
  final String? fechaModificacion;
  final String? fechaCreacion;
  final bool? borrado;
  final List<String>? imagenesOMd5Ids;
  final bool? ocultadoPorCiudadano;
  final bool? leidoPorGestor;
  final bool? leidoPorCiudadano;
  final int? categoriaId;

  ProductoWithdrawalSolicitudDto({
    required this.empresa,
    required this.gestorComentario,
    required this.estado,
    required this.nombre,
    required this.descripcion,
    required this.personaGuid,
    required this.id,
    required this.guid,
    required this.puntoLimpioGuid,
    required this.fechaModificacion,
    required this.fechaCreacion,
    required this.borrado,
    required this.imagenesOMd5Ids,
    required this.ocultadoPorCiudadano,
    required this.leidoPorGestor,
    required this.leidoPorCiudadano,
    required this.categoriaId,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'empresa': empresa,
      'personaGuid': personaGuid,
      'gestorComentario': gestorComentario,
      'estado': estado,
      'nombre': nombre,
      'descripcion': descripcion,
      'id': id,
      'guid': guid,
      'puntoLimpioGuid': puntoLimpioGuid,
      'fechaModificacion': fechaModificacion,
      'fechaCreacion': fechaCreacion,
      'borrado': borrado,
      'imagenesOMd5Ids': imagenesOMd5Ids,
      'ocultadoPorCiudadano': ocultadoPorCiudadano,
      'leidoPorGestor': leidoPorGestor,
      'leidoPorCiudadano': leidoPorCiudadano,
      'categoriaId': categoriaId,
    };
  }

  factory ProductoWithdrawalSolicitudDto.fromMap(Map<String, dynamic> map) {
    Log().info('-mislogs- ProductoWithdrawalSolicitudDto.fromMap: $map');
    return ProductoWithdrawalSolicitudDto(
      empresa: map['empresa'] != null ? map['empresa'] as int : null,
      personaGuid:
          map['personaGuid'] != null ? map['personaGuid'] as String : null,
      gestorComentario: map['gestorComentario'] != null
          ? map['gestorComentario'] as String
          : null,
      estado: map['estado'] != null ? map['estado'] as int : null,
      nombre: map['nombre'] != null ? map['nombre'] as String : null,
      descripcion:
          map['descripcion'] != null ? map['descripcion'] as String : null,
      id: map['id'] != null ? map['id'] as int : null,
      guid: map['guid'] != null ? map['guid'] as String : null,
      puntoLimpioGuid: map['puntoLimpioGuid'] != null
          ? map['puntoLimpioGuid'] as String
          : null,
      fechaModificacion: map['fechaModificacion'] != null
          ? map['fechaModificacion'] as String
          : null,
      fechaCreacion:
          map['fechaCreacion'] != null ? map['fechaCreacion'] as String : null,
      borrado: map['borrado'] != null ? map['borrado'] as bool : null,
      imagenesOMd5Ids: map['imagenesOMd5Ids'] != null
          ? List<String>.from(map['imagenesOMd5Ids'])
          : [],
      ocultadoPorCiudadano: map['ocultadoPorCiudadano'] != null
          ? map['ocultadoPorCiudadano'] as bool
          : null,
      leidoPorGestor:
          map['leidoPorGestor'] != null ? map['leidoPorGestor'] as bool : null,
      leidoPorCiudadano: map['leidoPorCiudadano'] != null
          ? map['leidoPorCiudadano'] as bool
          : null,
      categoriaId:
          map['categoriaId'] != null ? map['categoriaId'] as int : null,
    );
  }
}
