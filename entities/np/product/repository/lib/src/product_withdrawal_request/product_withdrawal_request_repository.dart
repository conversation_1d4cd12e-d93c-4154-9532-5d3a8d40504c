import '../_imports.dart';

class ProductWithdrawalRequestRepository
    extends RepositoryOnlineWriteOfflineReadAdapter<
        ProductWithdrawalRequestModel,
        String,
        ProductWithdrawalRequestLocalBase,
        ProductWithdrawalRequestRemote>
    implements ProductWithdrawalRequestRepositoryBase {
  ProductWithdrawalRequestRepository({
    required ProductWithdrawalRequestLocalBase local,
    required ConnectionServiceBase connection,
    required ProductWithdrawalRequestRemote remote,
    required Synchronizer<ProductWithdrawalRequestModel, String> synchronizable,
  }) : super(local, remote, connection, synchronizable);

  @override
  Future<String> getImage(String imageOrMd5) async {
    if (!StringUtil.isMd5(imageOrMd5)) return imageOrMd5;

    String? img = await local.getImageId(imageOrMd5);
    if (isStringFilled(img)) return img;

    img = await remote.getImage(imageOrMd5);
    if (isStringFilled(img)) await local.saveImage(imageOrMd5, img!);

    return img ?? "";
  }
}
