import '../_imports.dart';

class ProductWithdrawalRequestRemote
    extends RemoteBase<ProductWithdrawalRequestModel, String> {
  static final _tag = "ProductRequestRemote";
  final _api = ProductWithdrawalRequestApi();

  final TokenBase token;
  final UserBase user;

  ProductWithdrawalRequestRemote({
    required this.token,
    required this.user,
  });

  ProductoWithdrawalSolicitudDto _toDto(ProductWithdrawalRequestModel model) =>
      ProductoWithdrawalSolicitudDto(
        empresa: model.companyId,
        gestorComentario: model.managerComment,
        estado: model.state,
        imagenesOMd5Ids: model.imagesOrIdsMd5,
        nombre: model.name,
        personaGuid: model.citizenGuid,
        id: model.id,
        guid: model.guid,
        fechaModificacion: DateUtil.databaseFormatNP(model.modifyDate),
        fechaCreacion: DateUtil.databaseFormatNP(model.creationDate),
        borrado: model.isRemoved,
        descripcion: model.description,
        puntoLimpioGuid: model.cleanPointGuid,
        ocultadoPorCiudadano: model.isHiddenByCitizen,
        leidoPorCiudadano: model.isReadedByCitizen,
        leidoPorGestor: model.isReadedByManager,
        categoriaId: model.categoryId,
      );

  @override
  Future<List<ReceiveAdapter2<ProductWithdrawalRequestModel>>> getAll(
      String lastSyncDate) async {
    final dtos = await _api.getAll(
      url: await token.getUrl(),
      companyId: user.companyId!,
      token: await token.get(),
      date: lastSyncDate,
    );

    return dtos.map2(
      (e) => ReceiveAdapter2(
        modifyDateBack: e.fechaModificacion!,
        model: e.toModel(),
      ),
    );
  }

  @override
  Future<ProductWithdrawalRequestModel?> getByPk(String pk) async {
    final dto = await _api.getByPk(
      url: await token.getUrl(),
      token: await token.get(),
      guid: pk,
    );

    if (dto == null) return null;

    return dto.toModel();
  }

  @override
  Future<Either<ProductWithdrawalRequestModel?, Failure2<SendErrorType>>> save(
      ProductWithdrawalRequestModel model) async {
    if (model.isRemoved) {
      final response = await _api.remove(
        url: await token.getUrl(),
        token: await token.get(),
        guid: model.guid,
      );

      final error = response.checkErrors();
      if (error != null) return Either.right(error);

      return Either.left(model);
    }

    final response = await _api.save(
      url: await token.getUrl(),
      token: await token.get(),
      dto: _toDto(model),
    );

    final error = response.checkErrors();
    if (error != null) return Either.right(error);

    final newModel =
        ProductoWithdrawalSolicitudDto.fromMap(response.response).toModel();
    return Either.left(newModel);
  }

  Future<String?> getImage(String guid) async =>
      _api.getImage(guid, await token.getUrl(), await token.get());
}

extension ProductoWithdrawalSolicitudDtoExt on ProductoWithdrawalSolicitudDto {
  ProductWithdrawalRequestModel toModel() => ProductWithdrawalRequestModel(
        companyId: empresa!,
        managerComment: gestorComentario ?? "",
        state: estado!,
        imagesOrIdsMd5: imagenesOMd5Ids ?? [],
        name: nombre ?? "",
        // This field was added recently, so it can be null.
        citizenGuid: personaGuid ?? "",
        id: id!,
        cleanPointGuid: puntoLimpioGuid!,
        guid: guid!,
        modifyDate: DateTime.tryParse(fechaModificacion!)!,
        creationDate: DateTime.tryParse(fechaCreacion!)!,
        isRemoved: borrado!,
        isSync: true,
        description: descripcion ?? "",
        isHiddenByCitizen: ocultadoPorCiudadano ?? false,
        isReadedByCitizen: leidoPorCiudadano ?? false,
        isReadedByManager: leidoPorGestor ?? false,
        categoryId: categoriaId!,
      );
}
