import '../_imports.dart';

class ProductWithdrawalRequestModelAdapter
    extends TypeAdapter<ProductWithdrawalRequestModel> {
  @override
  final int typeId = 94;

  @override
  ProductWithdrawalRequestModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProductWithdrawalRequestModel(
      companyId: fields[0] as int,
      citizenGuid: (fields[1] ?? "") as String,
      managerComment: fields[2] as String,
      state: fields[3] as int,
      description: fields[4] as String,
      name: fields[5] as String,
      id: fields[6] as int,
      guid: fields[7] as String,
      modifyDate: fields[8] as DateTime,
      creationDate: fields[9] as DateTime,
      isRemoved: fields[10] as bool,
      isSync: fields[11] as bool,
      cleanPointGuid: fields[12] as String,
      imagesOrIdsMd5: fields[13] as List<String>,
      isHiddenByCitizen: fields[14] as bool,
      isReadedByManager: fields[15] as bool,
      isReadedByCitizen: fields[16] as bool,
      categoryId: fields[17] as int,
    );
  }

  @override
  void write(BinaryWriter writer, ProductWithdrawalRequestModel obj) {
    writer
      ..writeByte(18)
      ..writeByte(0)
      ..write(obj.companyId)
      ..writeByte(1)
      ..write(obj.citizenGuid)
      ..writeByte(2)
      ..write(obj.managerComment)
      ..writeByte(3)
      ..write(obj.state)
      ..writeByte(4)
      ..write(obj.description)
      ..writeByte(5)
      ..write(obj.name)
      ..writeByte(6)
      ..write(obj.id)
      ..writeByte(7)
      ..write(obj.guid)
      ..writeByte(8)
      ..write(obj.modifyDate)
      ..writeByte(9)
      ..write(obj.creationDate)
      ..writeByte(10)
      ..write(obj.isRemoved)
      ..writeByte(11)
      ..write(obj.isSync)
      ..writeByte(12)
      ..write(obj.cleanPointGuid)
      ..writeByte(13)
      ..write(obj.imagesOrIdsMd5)
      ..writeByte(14)
      ..write(obj.isHiddenByCitizen)
      ..writeByte(15)
      ..write(obj.isReadedByManager)
      ..writeByte(16)
      ..write(obj.isReadedByCitizen)
      ..writeByte(17)
      ..write(obj.categoryId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductWithdrawalRequestModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
