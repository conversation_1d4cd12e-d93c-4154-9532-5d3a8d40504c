import '../_imports.dart';

class ProductWithdrawalRequestLocal extends ProductWithdrawalRequestDao
    implements ProductWithdrawalRequestLocalBase {
  late ImageLocal _imageLocal;

  final DataServiceBase dataService;
  final UserBase user;

  ProductWithdrawalRequestLocal(this.dataService, this.user) {
    dataService.addRepository(this);
    _imageLocal = ImageLocal(dataService, user);
  }

  @override
  Future<List<ProductWithdrawalRequestModel>> getNoSync() async {
    return getWhere((m) => !m.isSync && m.companyId == user.companyId);
  }

  @override
  Stream<List<ProductWithdrawalRequestModel>> getAllStream() async* {
    yield (await getAll()).where((m) => m.companyId == user.companyId).toList();
  }

  @override
  Future<String> getImageId(String id) async {
    final m = await _imageLocal.getByPk(id);
    return m?.image ?? "";
  }

  @override
  Future<void> saveImage(String id, String image) => _imageLocal.save(
        ImageModel(
          image: image,
          id: id,
          lastAccessDate: now,
        ),
      );
}
