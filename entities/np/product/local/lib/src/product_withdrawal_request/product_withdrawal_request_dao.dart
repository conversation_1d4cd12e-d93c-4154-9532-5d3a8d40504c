import '../_imports.dart';

class ProductWithdrawalRequestDao extends DaoBase<ProductWithdrawalRequestModel,
    String, ProductWithdrawalRequestModelAdapter> {
  static const dbTableName = "product_withdrawal_request";

  ProductWithdrawalRequestDao()
      : super(
          ProductWithdrawalRequestModelAdapter(),
          isStoreInMemory: false,
          isPhysicalRemoval: true,
        );

  @override
  String get tableName => dbTableName;

  @override
  List<Future<void> Function()> get updateTableScripts => [];
}
