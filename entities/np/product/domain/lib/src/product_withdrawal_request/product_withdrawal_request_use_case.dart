import '../_imports.dart';

class ProductWithdrawalRequestUseCase extends UseCaseBase<
    ProductWithdrawalRequestModel,
    String,
    ProductWithdrawalRequestRepositoryBase> {
  final CleanPointBuildingUseCase cleanPointBuildingUseCase;
  final CleanPointPersonUseCase personUseCase;

  ProductWithdrawalRequestUseCase({
    required ProductWithdrawalRequestRepositoryBase repository,
    required UserProductBase user,
    required this.cleanPointBuildingUseCase,
    required this.personUseCase,
  }) : super(repository, user);

  @override
  bool internalReadGlobal() => true;

  UserProductBase get _u => user as UserProductBase;

  @override
  Future<bool> internalReadModel(ProductWithdrawalRequestModel model) async {
    if (model.companyId != user.companyId) return false;

    if (!ProductRequestUseCase.isValidForCitizen(_u, model.citizenGuid))
      return false;

    if (user.isAdmin) return true;

    if (user.app == SSOApplication.EcoSATPuntoLimpio &&
        !(await ProductRequestUseCase.isUserInTheSameCleanPoint(
            user,
            cleanPointBuildingUseCase,
            model.cleanPointBuildingPk,
            personUseCase))) return false;

    return true;
  }

  Future<String> getImage(String imageOrMd5) => repository.getImage(imageOrMd5);
}

abstract class ProductWithdrawalRequestRepositoryBase
    implements RepositoryBase<ProductWithdrawalRequestModel, String> {
  Future<String> getImage(String imageOrMd5);
}
