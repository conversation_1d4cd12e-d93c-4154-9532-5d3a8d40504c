// ignore_for_file: public_member_api_docs, sort_constructors_first
import '../_imports.dart';

enum ProductWithdrawalRequestState {
  pending(1),
  accepted(2),
  rejected(3),
  canceled(4),
  expired(5),
  ended(6);

  final int id;

  const ProductWithdrawalRequestState(this.id);

  String get tt {
    switch (this) {
      case pending:
        return TTProduct.pendiente.tt;
      case accepted:
        return TTProduct.aceptado.tt;
      case rejected:
        return TTProduct.rechazado.tt;
      case canceled:
        return TTProduct.cancelado.tt;
      case expired:
        return TTProduct.expirado.tt;
      case ended:
        return TTProduct.finalizado.tt;
    }
  }

  static ProductWithdrawalRequestState fromId(int id) {
    return ProductWithdrawalRequestState.values.firstWhere((e) => e.id == id,
        orElse: () => ProductWithdrawalRequestState.pending);
  }
}

class ProductWithdrawalRequestModel implements ModelSync<String> {
  final int companyId;
  final String citizenGuid;
  final String managerComment;
  final int state;
  final int categoryId;
  final String description;
  final String name;
  final int id;
  final String guid;
  final String cleanPointGuid;
  final bool isHiddenByCitizen;
  final bool isReadedByManager;
  final bool isReadedByCitizen;
  DateTime modifyDate;
  DateTime creationDate;
  bool isRemoved;
  bool isSync;
  List<String> imagesOrIdsMd5;

  ProductWithdrawalRequestModel({
    required this.companyId,
    required this.citizenGuid,
    required this.managerComment,
    required this.state,
    required this.categoryId,
    required this.description,
    required this.name,
    required this.id,
    required this.guid,
    required this.modifyDate,
    required this.creationDate,
    required this.isRemoved,
    required this.isSync,
    required this.cleanPointGuid,
    required this.imagesOrIdsMd5,
    required this.isHiddenByCitizen,
    required this.isReadedByManager,
    required this.isReadedByCitizen,
  });

  String get pk => guid;

  String get cleanPointBuildingPk => cleanPointGuid;

  int get productCategoryPk => categoryId;

  late final stateEnum = ProductWithdrawalRequestState.fromId(state);

  bool isNeccesaryNotify(SSORole role) {
    if (isRemoved) return false;

    switch (stateEnum) {
      case ProductWithdrawalRequestState.pending:
        return (role == SSORole.managerCleanPoint || role == SSORole.admin) &&
            !isReadedByManager;
      case ProductWithdrawalRequestState.accepted:
      case ProductWithdrawalRequestState.rejected:
      case ProductWithdrawalRequestState.canceled:
      case ProductWithdrawalRequestState.expired:
      case ProductWithdrawalRequestState.ended:
        return role == SSORole.citizen &&
            !isReadedByCitizen &&
            !isHiddenByCitizen;
    }
  }

  String getNotificationTitle() {
    switch (stateEnum) {
      case ProductWithdrawalRequestState.pending:
        return TTProduct.nuevaSolicitudDeReutilizable.tt;
      case ProductWithdrawalRequestState.accepted:
        return TTProduct.reutilizableAceptado.tt;
      case ProductWithdrawalRequestState.rejected:
        return TTProduct.reutilizableRechazado.tt;
      case ProductWithdrawalRequestState.canceled:
        return TTProduct.solicitudCancelada.tt;
      case ProductWithdrawalRequestState.expired:
        return TTProduct.solicitudExpirada.tt;
      case ProductWithdrawalRequestState.ended:
        return TTProduct.solicitudFinalizada.tt;
    }
  }

  ProductCategoryModel? getCategory(Map<int, ProductCategoryModel> map) =>
      map[productCategoryPk];

  CleanPointBuildingModel? getCleanPointBuilding(
          Map<String, CleanPointBuildingModel> map) =>
      map[cleanPointBuildingPk];

  ProductWithdrawalRequestModel copyWith({
    int? companyId,
    String? citizenGuid,
    String? managerComment,
    int? state,
    String? description,
    String? name,
    int? id,
    String? guid,
    String? cleanPointGuid,
    DateTime? modifyDate,
    DateTime? creationDate,
    bool? isRemoved,
    bool? isSync,
    List<String>? imagesOrIdsMd5,
    bool? isHiddenByCitizen,
    bool? isReadedByManager,
    bool? isReadedByCitizen,
    int? categoryId,
  }) {
    return ProductWithdrawalRequestModel(
      companyId: companyId ?? this.companyId,
      citizenGuid: citizenGuid ?? this.citizenGuid,
      managerComment: managerComment ?? this.managerComment,
      state: state ?? this.state,
      description: description ?? this.description,
      name: name ?? this.name,
      id: id ?? this.id,
      guid: guid ?? this.guid,
      cleanPointGuid: cleanPointGuid ?? this.cleanPointGuid,
      modifyDate: modifyDate ?? this.modifyDate,
      creationDate: creationDate ?? this.creationDate,
      isRemoved: isRemoved ?? this.isRemoved,
      isSync: isSync ?? this.isSync,
      imagesOrIdsMd5: imagesOrIdsMd5 ?? this.imagesOrIdsMd5,
      isHiddenByCitizen: isHiddenByCitizen ?? this.isHiddenByCitizen,
      isReadedByManager: isReadedByManager ?? this.isReadedByManager,
      isReadedByCitizen: isReadedByCitizen ?? this.isReadedByCitizen,
      categoryId: categoryId ?? this.categoryId,
    );
  }
}
