import '../_imports.dart';

class ProductWithdrawalRequestUseCaseValidator
    extends UseCaseValidatorBase<ProductWithdrawalRequestModel> {
  final CleanPointBuildingUseCase cleanPointUseCase;
  final ProductWithdrawalRequestUseCase productWithdrawalRequestUseCase;
  final UserNPBase user;

  ProductWithdrawalRequestUseCaseValidator(
    ProductWithdrawalRequestModel model,
    this.cleanPointUseCase,
    this.productWithdrawalRequestUseCase,
    this.user,
  ) : super(model);

  @override
  Future<Result<String>> validate() async {
    if (model.categoryId <= 0)
      return TTProduct.categoria.tt
          .colon(TTShared.noSeHaAsignadoUnValor.tt)
          .toResult();
    if (model.citizenGuid.isNullOrEmpty)
      return TTShared.ciudadano.tt
          .colon(TTShared.noSeHaAsignadoUnValor.tt)
          .toResult();

    if (user.companyId != model.companyId)
      return Result.error(tt(TTShared.noTienesPermisosParaRealizarEstaAccion));

    final before = await productWithdrawalRequestUseCase.getByPk(model.pk);

    // Solo verificamos las reglas cuando el modelo no es nuevo y el estado ha cambiado.
    if (before?.stateEnum != model.stateEnum) {
      switch (model.stateEnum) {
        case ProductWithdrawalRequestState.pending:
          if (!user.isCitizen)
            return Result.error(
                tt(TTShared.noTienesPermisosParaRealizarEstaAccion));

          if (user.app != SSOApplication.EcoReutiliza)
            return Result.error(
                tt(TTShared.noTienesPermisosParaRealizarEstaAccion));

          final check = <ValueObjectBase>[
            IntGreaterZero(
              model.companyId,
              translate(TTShared.empresa),
            ),
            StringFilled(
              model.description,
              translate(TTShared.descripcion),
            ),
            StringFilled(
              model.name,
              translate(TTShared.nombre),
            ),
            StringFilled(
              model.guid,
              translate(TTShared.identificador),
            ),
          ].validate();
          if (check.isError) return check;

          if (model.imagesOrIdsMd5.isEmpty)
            return Result.error(tt(TTShared.noSeHaSeleccionadoImagen));

          break;
        case ProductWithdrawalRequestState.accepted:
        case ProductWithdrawalRequestState.rejected:
        case ProductWithdrawalRequestState.canceled:
        case ProductWithdrawalRequestState.expired:
        case ProductWithdrawalRequestState.ended:
          if (user.app != SSOApplication.EcoSATPuntoLimpio)
            return Result.error(
                tt(TTShared.noTienesPermisosParaRealizarEstaAccion));

          if (!user.isAdmin && !user.isCleanPointManager)
            return Result.error(
                tt(TTShared.noTienesPermisosParaRealizarEstaAccion));

          // Cuando el administrador cambia el estado, mostramos la solicitud al ciudadano nuevamente.
          if (model.isHiddenByCitizen)
            model = model.copyWith(
              isHiddenByCitizen: false,
            );
          break;
      }
    }

    return Result.valid();
  }
}
