import '../_imports.dart';

class CadastreUseCase
    extends UseCaseBase<CadastreModel, String, CadastreRepositoryBase> {
  CadastreUseCase(CadastreRepositoryBase repository, UserNPBase user)
      : super(repository, user);

  @override
  bool internalReadGlobal() => true;

  Future<List<CadastreModel>?> getByCatastralReference(
      String catastralReference) async {
     return await repository.getByCatastralReference(catastralReference);
  }

  @override
  Future<bool> internalReadModel(CadastreModel model) async => true;
}

abstract class CadastreRepositoryBase
    implements RepositoryBase<CadastreModel, String> {
  Future<List<CadastreModel>?> getByCatastralReference(String catastralReference);
}
