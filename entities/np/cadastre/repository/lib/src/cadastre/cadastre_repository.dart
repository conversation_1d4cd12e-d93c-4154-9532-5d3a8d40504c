import '../_imports.dart';

class CadastreRepository extends RepositoryOnlineFirstAdapter<
    CadastreModel,
    String,
    CadastreLocalBase,
    CadastreRemote> implements CadastreRepositoryBase {
  CadastreRepository({
    required CadastreLocalBase local,
    required CadastreRemote remote,
    required ConnectionServiceBase connection,
    required Synchronizer<CadastreModel, String> synchronizable,
  }) : super(local, remote, connection, synchronizable);

  @override
  Future<List<CadastreModel>?> getByCatastralReference(String catastralReference) async {
    final response = await remote.getByCatastralReference(catastralReference);
    if (response == null) return null;
    return [response];
  }
}
