import '../_imports.dart';

class CadastreApi {
  Future<ApiResponse?> save({
    required String url,
    required String token,
    required CadastreDto dto,
  }) =>
      Api().post(
        url: "$url/api/ecocompostaje/catastro",
        body: dto.toMap(),
        header: Api().getHeaderToken(token),
      );

  Future<List<CadastreDto>?> getAll({
    required String url,
    required String token,
    required String syncDate,
  }) async {
    final response = await (Api().get(
      url: "$url/api/ecocompostaje/catastros?fecha=${syncDate.toDateNP()}",
      header: Api().getHeaderToken(token),
    ));

    if (response.isError || response.response == null) return null;
    return List<CadastreDto>.generate(
      response.response.length,
      (i) => CadastreDto.fromMap(response.response[i])!,
    );
  }

  Future<ApiResponse?> remove({
    required String url,
    required String token,
    required int id,
  }) =>
      Api().delete(
        url: "$url/api/ecocompostaje/catastro?id=$id",
        header: Api().getHeaderToken(token),
      );

  Future<ApiResponse?> getByUuid({
    required String url,
    required String token,
    required String uuid,
  }) =>
      Api().get(
        url: "$url/api/ecocompostaje/catastro?guid=$uuid",
        header: Api().getHeaderToken(token),
      );



  Future<ApiResponse?> getByCatastralReference({
    required String url,
    required String token,
    required String catastralReference,
  }) =>
      Api().get(
        url: "$url/api/referencia/catastral/numero?catastro=$catastralReference",
        header: Api().getHeaderToken(token),
      );
}
