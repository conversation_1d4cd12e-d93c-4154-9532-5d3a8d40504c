import '../_imports.dart';

extension CadastreDtoExtension on CadastreDto {
  CadastreModel toModel() => CadastreModel(
        id: id!,
        holder: titular!,
        cp: codigoPostal!,
        address: direccion!,
        community: comunidadAutonoma!,
        municipality: municipio!,
        reference: referenciaCatastral!,
        isRemoved: borrado ?? false,
        isSync: true,
        modifyDate: ParseUtil.stringToDatetime(fechaModificacion!)!,
        uuid: guid ?? '',
        creationDate: DateTime(0),
      );
}

extension CadastreModelExtension on CadastreModel {
  CadastreDto toDto() => CadastreDto(
        id: id,
        titular: holder,
        codigoPostal: cp,
        direccion: address,
        comunidadAutonoma: community,
        municipio: municipality,
        referenciaCatastral: reference,
        fechaModificacion: DateUtil.databaseFormatNP(modifyDate),
        borrado: isRemoved,
        guid: uuid,
      );
}
