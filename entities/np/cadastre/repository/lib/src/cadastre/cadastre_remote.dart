import '../_imports.dart';

class CadastreRemote implements RemoteBase<CadastreModel, String> {
  final _api = CadastreApi();

  final TokenBase? token;
  final UserBase user;

  CadastreRemote({
    required this.token,
    required this.user,
  });

  @override
  Future<List<ReceiveAdapter2<CadastreModel>>> getAll(String lastSync) async {
    final response = await _api.getAll(
      url: await token!.getUrl(),
      token: await token!.get(),
      syncDate: lastSync,
    );

    if (isListNullOrEmpty(response)) return [];
    return List<CadastreDto>.generate(response!.length, (i) => response[i])
        .map(
          (e) => ReceiveAdapter2(
            modifyDateBack: e.fechaModificacion!,
            model: e.toModel(),
          ),
        )
        .toList();
  }

  @override
  Future<CadastreModel?> getByPk(String pk) async {
    final response = await _api.getByUuid(
      url: await token!.getUrl(),
      token: await token!.get(),
      uuid: pk,
    );

    final error = response.checkErrors();
    if (error != null) return null;

    return CadastreDto.fromMap(response?.response)?.toModel();
  }

  Future<CadastreModel?> getByCatastralReference(String catastralReference) async {
    final response = await _api.getByCatastralReference(
      url: await token!.getUrl(),
      token: await token!.get(),
      catastralReference: catastralReference,
    );

    final error = response.checkErrors();
    if (error != null) return null;

    return CadastreDto.fromMap(response?.response)?.toModel();
  }

  @override
  Future<Either<CadastreModel, Failure2<SendErrorType>>> save(
      CadastreModel model) async {
    if (model.isRemoved) {
      final response = await _api.remove(
        url: await token!.getUrl(),
        token: await token!.get(),
        id: model.id,
      );

      final error = response.checkErrors();
      if (error != null) return Either.right(error);

      return Either.left(model);
    }

    final response = await _api.save(
      url: await token!.getUrl(),
      dto: model.toDto(),
      token: await token!.get(),
    );
    
    final error = response.checkErrors();
    if (error != null) return Either.right(error);

    return Either.left(CadastreDto.fromMap(response?.response)?.toModel());
  }
}
