// @Skip()
// @Timeout(const Duration(minutes: 5))

import 'package:element_repository/src/_imports.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:testing/testing.dart';
import 'package:token_source_np/token_source_np.dart';

void main() {
  group("ElementRemoteSource", () {
    late RemoteSnapshotPaginationHelper<ElementModel> helper;

    final guidMockPref = PreferenceUserMock<String?>(null);
    final paginationIndexpref = PreferenceUserMock<int>(1);
    final paginationCountPref = PreferenceUserMock<int?>(null);
    final lastSyncPref = PreferenceUserMock<String>(MockPrefs.dateSync);
    final u = UserData(
      login: "1234",
      password: "1234",
      id: 597,
      isAdmin: true,
      deviceId: "86731103177433700",
    );

    final server = MockPrefs.server_DEV_NP_Movisat;
    final user = MockPrefs.user_ServDesarrollo_NP;
    final app = SSOApplication.EcoCompostaje;
    final role = SSORole.admin;

    final testing = TestStart(
      server,
      user,
      TokenSourceNP(UserNPMock.test(server, user, app, role, 0)),
    );

    setUpAll(() async {
      await testing.start();
      final _api = ElementApi();
      helper = RemoteSnapshotPaginationHelper<ElementModel>(
        paginationIndex: paginationIndexpref,
        paginationGuid: guidMockPref,
        lastSyncDate: lastSyncPref,
        getTimeFromServer: () => testing.timeServer.get(),
        getNewSnapshot: () async {
          final result = await _api.getSnapshot(
            url: await testing.token.getUrl(),
            token: await testing.token.get(),
            userId: testing.user.id,
            isAdmin: testing.user.isAdmin,
            companyId: testing.user.companyId!,
            syncDate: ParseUtil.stringToDatetime(await (lastSyncPref.get()))!,
          );

          if (result == null) return null;

          return RemoteSnapshotData(result.guid!, result.numeroPaginas!);
        },
        getItems: (uuid, index) async {
          final result = await _api.getBySnapshot(
            url: await testing.token.getUrl(),
            token: await testing.token.get(),
            guidSnapshot: uuid,
            pagination: index,
          );

          return result?.map<ElementModel>((e) => e.toModel()).toList() ?? [];
        },
        paginationMaxCount: paginationCountPref,
      );
    });

    final List<ElementModel> entities = [];
    // La paginación empieza con el índice 1.
    int pagination = 1;
    int? beforeIndex;
    String? uuid;

    Future<void> _run() async {
      do {
        final iteration = await (helper.getNext());
        entities.addAll(iteration);

        // Contamos a partir de la segunda iteración.
        if (pagination > 2) {
          uuid = await guidMockPref.get();
          beforeIndex = await paginationIndexpref.get();
          expect(
            uuid != null && uuid!.isNotEmpty,
            true,
            reason: "El GUID no se ha actualizado: $uuid",
          );
          expect(
            beforeIndex != null && beforeIndex == pagination,
            true,
            reason: "El índice no se ha actualizado: $beforeIndex",
          );
        }

        // Aumentamos la iteracion al inicio, ya que al recuperar valores ya ha modificado el índice al siguiente valor.
        pagination++;

        await helper.finishIteration();
      } while (!helper.isFinished());
      await helper.completed();
    }

    Future<bool> _validateCompleted() async {
      final guidTemp = await guidMockPref.get();
      final paginationTemp = await paginationIndexpref.get();
      if (isStringFilled(guidTemp)) return false;
      if (paginationTemp != 1) return false;
      return true;
    }

    test('Recupera valores', () async {
      await _run();
      expect(await _validateCompleted(), true);
      expect(entities.isEmpty, false);

      final List<ElementModel> removes = entities.where2((m) => m.isRemoved);
      final List<ElementModel> notRemoves =
          entities.where2((m) => !m.isRemoved);

      print(
          "Eliminados: ${removes.length}. No eliminados: ${notRemoves.length}");
    });

    test('La perferencia de GUID se borra al finalizar', () async {
      final value = await guidMockPref.get();
      expect(value == "", true, reason: "El GUID no está vacío: $value");
    });
  });
}
