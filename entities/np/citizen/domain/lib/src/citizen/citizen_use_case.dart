import '../_imports.dart';

class CitizenUseCase
    extends UseCaseBase<CitizenModel, String, CitizenRepositoryBase> {
  CitizenUseCase(CitizenRepositoryBase repository, UserNPBase user)
      : super(repository, user);

  @override
  bool internalReadGlobal() => true;

  @override
  Future<bool> internalReadModel(CitizenModel model) async {
    if (user.role == SSORole.citizen && user.email != model.email) return false;
    return true;
  }

  Future<Result<String>> register(CitizenModel model) async =>
      repository.register(model);

  Future<CitizenModel?> getAdditionalUserData(String email) async {
    final result = await repository.getAdditionalUserData(email);
    if (result?.email != user.email) return null;
    return result;
  }

  Future<Result<String>> associateAddress({
    required int populationNumber,
    required String cadastralReference,
    required String citizenGuid,
  }) async {
    return repository.associateAddress(
      populationNumber: populationNumber,
      cadastralReference: cadastralReference,
      citizenGuid: citizenGuid,
    );
  }
}

abstract class CitizenRepositoryBase
    implements RepositoryBase<CitizenModel, String> {
  Future<Result<String>> register(CitizenModel model);
  Future<CitizenModel?> getAdditionalUserData(String email);
  Future<Result<String>> associateAddress({
    required int populationNumber,
    required String cadastralReference,
    required String citizenGuid,
  });
}
