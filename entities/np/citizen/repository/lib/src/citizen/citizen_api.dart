import '../_imports.dart';

class CitizenApi {
  String _getIncludeParametersBySSOApplication(SSOApplication ssoApplication) {
    final List<String> includeParams = ['direcciones'];

    switch (ssoApplication) {
      case SSOApplication.EcoReutiliza:
      case SSOApplication.EcoSATPuntoLimpio:
        includeParams.add('infoPuntoLimpio');
        break;
      case SSOApplication.EcoIdentificacionIoT:
        includeParams.addAll(['tarjetas', 'idsTarjetas', 'idsCerraduras']);
        break;
      case SSOApplication.EcoCompostaje:
      case SSOApplication.EcoMovil:
      case SSOApplication.EcoCiudadanoNP:
      case SSOApplication.EcoInstalaciones:
      case SSOApplication.Formulasat:
        break;
    }

    return includeParams.join(';');
  }

  Future<List<CiudadanoDto>> getAllSince({
    required String url,
    required int companyId,
    required String token,
    required String since,
    required SSOApplication ssoApplication,
  }) async {
    final includeParams = _getIncludeParametersBySSOApplication(ssoApplication);
    final apiUrl =
        "$url/api/ciudadanos/list?date=$since&enterprise=$companyId&include=$includeParams";

    final response = await Api().get(
      url: apiUrl,
      header: Api().getHeaderToken(token),
      isPrinterInLog: false,
      timeoutSeconds: 60,
    );

    if (response.isError ||
        response.response == null ||
        (response.response is String && isStringNullOrEmpty(response.response)))
      return [];

    return List<CiudadanoDto>.generate(response.response.length,
        (i) => CiudadanoDto.fromJson(response.response[i]));
  }

  Future<CiudadanoDto?> getByPk({
    required String url,
    required String token,
    required String guid,
    required int companyId,
    required SSOApplication ssoApplication,
  }) async {
    final includeParams = _getIncludeParametersBySSOApplication(ssoApplication);
    final apiUrl =
        "$url/api/ciudadano/guid?guid=$guid&enterprise=$companyId&include=$includeParams";

    final response = await Api().get(
      url: apiUrl,
      header: Api().getHeaderToken(token),
    );
    if (response.isError ||
        response.response == null ||
        (response.response is String && isStringNullOrEmpty(response.response)))
      return null;

    return CiudadanoDto.fromJson(response.response);
  }

  // Future<CiudadanoDto?> getWithToken({
  //   required String url,
  //   required String token,
  //   required int companyId,
  // }) async {
  //   final response = await Api().get(
  //     url:
  //         "$url/api/ciudadano/app/check?enterprise=$companyId&include=tarjetas;idsTarjetas;idsCerraduras;infoPuntoLimpio;direcciones",
  //     header: Api().getHeaderToken(token),
  //   );
  //   if (response.isError ||
  //       response.response == null ||
  //       (response.response is String && isStringNullOrEmpty(response.response)))
  //     return null;

  //   return CiudadanoDto.fromJson(response.response);
  // }

  Future<ApiResponse> save({
    required String url,
    required String token,
    required CiudadanoDto dto,
  }) =>
      Api().post(
        url: "$url/api/ciudadano",
        header: Api().getHeaderToken(token),
        body: dto.toJson(),
      );

  Future<ApiResponse> saveAddresses({
    required String url,
    required String token,
    required List<CiudadanoDireccionDto> dtos,
  }) =>
      Api().postString(
          url: "$url/api/ciudadano/direcciones/add/list",
          header: Api().getHeaderToken(token),
          body: jsonEncode(dtos.map2((e) => e.toJson())));

  Future<Either<CiudadanoDto, ApiResponse>> getByEmail({
    required String url,
    required String token,
    required String email,
    required int companyId,
    required SSOApplication ssoApplication,
  }) async {
    final includeParams = _getIncludeParametersBySSOApplication(ssoApplication);
    final apiUrl =
        "$url/api/ciudadano/email?email=$email&enterprise=$companyId&include=$includeParams";

    final response = await Api().get(
      url: apiUrl,
      header: Api().getHeaderToken(token),
    );
    if (response.isError ||
        response.response == null ||
        (response.response is String && isStringNullOrEmpty(response.response)))
      return Either.right(response);

    return Either.left(CiudadanoDto.fromJson(response.response));
  }

  Future<ApiResponse> changeFavorite({
    required String url,
    required String token,
    required String productGuid,
  }) async {
    return Api().post(
      url: "$url/api/ciudadano/producto/favorito",
      header: Api().getHeaderToken(token),
      body: {
        "productoGuid": productGuid,
      },
    );
  }

  Future<ApiResponse> associateAddress({
    required String url,
    required String token,
    required int companyId,
    required int populationNumber,
    required String cadastralReference,
    required String citizenGuid,

  }) async {
    return Api().post(
      url: "$url/api/ciudadano/tarjeta/app",
      header: Api().getHeaderToken(token),
      body: {
        "guidCiudadano": citizenGuid,
        "empresa": companyId,
        "referenciaCatastral": cadastralReference,
        "numeroHabitantes": populationNumber
      },
    );
  }
}

class CiudadanoDto {
  int? id;
  String? guid;
  dynamic idZona;
  String? nombre;
  String? apellidos;
  String? telefono;
  String? numeroCatastro;
  String? direccion;
  String? puerta;
  String? planta;
  String? numero;
  String? municipio;
  String? provincia;
  String? comunidadAutonoma;
  String? distrito;
  String? barrio;
  String? poblacion;
  String? codigoPostal;
  int? estadoValidacion;
  String? correoElectronico;
  String? fechaCreacion;
  String? fechaModificacion;
  String? fechaBaja;
  int? empresa;
  String? nif;
  String? observaciones;
  InfoPuntoLimpio? infoPuntoLimpio;
  List<TarjetaDto>? tarjetas;
  List<int>? idTarjetas;
  List<int>? idCerraduras;
  List<CiudadanoDireccionDto>? direcciones;

  CiudadanoDto({
    required this.id,
    required this.guid,
    required this.idZona,
    required this.nombre,
    required this.apellidos,
    required this.telefono,
    required this.numeroCatastro,
    required this.direccion,
    required this.puerta,
    required this.planta,
    required this.numero,
    required this.municipio,
    required this.provincia,
    required this.comunidadAutonoma,
    required this.distrito,
    required this.barrio,
    required this.poblacion,
    required this.codigoPostal,
    required this.estadoValidacion,
    required this.correoElectronico,
    required this.fechaCreacion,
    required this.fechaModificacion,
    required this.fechaBaja,
    required this.empresa,
    required this.nif,
    required this.observaciones,
    required this.infoPuntoLimpio,
    required this.tarjetas,
    required this.idTarjetas,
    required this.idCerraduras,
    required this.direcciones,
  });

  factory CiudadanoDto.fromJson(Map<String, dynamic> json) => CiudadanoDto(
        id: json["id"],
        guid: json["guid"],
        idZona: json["idZona"],
        nombre: json["nombre"],
        apellidos: json["apellidos"],
        telefono: json["telefono"],
        numeroCatastro: json["numeroCatastro"],
        direccion: json["direccion"],
        puerta: json["puerta"],
        planta: json["planta"],
        numero: json["numero"],
        municipio: json["municipio"],
        provincia: json["provincia"],
        comunidadAutonoma: json["comunidadAutonoma"],
        distrito: json["distrito"],
        barrio: json["barrio"],
        poblacion: json["poblacion"],
        codigoPostal: json["codigoPostal"],
        estadoValidacion: json["estadoValidacion"],
        correoElectronico: json["correoElectronico"],
        fechaCreacion: json["fechaCreacion"],
        fechaModificacion: json["fechaModificacion"],
        fechaBaja: json["fechaBaja"],
        // Utilizado solo en el envío.
        empresa: 0,
        nif: json["nif"],
        observaciones: json["observaciones"],
        infoPuntoLimpio: json["infoPuntoLimpio"] == null
            ? null
            : InfoPuntoLimpio.fromJson(json["infoPuntoLimpio"]),
        tarjetas: json["tarjetas"] == null
            ? <TarjetaDto>[]
            : List<TarjetaDto>.from(
                json["tarjetas"]
                    .map(
                      (x) => TarjetaDto.fromJson(x),
                    )
                    .toList(),
              ),
        idTarjetas: json["idsTarjetas"] == null
            ? <int>[]
            : List<int>.from(json["idsTarjetas"]),
        idCerraduras: json["idsCerraduras"] == null
            ? <int>[]
            : List<int>.from(json["idsCerraduras"]),
        direcciones: json["direcciones"] == null
            ? <CiudadanoDireccionDto>[]
            : List<CiudadanoDireccionDto>.from(
                json["direcciones"]
                    .map(
                      (x) => CiudadanoDireccionDto.fromJson(x),
                    )
                    .toList(),
              ),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "guid": guid,
        "idZona": idZona,
        "nombre": nombre,
        "apellidos": apellidos,
        "telefono": telefono,
        "numeroCatastro": numeroCatastro,
        "direccion": direccion,
        "puerta": puerta,
        "planta": planta,
        "numero": numero,
        "municipio": municipio,
        "provincia": provincia,
        "comunidadAutonoma": comunidadAutonoma,
        "poblacion": poblacion,
        "codigoPostal": codigoPostal,
        "estadoValidacion": estadoValidacion,
        "correoElectronico": correoElectronico,
        "empresa": empresa,
        "nif": nif,
        "observaciones": observaciones,
        "infoPuntoLimpio": infoPuntoLimpio?.toJson(),
      };
}

class InfoPuntoLimpio {
  int? id;
  String? guid;
  int? empresa;
  String? guidCiudadano;
  int? puntos;
  List<String>? favoritos;

  InfoPuntoLimpio({
    required this.id,
    required this.guid,
    required this.empresa,
    required this.guidCiudadano,
    required this.puntos,
    required this.favoritos,
  });

  factory InfoPuntoLimpio.fromJson(Map<String, dynamic> json) =>
      InfoPuntoLimpio(
        id: json["id"],
        guid: json["guid"],
        empresa: json["empresa"],
        guidCiudadano: json["guidCiudadano"],
        puntos: json["puntos"],
        favoritos: json["favoritos"] == null
            ? <String>[]
            : List<String>.from((json["favoritos"]).map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "guid": guid,
        "empresa": empresa,
        "guidCiudadano": guidCiudadano,
        "puntos": puntos,
        "favoritos": favoritos == null
            ? <String>[]
            : List<dynamic>.from(favoritos!.map((x) => x)),
      };
}

class TarjetaDto {
  int? id;
  String? descripcion;
  int? empresa;
  int? idTarjeta;
  int? idZona;
  int? idInterno;
  int? tecnologia;
  String? nombreTecnologia;
  int? uuid;
  int? idUsuario;
  String? nsMovisat;
  String? fechaCreacion;
  String? fechaModificacion;
  String? fechaBaja;

  TarjetaDto({
    required this.id,
    required this.descripcion,
    required this.empresa,
    required this.idTarjeta,
    required this.idZona,
    required this.idInterno,
    required this.tecnologia,
    required this.nombreTecnologia,
    required this.uuid,
    required this.idUsuario,
    required this.nsMovisat,
    required this.fechaCreacion,
    required this.fechaModificacion,
    required this.fechaBaja,
  });

  factory TarjetaDto.fromJson(Map<String, dynamic> json) => TarjetaDto(
        id: json["id"],
        descripcion: json["descripcion"],
        empresa: json["empresa"],
        idTarjeta: json["idTarjeta"],
        idZona: json["idZona"],
        idInterno: json["idInterno"],
        tecnologia: json["tecnologia"],
        nombreTecnologia: json["nombreTecnologia"],
        uuid: json["uuid"],
        idUsuario: json["idUsuario"],
        nsMovisat: json["nsMovisat"],
        fechaCreacion: json["fechaCreacion"],
        fechaModificacion: json["fechaModificacion"],
        fechaBaja: json["fechaBaja"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "descripcion": descripcion,
        "empresa": empresa,
        "idTarjeta": idTarjeta,
        "idZona": idZona,
        "idInterno": idInterno,
        "tecnologia": tecnologia,
        "nombreTecnologia": nombreTecnologia,
        "uuid": uuid,
        "idUsuario": idUsuario,
        "nsMovisat": nsMovisat,
        "fechaCreacion": fechaCreacion,
        "fechaModificacion": fechaModificacion,
        "fechaBaja": fechaBaja,
      };
}

extension CardModelExtension on CardModel {
  TarjetaDto toDto() {
    return TarjetaDto(
      descripcion: description,
      empresa: companyId,
      uuid: uuid,
      idTarjeta: cardId,
      idZona: zoneId,
      idInterno: internalId,
      tecnologia: technology,
      nombreTecnologia: technologyName,
      idUsuario: userId,
      nsMovisat: nsMovisat,
      fechaCreacion: "",
      fechaModificacion: "",
      fechaBaja: "",
      id: id,
    );
  }
}

class CiudadanoDireccionDto {
  int? id;
  int? idCiudadano;
  String? referenciaCatastral;
  String? direccion;
  String? codigoPostal;
  String? poblacion;
  String? municipio;
  String? provincia;
  String? comunidadAutonoma;
  String? distrito;
  String? barrio;
  String? numero;
  String? planta;
  String? puerta;
  bool? direccionPrincipal;
  CiudadanoDireccionCatastroDto? catastro;
  String? numeroAbonado;
  String? fechaCreacion;
  String? fechaModificacion;
  String? fechaBaja;

  CiudadanoDireccionDto({
    required this.id,
    required this.idCiudadano,
    required this.referenciaCatastral,
    required this.direccion,
    required this.codigoPostal,
    required this.poblacion,
    required this.municipio,
    required this.provincia,
    required this.comunidadAutonoma,
    required this.distrito,
    required this.barrio,
    required this.numero,
    required this.planta,
    required this.puerta,
    required this.direccionPrincipal,
    required this.catastro,
    required this.fechaCreacion,
    required this.fechaModificacion,
    required this.fechaBaja,
    required this.numeroAbonado,
  });

  factory CiudadanoDireccionDto.fromJson(Map<String, dynamic> json) =>
      CiudadanoDireccionDto(
        id: json["id"],
        idCiudadano: json["idCiudadano"],
        referenciaCatastral: json["referenciaCatastral"],
        direccion: json["direccion"],
        codigoPostal: json["codigoPostal"],
        poblacion: json["poblacion"],
        municipio: json["municipio"],
        provincia: json["provincia"],
        comunidadAutonoma: json["comunidadAutonoma"],
        distrito: json["distrito"],
        barrio: json["barrio"],
        numero: json["numero"],
        numeroAbonado: json["numeroAbonado"],
        planta: json["planta"],
        puerta: json["puerta"],
        direccionPrincipal: json["direccionPrincipal"],
        catastro: json["catastro"] == null
            ? null
            : CiudadanoDireccionCatastroDto.fromJson(json["catastro"]),
        fechaCreacion:
            json["fechaCreacion"] == null ? null : json["fechaCreacion"],
        fechaModificacion: json["fechaModificacion"] == null
            ? null
            : json["fechaModificacion"],
        fechaBaja: json["fechaBaja"] == null ? null : json["fechaBaja"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "idCiudadano": idCiudadano,
        "referenciaCatastral": referenciaCatastral,
        "direccion": direccion,
        "codigoPostal": codigoPostal,
        "poblacion": poblacion,
        "municipio": municipio,
        "provincia": provincia,
        "comunidadAutonoma": comunidadAutonoma,
        "distrito": distrito,
        "barrio": barrio,
        "numero": numero,
        "numeroAbonado": numeroAbonado,
        "planta": planta,
        "puerta": puerta,
        "direccionPrincipal": direccionPrincipal,
        "catastro": catastro?.toJson(),
        "fechaCreacion": fechaCreacion,
        "fechaModificacion": fechaModificacion,
        "fechaBaja": fechaBaja,
      };
}

class CiudadanoDireccionCatastroDto {
  int? id;
  String? referenciaCatastral;
  String? titular;
  String? direccion;
  String? codigoPostal;
  String? municipio;
  String? provincia;
  String? comunidadAutonoma;
  String? poblacion;
  String? numero;
  String? planta;
  String? puerta;
  String? fechaCreacion;
  String? fechaModificacion;
  String? fechaBaja;

  CiudadanoDireccionCatastroDto({
    this.id,
    this.referenciaCatastral,
    this.titular,
    this.direccion,
    this.codigoPostal,
    this.municipio,
    this.provincia,
    this.comunidadAutonoma,
    this.poblacion,
    this.numero,
    this.planta,
    this.puerta,
    this.fechaCreacion,
    this.fechaModificacion,
    this.fechaBaja,
  });

  factory CiudadanoDireccionCatastroDto.fromJson(Map<String, dynamic> json) =>
      CiudadanoDireccionCatastroDto(
        id: json["id"],
        referenciaCatastral: json["referenciaCatastral"],
        titular: json["titular"],
        direccion: json["direccion"],
        codigoPostal: json["codigoPostal"],
        municipio: json["municipio"],
        provincia: json["provincia"],
        comunidadAutonoma: json["comunidadAutonoma"],
        poblacion: json["poblacion"],
        numero: json["numero"],
        planta: json["planta"],
        puerta: json["puerta"],
        fechaCreacion:
            json["fechaCreacion"] == null ? null : json["fechaCreacion"],
        fechaModificacion: json["fechaModificacion"] == null
            ? null
            : json["fechaModificacion"],
        fechaBaja: json["fechaBaja"] == null ? null : json["fechaBaja"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "referenciaCatastral": referenciaCatastral,
        "titular": titular,
        "direccion": direccion,
        "codigoPostal": codigoPostal,
        "municipio": municipio,
        "provincia": provincia,
        "comunidadAutonoma": comunidadAutonoma,
        "poblacion": poblacion,
        "numero": numero,
        "planta": planta,
        "puerta": puerta,
        "fechaCreacion": fechaCreacion,
        "fechaModificacion": fechaModificacion,
        "fechaBaja": fechaBaja,
      };
}
