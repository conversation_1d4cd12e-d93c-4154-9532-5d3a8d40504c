import '../_imports.dart';

class CitizenRepository extends RepositoryOnlineFirstAdapter<CitizenModel,
    String, CitizenLocalBase, CitizenRemote> implements CitizenRepositoryBase {
  CitizenRepository({
    required CitizenLocalBase local,
    required ConnectionServiceBase connection,
    required CitizenRemote remote,
    required Synchronizer<CitizenModel, String> synchronizable,
  }) : super(local, remote, connection, synchronizable);

  @override
  Future<CitizenModel?> getAdditionalUserData(String email) async {
    CitizenModel? model;

    if (connection.hasInternet) {
      final response = await remote.getByEmail(email);
      if (response.isLeft) {
        model = response.left;
      } else {
        if (response.right!.isErrorNotFound) return null;
      }
    }
    if (model?.email != email) model == null;
    if (model != null) await local.save(model);
    if (model == null) model = await local.getByEmail(email);
    return model;
  }

  @override
  Future<Result<String>> register(CitizenModel model) async {
    if (!connection.hasInternet)
      return TTShared.noTienesConexionInternet.tt.toResult();
    final r = await remote.save(model);
    if (r.isRight)
      return Result.error(r.right?.message ?? TTShared.haOcurridoUnError.tt);
    if (r.left == null) return Result.error(TTShared.haOcurridoUnError.tt);

    await local.save(model);

    return Result.valid();
  }

  @override
  Future<Result<String>> associateAddress({
    required int populationNumber,
    required String cadastralReference,
    required String citizenGuid,
  }) async {
    final response = await remote.associateAddress(
      populationNumber: populationNumber,
      cadastralReference: cadastralReference,
      citizenGuid: citizenGuid,
    );
    return response;
  }
}
