import '../_imports.dart';

class CitizenRemote implements RemoteBase<CitizenModel, String> {
  final _api = CitizenApi();

  final TokenBase token;
  final UserBase user;

  CitizenRemote({
    required this.token,
    required this.user,
  });

  SSOApplication getUserApp() {
    return (user as UserNPBase).app;
  }

  @override
  Future<List<ReceiveAdapter2<CitizenModel>>> getAll(
      String lastSyncDate) async {
    final dtos = await _api.getAllSince(
      url: await token.getUrl(),
      companyId: user.companyId!,
      token: await token.get(),
      since: lastSyncDate,
      ssoApplication: getUserApp(),
    );

    return dtos.map2(
      (e) => ReceiveAdapter2(
        modifyDateBack: e.fechaModificacion!,
        model: e.toModel(user),
      ),
    );
  }

  @override
  Future<CitizenModel?> getByPk(String guid) async {
    final dto = await _api.getByPk(
      url: await token.getUrl(),
      token: await token.get(),
      guid: guid,
      companyId: user.companyId!,
      ssoApplication: getUserApp(),
    );

    if (dto == null) return null;

    return dto.toModel(user);
  }

  @override
  Future<Either<CitizenModel?, Failure2<SendErrorType>>> save(
      CitizenModel model) async {
    if (model.isRemoved)
      throw Exception("No se pueden borrar los registros de ciudadanos");

    final response = await _api.save(
      url: await token.getUrl(),
      token: await token.get(),
      dto: model.toDto(user.companyId!),
    );

    final error = response.checkErrors();
    if (error != null) return Either.right(error);

    if (model.addresses.isFilled) {
      // Recuperamos el ID porque cuando se crea un ciudadano la dirección no tiene el ID real y no se vincula.
      final citizenId = response.response['id'] as int;
      final response2 = await _api.saveAddresses(
        url: await token.getUrl(),
        token: await token.get(),
        dtos: model.addresses.map2(
          (e) => e.toDto(citizenId),
        ),
      );

      final error2 = response2.checkErrors();
      if (error2 != null) return Either.right(error2);

      final response3 = await _api.getByPk(
        url: await token.getUrl(),
        token: await token.get(),
        guid: model.guid,
        companyId: user.companyId!,
        ssoApplication: getUserApp(),
      );

      if (response3 != null) return Either.left(response3.toModel(user));
    }

    return Either.left(CiudadanoDto.fromJson(response.response).toModel(user));
  }

  Future<Either<CitizenModel, ApiResponse>> getByEmail(String mail) async {
    final response = await _api.getByEmail(
      url: await token.getUrl(),
      token: await token.get(),
      email: mail,
      companyId: user.companyId!,
      ssoApplication: getUserApp(),
    );

    if (response.isRight) return Either.right(response.right);
    return Either.left(response.left!.toModel(user));
  }

  Future<Result<String>> associateAddress({
    required int populationNumber,
    required String cadastralReference,
    required String citizenGuid,
  }) async {
    final response = await _api.associateAddress(
      url: await token.getUrl(),
      token: await token.get(),
      companyId: user.companyId!,
      populationNumber: populationNumber,
      cadastralReference: cadastralReference,
      citizenGuid: citizenGuid,
    );

    final error = response.checkErrors();
    if (error != null) return Result.error(error.message);

    return Result.valid();
  }
}

extension CitizenRemoteExtension on CiudadanoDto {
  CitizenModel toModel(UserBase user) {
    bool isRemoved = fechaBaja?.isModelRemoved() ?? false;

    return CitizenModel(
      floor: planta ?? "",
      door: puerta ?? "",
      name: nombre ?? "",
      id: id!,
      address: direccion ?? "",
      autonomousCommunity: comunidadAutonoma ?? "",
      district: distrito ?? "",
      neighborhood: barrio ?? "",
      cadastralNumber: numeroCatastro ?? "",
      email: correoElectronico ?? "",
      surname: apellidos ?? "",
      municipality: municipio ?? "",
      phone: telefono ?? "",
      postalCode: codigoPostal ?? "",
      province: provincia ?? "",
      validationStatus: estadoValidacion ?? 0,
      zoneId: idZona ?? 0,
      modifyDate: ParseUtil.stringToDatetime(fechaModificacion)!,
      creationDate: ParseUtil.stringToDatetime(fechaCreacion)!,
      isRemoved: isRemoved,
      isSync: true,
      cards: tarjetas?.map2(
            (e) => CardModel(
              nsMovisat: e.nsMovisat ?? "",
              technology: e.tecnologia ?? 0,
              technologyName: e.nombreTecnologia ?? "",
              userId: e.idUsuario!,
              zoneId: e.idZona ?? 0,
              internalId: e.idInterno ?? 0,
              cardId: e.idTarjeta ?? 0,
              description: e.descripcion ?? "",
              companyId: e.empresa!,
              uuid: e.uuid ?? 0,
              id: e.id!,
            ),
          ) ??
          [],
      cardIds: idTarjetas ?? [],
      guid: guid ?? "",
      nif: nif ?? "",
      observations: observaciones ?? "",
      number: numero ?? "",
      town: poblacion ?? "",
      cleanPointInfo: infoPuntoLimpio == null
          ? null
          : CitizenCleanPointInfoModel(
              id: infoPuntoLimpio!.id!,
              guid: infoPuntoLimpio!.guid!,
              companyId: infoPuntoLimpio!.empresa!,
              citizenGuid: infoPuntoLimpio!.guidCiudadano ?? "",
              points: infoPuntoLimpio!.puntos ?? 0,
              favorites: infoPuntoLimpio!.favoritos ?? [],
            ),
      addresses: direcciones
              ?.map2(
                (e) => CitizenAddressModel(
                  id: e.id!,
                  citizenId: id!,
                  citizenEmail: correoElectronico ?? "",
                  cadastralReference: e.referenciaCatastral ?? "",
                  address: e.direccion ?? "",
                  municipality: e.municipio ?? "",
                  province: e.provincia ?? "",
                  autonomousCommunity: e.comunidadAutonoma ?? "",
                  district: e.distrito ?? "",
                  neighborhood: e.barrio ?? "",
                  town: e.poblacion ?? "",
                  postalCode: e.codigoPostal ?? "",
                  floor: e.planta ?? "",
                  door: e.puerta ?? "",
                  number: e.numero ?? "",
                  subscriberNumber: e.numeroAbonado,
                  isMainAddress: e.direccionPrincipal ?? false,
                  creationDate: ParseUtil.stringToDatetime(e.fechaCreacion)!,
                  modifyDate: ParseUtil.stringToDatetime(e.fechaModificacion!)!,
                  isRemoved: e.fechaBaja?.isModelRemoved() ?? false,
                  cadastre: e.catastro == null
                      ? null
                      : CitizenAddressCadastreModel(
                          id: e.catastro!.id!,
                          citizenAddressId: e.id!,
                          citizenId: id!,
                          citizenEmail: correoElectronico ?? "",
                          cadastralReference:
                              e.catastro!.referenciaCatastral ?? "",
                          titular: e.catastro!.titular ?? "",
                          address: e.catastro!.direccion ?? "",
                          postalCode: e.catastro!.codigoPostal ?? "",
                          municipality: e.catastro!.municipio ?? "",
                          province: e.catastro!.provincia ?? "",
                          autonomousCommunity:
                              e.catastro!.comunidadAutonoma ?? "",
                          town: e.catastro!.poblacion ?? "",
                          number: e.catastro!.numero ?? "",
                          floor: e.catastro!.planta ?? "",
                          door: e.catastro!.puerta ?? "",
                          creationDate: ParseUtil.stringToDatetime(
                              e.catastro!.fechaCreacion!)!,
                          modifyDate: ParseUtil.stringToDatetime(
                              e.catastro!.fechaModificacion!)!,
                          isRemoved:
                              e.catastro!.fechaBaja?.isModelRemoved() ?? false,
                        ),
                ),
              )
              .where2((m) => [
                    m.address,
                    m.number,
                    m.floor,
                    m.door,
                    m.town,
                    m.postalCode,
                    m.municipality,
                    m.province,
                    m.subscriberNumber,
                    m.autonomousCommunity,
                  ].joinFilled(", ").isFilled) ??
          [],
    );
  }
}

extension CitizenModelExtension on CitizenModel {
  CiudadanoDto toDto(int companyId) {
    return CiudadanoDto(
      planta: floor,
      puerta: door,
      nombre: name,
      id: id,
      direccion: address,
      comunidadAutonoma: autonomousCommunity,
      numeroCatastro: cadastralNumber,
      correoElectronico: email,
      apellidos: surname,
      municipio: municipality,
      barrio: neighborhood,
      distrito: district,
      telefono: phone,
      codigoPostal: postalCode,
      provincia: province,
      estadoValidacion: validationStatus,
      idZona: zoneId,
      fechaModificacion: DateUtil.databaseFormatNP(modifyDate)!,
      fechaCreacion: DateUtil.databaseFormatNP(creationDate)!,
      fechaBaja: isRemoved ? DateUtil.databaseFormatNP(now) : null,
      idTarjetas: cardIds,
      tarjetas: cards.map2((e) => e.toDto()),
      idCerraduras: [],
      guid: guid,
      nif: nif,
      observaciones: observations,
      numero: number,
      poblacion: town,
      empresa: companyId,
      // We don't send this object, because it's only for read.
      infoPuntoLimpio: cleanPointInfo == null
          ? null
          : InfoPuntoLimpio(
              id: cleanPointInfo!.id,
              guid: cleanPointInfo!.guid,
              empresa: cleanPointInfo!.companyId,
              guidCiudadano: cleanPointInfo!.citizenGuid,
              puntos: cleanPointInfo!.points,
              favoritos: cleanPointInfo!.favorites,
            ),
      // We don't send this object, this is setted in other endpoint.
      direcciones: [],
    );
  }
}

extension CitizenAddressModelExtRepo on CitizenAddressModel {
  CiudadanoDireccionDto toDto(int citizenId) {
    return CiudadanoDireccionDto(
      planta: floor,
      puerta: door,
      id: id,
      direccion: address,
      comunidadAutonoma: autonomousCommunity,
      distrito: district,
      barrio: neighborhood,
      municipio: municipality,
      codigoPostal: postalCode,
      provincia: province,
      fechaModificacion: DateUtil.databaseFormatNP(modifyDate)!,
      fechaCreacion: DateUtil.databaseFormatNP(creationDate)!,
      fechaBaja: isRemoved ? DateUtil.databaseFormatNP(now) : null,
      numero: number,
      poblacion: town,
      catastro: cadastre?.toDto(),
      direccionPrincipal: isMainAddress,
      idCiudadano: citizenId,
      referenciaCatastral: cadastralReference,
      numeroAbonado: subscriberNumber,
    );
  }
}

extension CitizenAddressCadastreModelExtRepo on CitizenAddressCadastreModel {
  CiudadanoDireccionCatastroDto toDto() {
    return CiudadanoDireccionCatastroDto(
      planta: floor,
      puerta: door,
      id: id,
      direccion: address,
      comunidadAutonoma: autonomousCommunity,
      municipio: municipality,
      codigoPostal: postalCode,
      provincia: province,
      fechaModificacion: DateUtil.databaseFormatNP(modifyDate)!,
      fechaCreacion: DateUtil.databaseFormatNP(creationDate)!,
      fechaBaja: isRemoved ? DateUtil.databaseFormatNP(now) : null,
      numero: number,
      poblacion: town,
      referenciaCatastral: cadastralReference,
    );
  }
}
