import '../_imports.dart';

class SSORemote {
  final _userCreateApi = UserCreateApi();
  final _userGrantAccessApi = UserGrantAccessApi();
  final _userPasswordRecover1Api = UserPasswordRecover1Api();
  final _userPasswordRecover2Api = UserPasswordRecover2Api();
  final _userPasswordRecover3OrModifyApi = UserPasswordRecover3OrModifyApi();
  final _userRemoveApi = UserRemoveApi();
  final _userModifyApi = UserModifyApi();
  final _companiesGetApi = CompaniesGetApi();
  final _applicationByCompanyGetApi = ApplicationByCompanyGetApi();
  final _companyByApplicationGetApi = CompaniesByApplicationGetApi();
  final _municipalityGetApi = MunicipalityGetApi();
  final _municipalityAppGetApi = MunicipalityAppGetApi();
  final _municipalityPointGetApi = MunicipalityPointGetApi();
  final _getImageCompany = CompanyImageGetApi();
  final _getImageApplication = ApplicationImageGetApi();
  final _getCompanyByIdAndApplicationGetApi = CompanyByIdAndApplicationGetApi();

  final TokenBase token;
  final UserNPBase user;

  SSORemote(this.token, this.user);

  Future<Result<String>> insert({
    required String login,
    required String password,
    required String name,
    required String lastName,
    required LanguageTypeSSO languageType,
    required int companyId,
    required int applicationId,
    required int roleId,
  }) async {
    final result = await _userCreateApi.execute(
      url: await (token.getUrl()),
      login: login,
      passwordSha256: password,
      name: name,
      lastName: lastName,
      languageId: languageType.id,
      token: await (token.get()),
      companyId: companyId,
    );

    if (result.isError) return result;

    return giveAccess(
      login: login,
      companyIdInSSO: companyId,
      applicationId: applicationId,
      roleId: roleId,
    );
  }

  Future<Result<String>> giveAccess({
    required String login,
    required int companyIdInSSO,
    required int applicationId,
    required int roleId,
  }) async =>
      _userGrantAccessApi.execute(
        url: await token.getUrl(),
        login: login,
        companyId: companyIdInSSO,
        applicationId: applicationId,
        roleId: roleId,
        token: await token.get(),
      );

  Future<Result<String>> recoverPasswordStep1(String login) async =>
      _userPasswordRecover1Api.execute(
        url: await token.getUrl(),
        login: login,
        token: await token.get(),
      );

  Future<Result<String>> recoverPasswordStep2(String login, int code) async =>
      _userPasswordRecover2Api.execute(
        url: await token.getUrl(),
        login: login,
        token: await token.get(),
        code: code,
      );

  Future<Result<String>> recoverPasswordStep3(String login, String password) =>
      modifyPassword(login, password);

  Future<Result<String>> modifyPassword(String login, String password) async =>
      Result.fromBool(
        await _userPasswordRecover3OrModifyApi.execute(
          url: await token.getUrl(),
          token: await token.get(),
          login: login,
          newPassword: password,
        ),
      );

  Future<Result<String>> update({
    required String? name,
    required String? surname,
    required String? image,
    required String? password,
    required String? email,
  }) async {
    final result = await _userModifyApi.execute(
      url: await token.getUrl(),
      token: await token.get(),
      email: email ?? "",
      name: name ?? "",
      surname: surname ?? "",
      password: "",
      image: image ?? "",
    );
    if (isTrue(result)) return Result.valid();
    return Result.error(tt(TTShared.haOcurridoUnError));
  }

  Future<Result<String>> remove(int id) async {
    final result = await _userRemoveApi.execute(
      url: await (token.getUrl()),
      token: await (token.get()),
      userId: id,
    );
    if (result) return Result.valid();
    return Result.error(tt(TTShared.haOcurridoUnError));
  }

  Future<List<CompanyModel>> getCompanies() async {
    final result = await _companiesGetApi.execute(
      url: await token.getUrl(),
      token: await token.get(),
    );
    return result.map((e) => e.toModel()).toList();
  }

  Future<CompanyAppModel?> getCompanyAppById(int companyIdInSSO) async {
    final result = await _getCompanyByIdAndApplicationGetApi.execute(
      url: await token.getUrl(),
      token: await token.get(),
      companyIdInSSO: companyIdInSSO,
      appId: user.app.id,
    );

    if (result == null) return null;

    return CompanyAppModel(
      creationDate: ParseUtil.stringToDatetime(result.fechaAlta) ?? now,
      modifyDate: ParseUtil.stringToDatetime(result.fechaModificacion) ?? now,
      removeDate: ParseUtil.stringToDatetime(result.fechaBaja),
      id: result.id ?? 0,
      appId: result.aplicacionId ?? 0,
      companyIdInSSO: result.empresaId ?? 0,
      urlApi: result.urlApi ?? "",
      urlApiIa: result.urlApiIa ?? "",
      urlApiEcoSat: result.urlApiEcoSat ?? "",
      urlWeb: result.urlWeb ?? "",
    );
  }

  Future<bool> hasCompanyTheApp(int companyInSSOId, SSOApplication app) async {
    final result = await _applicationByCompanyGetApi.execute(
        url: await token.getUrl(),
        token: await token.get(),
        companyIdInSSO: companyInSSOId);
    return result.any((a) => a.aplicacionId == app.id);
  }

  Future<bool> hasAppTheCompany(int companyInSSOId, SSOApplication app) async {
    final result = await _companyByApplicationGetApi.execute(
        url: await token.getUrl(),
        token: await token.get(),
        companyIdInSSO: companyInSSOId);
    return result.any((a) => a.id == app.id);
  }

  Future<List<MunicipalityModel>> getMunicipalities() async {
    final dtos = await _municipalityGetApi.execute(
      url: await token.getUrl(),
      token: await token.get(),
    );

    return dtos.map2((e) => e.toModel());
  }

  Future<List<MunicipalityApp>> getMunicipalitiesApp() async {
    final dtos = await _municipalityAppGetApi.execute(
      url: await token.getUrl(),
      token: await token.get(),
      appId: user.app.id,
    );

    return dtos.map2((e) => e.toModel());
  }

  Future<List<LatLong>> getMunicipalityPointByPk(int companyIdSSO) async {
    final dtos = await _municipalityPointGetApi.execute(
      url: await token.getUrl(),
      token: await token.get(),
      companyIdSSO: companyIdSSO,
    );

    if (dtos.isEmpty) return [];

    List<LatLong> result = [];
    for (var dto in dtos) {
      if (dto.lat == null || dto.lng == null) continue;
      if (dto.lat == 0 && dto.lng == 0) continue;

      result.add(LatLong(dto.lat!, dto.lng!));
    }

    return result;
  }

  Future<String> getImageByMd5Company(String md5) async =>
      _getImageCompany.execute(
        url: await token.getUrl(),
        token: await token.get(),
        md5: md5,
      );

  Future<String> getImageByMd5Application(String md5) async =>
      _getImageApplication.execute(
        url: await token.getUrl(),
        token: await token.get(),
        md5: md5,
      );
}
