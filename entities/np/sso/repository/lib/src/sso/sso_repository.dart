import '../_imports.dart';

class SSORepository implements SSORepositoryBase {
  final SSORemote remote;
  final SSOLocalBase local;
  final UserSSOLocalBase? userSSOLocal;
  final ConnectionServiceBase connection;
  final Synchronizer<UserSSOModel, UserSSOPK>? userSSOSync;
  final PreferenceGlobalBase<String>? companyApp;
  final MunicipalityPointLocalBase? municipalityPointsLocal;

  SSORepository(
    this.remote,
    this.local,
    this.connection,
    this.userSSOSync,
    this.userSSOLocal,
    this.companyApp,
    this.municipalityPointsLocal,
  );

  @override
  Future<Result<String>> recoverPasswordStep1(String login) async {
    if (!connection.hasInternet)
      return Result.error(tt(TTShared.noTienesConexionInternet));

    return remote.recoverPasswordStep1(login);
  }

  @override
  Future<Result<String>> recoverPasswordStep2(String login, int code) async {
    if (!connection.hasInternet)
      return Result.error(tt(TTShared.noTienesConexionInternet));
    return remote.recoverPasswordStep2(login, code);
  }

  @override
  Future<Result<String>> recoverPasswordStep3(
      String login, String password) async {
    if (!connection.hasInternet)
      return Result.error(tt(TTShared.noTienesConexionInternet));
    return remote.recoverPasswordStep3(login, password);
  }

  @override
  Future<Result<String>> modifyPassword(String login, String password) async {
    if (!connection.hasInternet)
      return Result.error(tt(TTShared.noTienesConexionInternet));
    return remote.modifyPassword(login, password);
  }

  @override
  Future<Result<String>> insert({
    String? login,
    String? password,
    String? name,
    String? lastName,
    LanguageTypeSSO? languageType,
    int? companyId,
    int? applicationId,
    int? roleId,
  }) async {
    if (!connection.hasInternet)
      return Result.error(tt(TTShared.noTienesConexionInternet));

    final result = await remote.insert(
      login: login!,
      password: password!,
      name: name!,
      lastName: lastName!,
      languageType: languageType!,
      companyId: companyId!,
      applicationId: applicationId!,
      roleId: roleId!,
    );

    userSSOSync?.syncLoopBase.restart();
    await userSSOSync?.synchronize();

    return result;
  }

  @override
  Future<Result<String>> giveAccess(
      {String? login,
      int? companyIdInSSO,
      int? applicationId,
      int? roleId}) async {
    if (!connection.hasInternet)
      return Result.error(tt(TTShared.noTienesConexionInternet));

    final result = await remote.giveAccess(
      login: login!,
      companyIdInSSO: companyIdInSSO!,
      applicationId: applicationId!,
      roleId: roleId!,
    );

    await userSSOSync?.synchronize();
    return result;
  }

  @override
  Future<Result<String>> update({
    String? name,
    String? surname,
    String? image,
    String? password,
    String? email,
  }) async {
    if (!connection.hasInternet)
      return Result.error(tt(TTShared.noTienesConexionInternet));
    final result = await remote.update(
      name: name ?? "",
      surname: surname ?? "",
      image: image ?? "",
      password: password,
      email: email ?? "",
    );
    if (result.isValid) {
      await userSSOSync?.synchronize();
    }
    return result;
  }

  @override
  Future<Result<String>> remove(int id) async {
    if (!connection.hasInternet)
      return Result.error(tt(TTShared.noTienesConexionInternet));
    final result = await remote.remove(id);
    if (result.isValid && userSSOLocal != null) {
      final user = await userSSOLocal!.getById(id);
      if (user != null) {
        user.isRemoved = true;
        user.modifyDate = DateTime.now();
        user.isSync = true;
        await userSSOLocal!.save(user);
      }
    }
    return result;
  }

  @override
  Future<List<CompanyModel>> getCompanies() async {
    if (!connection.hasInternet) return [];
    return remote.getCompanies();
  }

  @override
  Future<CompanyAppModel?> getCompanyAppById(int companyIdInSSO) async {
    if (!connection.hasInternet) return null;
    return remote.getCompanyAppById(companyIdInSSO);
  }

  @override
  Future<bool> hasCompanyTheApp(int companyInSSOId, SSOApplication app) async {
    if (companyApp == null) return false;

    final value = await companyApp!.get();
    if (value == "${companyInSSOId}-${app.id}") return true;

    if (!connection.hasInternet) return false;

    final result = await remote.hasCompanyTheApp(companyInSSOId, app);
    if (result) await companyApp!.set("${companyInSSOId}-${app.id}");

    return result;
  }

  @override
  Future<bool> hasAppTheCompany(int companyInSSOId, SSOApplication app) async {
    if (companyApp == null) return false;

    final value = await companyApp!.get();
    if (value == "${companyInSSOId}-${app.id}") return true;

    if (!connection.hasInternet) return false;

    final result = await remote.hasAppTheCompany(companyInSSOId, app);
    if (result) await companyApp!.set("${companyInSSOId}-${app.id}");

    return result;
  }

  @override
  Future<List<MunicipalityModel>> getMunicipalities() async {
    if (!connection.hasInternet) return [];
    return remote.getMunicipalities();
  }

  @override
  Future<MunicipalityPointModel?> getMunicipalityPointByPk(
      int companyIdSSO) async {
    if (municipalityPointsLocal == null) return null;

    List<LatLong> points = [];

    if (connection.hasInternet)
      points = await remote.getMunicipalityPointByPk(companyIdSSO);

    if (points.isNotEmpty) {
      final m = MunicipalityPointModel(
        companyIdSSO: companyIdSSO,
        points: points,
      );
      await municipalityPointsLocal!.save(m);
      return m;
    }

    return await municipalityPointsLocal!.getByPk(companyIdSSO);
  }

  @override
  Future<List<MunicipalityApp>> getMunicipalitiesApp() async {
    if (!connection.hasInternet) return [];
    return remote.getMunicipalitiesApp();
  }

  @override
  Future<String> getImageByMd5Company(String md5) async {
    String image = await local.getImageByMd5Company(md5);

    if (image.isEmpty && connection.hasInternet) {
      image = await remote.getImageByMd5Company(md5);
      if (image.isFilled) await local.saveImageCompany(md5, image);
    }

    return image;
  }

  @override
  Future<String> getImageByMd5Application(String md5) async {
    String image = await local.getImageByMd5Application(md5);

    if (image.isEmpty && connection.hasInternet) {
      image = await remote.getImageByMd5Application(md5);
      if (image.isFilled) await local.saveImageApplication(md5, image);
    }

    return image;
  }

  @override
  get companyId => companyId;
}
