import '_imports.dart';

extension MunicipalityDtoExtension on MunicipioDto {
  MunicipalityModel toModel() => MunicipalityModel(
        id: id!,
        name: companyName ?? "",
        availableAppIds: isAvaibleAppIds ?? [],
        notAvailableAppIds: notAvailableAppIds ?? [],
        companyIdSSO: companyIdSso!,
        isVisibleForCitizen: isVisibleForUser ?? false,
        points: points
                ?.map2((e) => LatLong(e.lat ?? 0, e.lng ?? 0))
                .where2((m) => m.isValid()) ??
            [],
        image: companyImage ?? "",
      );
}

extension MunicipalityAppDtoExtension on MunicipioAppDto {
  MunicipalityApp toModel() => MunicipalityApp(
        applicationId: idAplicacion ?? 0,
        companyIdSSO: idEmpresa ?? 0,
        municipalityId: idMunicipio ?? 0,
        id: id!,
        company: empresa == null
            ? null
            : MunicipalityAppCompany(
                id: empresa?.id ?? 0,
                name: empresa?.nombre ?? "",
                managementId: empresa?.idGestion ?? 0,
              ),
        municipality: municipio == null
            ? null
            : MunicipalityAppMunicipality(
                id: municipio?.id ?? 0,
                municipality: municipio?.municipio ?? "",
                province: municipio?.provincia ?? "",
                autonomousCommunity: municipio?.comunidadAutonoma ?? "",
                country: municipio?.pais ?? "",
              ),
      );
}

extension UsuarioExtension on Usuario {
  UserModel toModel() => UserModel(
        id: id!,
        name: nombre ?? '',
        nameShort: nombreCorto ?? '',
        surname: apellidos ?? '',
        email: email ?? '',
        isActive: activo!,
        imageMd5: imagenMD5 ?? "",
        registrationDate: fechaAlta ?? '',
        modifyDate: fechaModificacion!,
        deregistrationDate: fechaBaja ?? "",
        language: idioma!.toModel(),
        isInternal: interno!,
      );
}

extension IdiomaExtension on Idioma {
  LanguageModel toModel() => LanguageModel(
        id: id!,
        name: nombre ?? '',
        registrationDate: fechaAlta ?? '',
        modifyDate: fechaModificacion!,
        deregistrationDate: fechaBaja ?? "",
        code: codigo ?? "",
      );
}

extension AccesosExtension on Accesos {
  AccessModel toModel() => AccessModel(
        urlApi: urlApi ?? "",
        urlWeb: urlWeb ?? "",
        urlApiEcosat: urlApiEcoSAT ?? "",
        application: aplicacion?.toModel(),
        company: empresa!.toModel(),
        role: rol?.toModel(),
        isVirtual: this.virtual ?? false,
      );

  bool isValid() {
    if (urlApi.isNullOrEmpty) return false;
    if (rol == null) return false;
    if (aplicacion == null) return false;
    if (empresa == null) return false;
    return true;
  }
}

extension AplicacionExtension on Aplicacion {
  ApplicationModel toModel() => ApplicationModel(
        id: id!,
        name: nombre ?? '',
        imageMd5: imagenMD5 ?? "",
        registrationDate: fechaAlta ?? '',
        modifyDate: fechaModificacion!,
        deregistrationDate: fechaBaja ?? "",
      );
}

extension EmpresaExtension on Empresa {
  CompanyModel toModel() => CompanyModel(
        idInSSO: id!,
        name: nombre ?? '',
        imageMd5: imagenMD5 ?? "",
        registrationDate: fechaAlta ?? '',
        modifyDate: fechaModificacion!,
        deregistrationDate: fechaBaja ?? "",
        cartography: cartografia ?? "",
        idInNP: idGestion ?? 0,
      );
}

extension RolExtension on Rol {
  RoleModel toModel() => RoleModel(
        id: id!,
        name: nombre ?? '',
        registrationDate: fechaAlta ?? '',
        modifyDate: fechaModificacion!,
        deregistrationDate: fechaBaja ?? "",
      );
}
