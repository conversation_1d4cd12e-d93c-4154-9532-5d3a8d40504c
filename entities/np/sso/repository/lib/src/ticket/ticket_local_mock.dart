import '../_imports.dart';

class TicketLocalMock implements TicketLocalBase {
  Future<TicketModel?> get() async => null;
  Future<void> deleteAll() async => null;
  Future<bool> insert(TicketModel? model) async => true;
  Future<void> removeAll() async => null;
}

class SSOLocalMock implements SSOLocalBase {
  @override
  Future<String> getImageByMd5Application(String md5) async => "";

  @override
  Future<String> getImageByMd5Company(String md5) async => "";

  @override
  Future<void> saveImageApplication(String md5, String image) async => null;

  @override
  Future<void> saveImageCompany(String md5, String image) async => null;
}
