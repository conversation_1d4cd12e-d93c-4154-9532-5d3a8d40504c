import '../_imports.dart';

class TicketRemoteSource implements TicketRemoteSourceBase {
  static const _tag = "TicketRemoteSource";

  final _ticketGetApi = TicketGetApi();
  final _userLoginApi = UserLoginApi();
  final _ticketGenerateApi = TicketGenerateApi();
  final _userLogoutApi = UserLogoutApi();

  final TokenBase token;
  final SSOApplication application;
  final ConnectionServiceBase connection;
  final List<SSORole> unallowedRoles;

  TicketRemoteSource(
    this.token,
    this.application,
    this.connection,
    this.unallowedRoles,
  );

  @override
  Future<TicketModel?> getById(String id) async {
    final response = await _ticketGetApi.execute(
      url: await token.getUrl(),
      ticketId: id,
      token: await token.get(),
    );

    if (response == null) return null;

    return TicketModel(
      id: response.id!,
      urlApi: response.urlApi!,
      urlApiEcosat: response.urlApiEcosat ?? '',
      user: response.usuario?.toModel(),
      company: response.empresa?.toModel(),
      application: response.aplicacion?.toModel(),
      role: response.rol?.toModel(),
      creationDate: now,
    );
  }

  @override
  Future<Either<TicketGetResult, Failure<TicketLoginError>>> getByUser({
    required String email,
    required String password,
    required int applicationId,
    required int? companyId,
    required int? roleId,
  }) async {
    final result = await _userLoginApi.execute(
      url: await token.getUrl(),
      login: email,
      passwordSha256OrMd5: password,
      token: await token.get(),
    );

    if (result.isRight) {
      switch (result.right!.code) {
        case UserLoginApiError.userNotVerified:
          return Either.right(
            Failure(
              TicketLoginError.userNotVerified,
              tt(TTSSO
                  .elUsuarioNoEstaActivoRevisaTuCorreoElectronicoParaActivarlo),
            ),
          );
        case UserLoginApiError.emailOrPassNotValid:
          return Either.right(
            Failure(
              TicketLoginError.emailOrPassNotValid,
              result.right!.message.isFilled
                  ? result.right!.message
                  : tt(TTShared.losDatosDeAccesoNoExistenONoSonValidos),
            ),
          );
        case UserLoginApiError.generic:
          if (!(await connection.forceCheckInternetState()))
            return Either.right(
              Failure(
                TicketLoginError.generic,
                tt(TTShared.noTienesConexionInternet),
              ),
            );
          return Either.right(Failure.code(TicketLoginError.generic));
        case UserLoginApiError.userRemoved:
          return Either.right(
            Failure(
              TicketLoginError.userRemoved,
              tt(TTSSO.noTienesAccesoAEstaAplicacion),
            ),
          );
        case UserLoginApiError.passNotValid:
          return Either.right(
            Failure(
              TicketLoginError.passNotValid,
              result.right!.hasMessage
                  ? result.right!.message
                  : tt(TTSSO.laContraseniaNoEsValida),
            ),
          );
        case UserLoginApiError.userBlocked:
          return Either.right(
            Failure(
              TicketLoginError.userBlocked,
              result.right!.hasMessage
                  ? result.right!.message
                  : tt(TTSSO.usuarioBloqueado),
            ),
          );
      }
    }

    final r = result.left!;

    // Comprobamos si ha sido borrado.
    if (isStringFilled(r.fechaBaja)) {
      final removeDate = DateTime.tryParse(r.fechaBaja!);
      if (removeDate != null && removeDate.year > 2000)
        return Either.right(
          Failure(
            TicketLoginError.userRemoved,
            tt(TTSSO.noTienesAccesoAEstaAplicacion),
          ),
        );
    }

    // Comprobamos si el usuario ha verificado el correo.
    if (isFalse(r.activo))
      return Either.right(
        Failure(
          TicketLoginError.userNotVerified,
          tt(TTSSO.elUsuarioNoEstaActivo),
        ),
      );

    // Comprobamos si el usuario tiene acceso a la aplicación.
    // Si se indica empresa, filtrará por ella.
    List<Accesos> access = r.accesos.where2(
      (a) {
        if (!a.isValid()) return false;
        if (a.aplicacion?.id != applicationId) return false;

        // Puede tener varios accesos de la misma aplicación con diferentes empresas.
        if (!isNull(companyId) && a.empresa?.id != companyId) return false;

        // Puede tener varios accesos en la misma empresa con diferentes roles.
        // Esto se hace cuando vuelve a logear con un rol seleccionado.
        if (!isNull(roleId) && a.rol?.id != roleId) return false;

        // Bloqueamos el acceso si el rol no está permitido.
        if (unallowedRoles.any((r) => r.id == (a.rol?.id ?? -1))) return false;

        return true;
      },
    );

    // Eliminamos el acceso virtual (principalmente ciudadano) si no están permitidos.
    if (!application.hasVirtualAccess)
      access.removeWhere((a) => isTrue(a.virtual));

    if (isListNullOrEmpty(access))
      return Either.right(
        Failure(
          TicketLoginError.noAppAccess,
          tt(TTSSO.noTienesAccesoAEstaAplicacion),
        ),
      );

    // Si hay más de 1 acceso, deberá seleccionarlo para poder logear.
    if (!access.isOneOrLess) {
      return Either.left(
        TicketGetResult(
          null,
          false,
          access.map2((e) => e.toModel()),
        ),
      );
    }

    bool isRequiredDoubleFactor = false;

    // Verificación de doble factor.
    if (isTrue(r.dobleFactor!)) {
      final lastLoginDate = DateTime.tryParse(r.fechaDF!)?.toLocal();
      // Comprobamos el año porque el servidor asigna fechas por defecto con año 0001.
      if (lastLoginDate != null) {
        if (now
            .subtract(EnvironmentDebug().doubleFactorDuration ??
                const Duration(days: 1))
            .isAfter(lastLoginDate)) {
          final isSent = await DoubleFactorGenerateApi().execute(
            email: email,
            url: await token.getUrl(),
            token: await token.get(),
          );

          // Si no devuelve true, ignoramos la validación y permitirmos continuar.
          if (isTrue(isSent)) {
            isRequiredDoubleFactor = true;
          } else {
            Log().info(
                "[$_tag] No se ha conseguido generar el código de doble factor");
          }
        }
      }
    }

    // Generamos el ticket ID.
    String? ticketId;

    try {
      ticketId = await _ticketGenerateApi.execute(
        url: await token.getUrl(),
        userId: r.id!,
        companyId: access.first.empresa!.id!,
        appId: access.first.aplicacion!.id!,
        rolId: access.first.rol!.id!,
        urlApi: access.first.urlApi!,
        urlApiEcosat: access.first.urlApiEcoSAT ?? "",
        token: await token.get(),
      );
    } catch (e) {
      Log().error("[$_tag] Error al validar el acceso: $e");
    }

    if (isStringNullOrEmpty(ticketId))
      return Either.right(
        Failure(
          TicketLoginError.generic,
          tt(TTSSO.noSeHanPodidoObtenerLosDatosDeConfiguracionDeLaAplicacion),
        ),
      );

    // Recuperamos el ticket.
    final ticket = await getById(ticketId!);
    if (ticket == null)
      return Either.right(
        Failure(
          TicketLoginError.generic,
          tt(TTSSO.noSeHanPodidoObtenerLosDatosDeConfiguracionDeLaAplicacion),
        ),
      );

    return Either.left(TicketGetResult(ticket, isRequiredDoubleFactor, []));
  }

  // @override
  // Future<void> delete(UserBase user) async {
  //   await _userLogoutApi.execute(
  //     url: await token.getUrl(),
  //     userId: user.id,
  //     token: await token.get(),
  //   );
  // }

  @override
  Future<bool> doubleFactorVerify(int code, String email) async =>
      DoubleFactorValidateApi().execute(
        url: await token.getUrl(),
        code: code,
        email: email,
        token: await token.get(),
      );

  @override
  Future<Either<bool, String?>> sendActivationMail(
          {required String email, int? companyIdSSO}) async =>
      UserActivationMailApi().send(
        url: await token.getUrl(),
        email: email,
        empresaId: companyIdSSO ?? 0,
        token: await token.get(),
      );
}
