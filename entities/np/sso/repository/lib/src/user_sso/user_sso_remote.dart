import '../_imports.dart';

class UserSSORemote implements RemoteBase<UserSSOModel, UserSSOPK> {
  final _api = UserGetApi();
  final _getByPk = UserGetByPkApi();
  final _getImage = UserImageGetApi();
  final TokenBase token;
  final TicketModel? Function() getTicketModel;
  final UserBase user;

  UserSSORemote(this.token, this.getTicketModel, this.user);

  @override
  Future<List<ReceiveAdapter2<UserSSOModel>>> getAll(String lastSync) async {
    final ticket = getTicketModel();
    if (ticket == null) {
      Log().debug("[UserSSORemote] Ignorado por no tener ticket");
      return [];
    }

    final result = await _api.execute(
      url: await token.getUrl(),
      token: await token.get(),
      companyId: ticket.company!.idInSSO,
      applicationId: ticket.application!.id,
      syncDate: lastSync,
      isAdmin: user.isAdmin,
      userId: user.id!,
    );

    final List<ReceiveAdapter2<UserSSOModel>> list = result.map2((e) {
      return ReceiveAdapter2(
        modifyDateBack: e.fechaModificacion!,
        model: _toModel(e),
      );
    });

    Log().debug("[UserSSORemoteSource] Usuarios recuperados: ${list.length}");
    return list;
  }

  UserSSOModel _toModel(UserGetResponse e) {
    bool isRemoved = false;

    try {
      DateTime? date;
      if (e.fechaBaja != null) date = DateTime.tryParse(e.fechaBaja!);
      // La fecha de baja viene siempre informada, pero cuando realmente no
      // ha sido asignada, viene con todo a 1.
      isRemoved = !isNullLesserOrEqualZero(date?.year) && date!.year > 2000;
    } catch (e) {}

    return UserSSOModel(
      id: e.id!,
      companyId: user.companyId!,
      roleId: e.rol?.id,
      roleName: e.rol?.nombre,
      name: e.nombre!,
      surname: e.apellidos ?? "",
      nameShort: e.nombreCorto ?? "",
      email: e.email!,
      imageMd5: e.imagenMD5 ?? "",
      creationDate: ParseUtil.stringToDatetime(e.fechaAlta!)!,
      modifyDate: ParseUtil.stringToDatetime(e.fechaModificacion!)!,
      isVerifiedEmail: e.activo ?? false,
      isInternal: e.interno ?? false,
      description: e.descripcion ?? "",
      isRemoved: isRemoved,
      isSync: true,
    );
  }

  @override
  Future<UserSSOModel?> getByPk(UserSSOPK pk) async {
    final response = await _getByPk.execute(
      url: await token.getUrl(),
      token: await token.get(),
      id: pk.id,
    );
    if (response == null) {
      return null;
    }
    return _toModel(response);
  }

  Future<UserGetResponse?> getDtoByPk(int pk) async => await _getByPk.execute(
        url: await token.getUrl(),
        token: await token.get(),
        id: pk,
      );

  Future<String> getImageByMd5(String md5) async => _getImage.execute(
        url: await token.getUrl(),
        token: await token.get(),
        md5: md5,
      );

  @override
  Future<Either<UserSSOModel, Failure2<SendErrorType>>> save(
      UserSSOModel? model) {
    // Se hace a través de SSORepository
    throw UnimplementedError();
  }
}
