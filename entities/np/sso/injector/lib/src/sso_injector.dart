import '_imports.dart';

class SSOInjector {
  final UserNPBase user;
  final ConnectionServiceBase connection;
  final TicketLocalBase ticketLocal;
  final UserSSOLocalBase? userSSOLocal;
  final MenuSSOLocalBase menuSSOLocal;
  final SSOLocalBase ssoLocal;
  final TextSSOLocalBase textSSOLocal;
  final MunicipalityPointLocalBase? municipalityPointLocal;

  final TokenBase token;
  final TicketModel? Function() getTicketCached;
  final bool Function(int? rolId) isRemovableRoleId;

  final PreferenceUserBase<String> lastSyncUserSSO;
  final PreferenceUserBase<String> lastSyncMenuSSO;
  // final PreferenceUserBase<String> lastSyncTranslationSSO;
  final PreferenceGlobalBase<String>? companyApp;

  final SyncServiceBase syncService;
  final List<SSORole> unallowedRoles;

  MenuSSORepositoryBase? menuSSORepository;
  SSORepositoryBase? ssoRepository;
  UserSSORepositoryBase? userSSORepository;
  TextSSORepositoryBase? textSSORepository;

  SSOInjector({
    required this.user,
    required this.connection,
    required this.ticketLocal,
    this.userSSOLocal,
    required this.menuSSOLocal,
    required this.ssoLocal,
    required this.textSSOLocal,
    this.municipalityPointLocal,
    required this.token,
    required this.getTicketCached,
    required this.isRemovableRoleId,
    required this.lastSyncUserSSO,
    required this.lastSyncMenuSSO,
    // required this.lastSyncTranslationSSO,
    required this.syncService,
    this.companyApp,
    this.ssoRepository,
    this.userSSORepository,
    this.menuSSORepository,
    this.textSSORepository,
    this.unallowedRoles = const [],
  });

  Future<void> init() async {
    // === MENU SSO === //
    final menuSSORemoteSource1 = MenuSSORemote(
      token: token,
      user: user,
    );

    if (menuSSORepository == null) {
      final menuSSOSync = Synchronizer<MenuSSOModel, MenuSSOPK>(
        syncService: injector.get<SyncServiceBase>(),
        syncLoopBase: SyncLoopOnceByUser(user),
        priority: -1000,
        syncReceive: SyncReceive(
          local: menuSSOLocal,
          remote: menuSSORemoteSource1,
          lastSyncPref: lastSyncMenuSSO,
          isSyncInLoop: () => false,
          syncService: syncService,
        ),
      );

      injector.registerSingleton<Synchronizer<MenuSSOModel, MenuSSOPK>>(
        menuSSOSync,
      );

      menuSSORepository = MenuSSORepository(
        local: menuSSOLocal,
        synchronizable: menuSSOSync,
        connection: connection,
        remote: menuSSORemoteSource1,
      );
    }

    injector.registerSingleton<MenuSSORepositoryBase>(menuSSORepository!);

    injector.registerSingleton<MenuSSOUseCase>(
      MenuSSOUseCase(
        injector.get<MenuSSORepositoryBase>(),
        user,
      ),
    );

    injector.registerSingleton<ManagerCache<MenuSSOModel, MenuSSOPK>>(
      ManagerCache<MenuSSOModel, MenuSSOPK>(
        menuSSOUseCase,
      ),
    );

    // === TRANSLATION SSO === //
    final textSSORemote = TextSSORemote(
      token: token,
      user: user,
    );

    if (textSSORepository == null) {
      final translationSSOSync = Synchronizer<TextSSOModel, String>(
        syncService: injector.get<SyncServiceBase>(),
        syncLoopBase: SyncLoopOnce(),
        priority: 999,
        syncReceive: SyncReceive(
          local: textSSOLocal,
          remote: textSSORemote,
          lastSyncPref: PreferenceEmptySync(),
          isSyncInLoop: () => false,
          syncService: syncService,
        ),
      );

      injector.registerSingleton<Synchronizer<TextSSOModel, String>>(
        translationSSOSync,
      );

      textSSORepository = TextSSORepository(
        local: textSSOLocal,
        synchronizable: translationSSOSync,
        connection: connection,
        remote: textSSORemote,
      );
    }

    injector.registerSingleton<TextSSORepositoryBase>(textSSORepository!);

    injector.registerSingleton<TextSSOUseCase>(
      TextSSOUseCase(
        injector.get<TextSSORepositoryBase>(),
        user,
      ),
    );

    injector.registerSingleton<ManagerCache<TextSSOModel, String>>(
      ManagerCache<TextSSOModel, String>(
        injector.get<TextSSOUseCase>(),
      ),
    );

    // === USER SSO ==== //
    final userSSORemoteSource = UserSSORemote(
      token,
      getTicketCached,
      user,
    );

    if (userSSOLocal != null && userSSORepository == null) {
      final userSSOSync = Synchronizer<UserSSOModel, UserSSOPK>(
        syncService: injector.get<SyncServiceBase>(),
        syncLoopBase: SyncLoopTimer(const Duration(minutes: 2)),
        // Necesitamos sincronizar esto primero porque hay muchos modelos que dependen de esto.
        priority: -100,
        syncReceive: SyncReceive(
          local: userSSOLocal!,
          remote: userSSORemoteSource,
          lastSyncPref: lastSyncUserSSO,
          syncService: syncService,
          isSyncInLoop: () => user.isAdmin,
        ),
      );

      injector.registerSingleton<Synchronizer<UserSSOModel, UserSSOPK>>(
          userSSOSync);

      userSSORepository = UserSSORepository(
        local: userSSOLocal!,
        synchronizable: userSSOSync,
        connection: connection,
        remote: userSSORemoteSource,
      );
    }

    if (userSSORepository != null) {
      injector.registerSingleton<UserSSORepositoryBase>(userSSORepository!);

      injector.registerSingleton<UserSSOUseCase>(
        UserSSOUseCase(injector.get<UserSSORepositoryBase>(), user),
      );

      injector.registerSingleton<ManagerCache<UserSSOModel, UserSSOPK>>(
        ManagerCache<UserSSOModel, UserSSOPK>(
          userSSOUseCase,
        ),
      );
    }

    // === TICKET SSO === //

    final ticketRemoteSource = TicketRemoteSource(
      token,
      user.app,
      connection,
      unallowedRoles,
    );

    injector.registerSingleton<TicketUseCase>(
      TicketUseCase(
        TicketRepository(
          local: ticketLocal,
          remote: ticketRemoteSource,
          connection: connection,
          user: user,
        ),
      ),
    );

    // === SSO === //

    if (ssoRepository == null) {
      ssoRepository = SSORepository(
        SSORemote(token, user),
        ssoLocal,
        connection,
        injector.getOrNull<Synchronizer<UserSSOModel, UserSSOPK>>(),
        userSSOLocal,
        companyApp,
        municipalityPointLocal,
      );
    }

    injector.registerSingleton<SSOUseCase>(
      SSOUseCase(
        ssoRepository!,
        user,
        isRemovableRoleId,
      ),
    );
  }
}
