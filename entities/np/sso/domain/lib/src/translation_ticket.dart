import '_imports.dart';

enum TTSSO {
  teHasRegistroEnX,
  vuelveAIniciarSesionParaPoderSeleccionarElNuevoMunicipio,
  registrarseEnOtroMunicipio,
  seleccionaUnMunicipio,
  noSeHanEncontradoMunicipios,
  tipos,
  lopd,
  historicoGeneral,
  miQr,
  portalDeComunicaciones,
  solicitudes,
  informes,
  catalogoDeVehiculos,
  centroEducativo,
  puntoLimpio,
  descripcionRequisitosContraseniaMinMax,
  laContraseniaDebeTenerAlMenos,
  solicitudReutilizables,
  entradaReutilizables,
  salidaReutilizables,
  listadoHistorioReutilizable,
  listadoCategoriasReutilizables,
  gestionEquipamientos,
  asignarUrl,
  pdf,
  filtroElementos,
  urlDelProyecto,
  limitaciones,
  personas,
  seleccionaUnaEmpresa,
  usuarioBloqueado,
  granProductor,
  comunitario,
  maestroCompostador,
  encargado,
  gestorDePuntoLimpio,
  ciudadano,
  domestico,
  administrador,
  noTienesAccesoAEstaAplicacion,
  laPersonaEstaPendienteDeSerBorrada,
  elUsuarioNoEstaActivo,
  elUsuarioNoEstaActivoRevisaTuCorreoElectronicoParaActivarlo,
  noSeHanPodidoObtenerLosDatosDeConfiguracionDeLaAplicacion,
  noSePuedenBorrarPersonasConElRolX,
  laContraseniaNoEsValida,

  // Menús.
  ciudadanos,
  comunidades,
  empresas,
  accionNoDisponible,
  proyecto,
  cartografia,
  recursos,
  movilidad,
  elementos,
  gestion,
  configuracion,
  informacion,
  integracion,
  smartCity,
  documentacion,
  contrato,
  inventario,
  vehiculosMaquinaria,
  objetivosContrato,
  herramientas,
  vistas,
  casa,
  aceras,
  tramos,
  medir,
  poligonos,
  definirCuadriculas,
  nuevoMapa,
  puntoLimpioMovil,
  edificiosEInstalaciones,
  zonasCartograficas,
  flota,
  alarmasGeograficas,
  mensajeria,
  agendaVoz,
  moviles,
  listadoFlota,
  reproducirRecorridos,
  itinerarios,
  movilesCercanos,
  catalogoElementosBdt,
  catalogoBdt,
  puntosUbicacion,
  puntosLimpios,
  equipamientos,
  entradasResiduos,
  salidasResiduos,
  objetivos,
  incidencias,
  estudioActual,
  optimizacionRutas,
  sincronizarBdt,
  utilidades,
  marcoGeografico,
  ambitoActividad,
  capasKml,
  proveedorCarto,
  configurarIconos,
  subflotas,
  equipamientoRecursosIa,
  catalogoElementosBdtIa,
  informacionLinea,
  listadoMovilidad,
  listadoVolumetricos,
  listadoSensores,
  listadoAlarmasGeo,
  informacionProducto,
  manualUsuario,
  glosarioTerminos,
  informacionProyecto,
  anotaciones,
  listadoDeAccesos,
  historicoDeGestion,
  entradaDeResiduos,
  salidaDeResiduos,
  listadoRevisiones,
  listadoCatastro,
  listadoExtracciones,
  listadoAportes,
  quieresQueVolvamosAEnviarElCorreoDeActivacionParaQuePuedasActivarTuCuenta,
  correoEnviado,
}

extension TTSSOExt on TTSSO {
  String get tt => translate(this);
}
