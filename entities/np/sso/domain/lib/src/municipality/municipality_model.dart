import '../_imports.dart';

@Deprecated("Usar MunicipalityApp")
class MunicipalityModel implements Model<int> {
  // TODO: convertirlo a un enum.
  /// Identificador de la empresa en el SSO.
  ///
  /// No cambia entre entornos del SSO.
  final int companyIdSSO;
  final String name;
  final List<LatLong> points;
  final int id;
  final bool isVisibleForCitizen;
  final List<int> availableAppIds;
  final List<int> notAvailableAppIds;
  final String image;

  MunicipalityModel({
    required this.companyIdSSO,
    required this.name,
    required this.points,
    required this.id,
    required this.isVisibleForCitizen,
    required this.availableAppIds,
    required this.notAvailableAppIds,
    required this.image,
  });

  int get pk => id;

  bool isVisibleInApp(SSOApplication app) {
    if (!availableAppIds.contains(app.id)) return false;
    if (notAvailableAppIds.contains(app.id)) return false;
    return true;
  }
}
