import '../_imports.dart';

@Deprecated("Usar MunicipalityApp")
class MunicipalityPointModel implements Model<int> {
  final int companyIdSSO;
  final List<LatLong> points;

  MunicipalityPointModel({
    required this.companyIdSSO,
    required this.points,
  });

  int get pk => companyIdSSO;

  LatLong? getSouthWestBoundLimit() {
    if (points.isEmpty) return null;

    double minLat = points.first.lat;
    double minLong = points.first.long;

    for (final p in points) {
      if (p.lat < minLat) minLat = p.lat;
      if (p.long < minLong) minLong = p.long;
    }

    return LatLong(minLat, minLong);
  }

  LatLong? getNorthEastBoundLimit() {
    if (points.isEmpty) return null;

    double maxLat = points.first.lat;
    double maxLong = points.first.long;

    for (final p in points) {
      if (p.lat > maxLat) maxLat = p.lat;
      if (p.long > maxLong) maxLong = p.long;
    }

    return LatLong(maxLat, maxLong);
  }
}
