import '_imports.dart';

class MunicipalityApp {
  final int applicationId;
  final int companyIdSSO;
  final int municipalityId;
  final int id;
  final MunicipalityAppCompany? company;
  final MunicipalityAppMunicipality? municipality;

  MunicipalityApp({
    required this.applicationId,
    required this.companyIdSSO,
    required this.municipalityId,
    required this.id,
    required this.company,
    required this.municipality,
  });

  late final filter =
      "${municipality?.municipality ?? ""} ${municipality?.province ?? ""}"
          .toLowerCase();

  late final name =
      [municipality?.municipality, municipality?.province].joinFilled(", ");

  List<MunicipalityApp> getCompaniesInTheSameMunicipality(
          List<MunicipalityApp> municipalities) =>
      municipalities.where2((e) {
        if (e.applicationId != applicationId) return false;
        if (e.municipalityId != municipalityId) return false;
        if (e.company?.name.isNullOrEmpty != false) return false;
        return true;
      });
}

class MunicipalityAppCompany {
  final int id;
  final String name;
  final int managementId;

  MunicipalityAppCompany({
    required this.id,
    required this.name,
    required this.managementId,
  });
}

class MunicipalityAppMunicipality {
  final int id;
  final String municipality;
  final String province;
  final String autonomousCommunity;
  final String country;

  MunicipalityAppMunicipality({
    required this.id,
    required this.municipality,
    required this.province,
    required this.autonomousCommunity,
    required this.country,
  });
}
