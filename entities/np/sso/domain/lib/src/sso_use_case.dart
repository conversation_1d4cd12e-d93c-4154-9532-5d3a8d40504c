import '_imports.dart';

class SSOUseCase {
  @protected
  final SSORepositoryBase repository;
  final UserNPBase user;
  final bool Function(int? rolId) isRemovableRoleId;

  SSOUseCase(this.repository, this.user, this.isRemovableRoleId);

  /// Eliminar un usuario del SSO.
  /// TODO - Eliminar el usuario de ecocompostaje
  Future<Result<String>> remove(UserSSOModel userSSO) async {
    if (!isTrue(user.isAdmin))
      return Result.error(tt(TTShared.noTienesPermisosParaRealizarEstaAccion));
    if (!isRemovableRoleId(userSSO.roleId))
      return Result.error(
        tt(
          TTSSO.noSePuedenBorrarPersonasConElRolX,
          [userSSO.roleName ?? ''],
        ),
      );
    return repository.remove(userSSO.id);
  }

  /// Eliminar varios usuarios del SSO.
  Future<Result<String>> removeAll(List<UserSSOModel> models) async {
    if (isListNullOrEmpty(models))
      return Result.error(tt(TTShared.noSeHaSeleccionadoNingunRegistro));
    int dontRemoved = 0;
    for (final m in models) {
      final r = await repository.remove(m.id);
      if (r.isError) dontRemoved++;
    }

    if (dontRemoved > 0)
      return Result.error(
          tt(TTShared.noSeHanPodidoBorrarXRegistros, [dontRemoved.toString()]));

    return Result.valid();
  }

  /// Actualiza la información del usuario.
  Future<Result<String>> update({
    required int userId,
    String? email,
    String? name,
    String? surname,
    String? image,
    String? communityUuid,
  }) async {
    if (!user.isLogin())
      return Result.error(tt(TTShared.noTienesPermisosParaRealizarEstaAccion));

    if (isNullLesserOrEqualZero(userId))
      return Result.error(tt(TTShared.noSeHaIndicadoElUsuario));

    if (user.id != userId && !user.isAdmin)
      return Result.error(tt(TTShared.noTienesPermisosParaRealizarEstaAccion));

    final check = <ValueObjectBase>[
      if (email != null)
        StringFilled(
          email,
          tt(TTShared.email),
        ),
      if (name != null)
        StringFilled(
          name,
          tt(TTShared.nombre),
        ),
      if (surname != null)
        StringFilled(
          surname,
          tt(TTShared.apellidos),
        ),
      if (image != null)
        StringFilled(
          image,
          tt(TTShared.imagen),
        ),
    ].validate();

    if (check.isError) return check;

    return repository.update(
      name: name,
      surname: surname,
      image: image,
      password: null,
      email: email,
    );
  }

  /// Crea el usuario en el SSO.
  ///
  /// Toda la informacion que se envia será registrada en el SSO, esto es independiente de cualquier aplicacion o cualquier caso de uso
  Future<Result<String>> create({
    required Email login,
    required Password password,
    required StringFilled? name,
    required StringFilled? surname,
    required NotNull<LanguageTypeSSO> language,
    required IntGreaterZero companyIdInSSO,
    required IntGreaterZero applicationId,
    required IntGreaterZero roleId,
  }) async {
    // Si hay un usuario identificado y no es admin, no puede realizar la operación.
    if (user.isLogin() && !user.isAdmin)
      return Result.error(tt(TTShared.noTienesPermisosParaRealizarEstaAccion));

    final checkList = <ValueObjectBase>[
      login,
      if (name != null) name,
      if (surname != null) surname,
      password,
      language,
      companyIdInSSO,
      applicationId,
      roleId,
    ];

    final check = checkList.validate();

    if (check.isError) return check;

    return repository.insert(
      login: login.value,
      password: CryptoUtil.sha256(password.value),
      name: name?.value ?? "",
      lastName: surname?.value ?? "",
      languageType: language.value,
      applicationId: applicationId.value!,
      companyId: companyIdInSSO.value!,
      roleId: roleId.value!,
    );
  }

  /// Conceder acceso a una aplicación a un usuario en una compañia especifica con un rol especifico
  Future<Result<String>> giveAccess({
    required StringFilled login,
    required IntGreaterZero companyIdInSSO,
    required IntGreaterZero applicationId,
    required IntGreaterZero roleId,
  }) async {
    final r = <ValueObjectBase>[
      login,
      companyIdInSSO,
      applicationId,
      roleId,
    ].validate();

    if (r.isError) return r;

    return repository.giveAccess(
      login: login.value!,
      companyIdInSSO: companyIdInSSO.value!,
      applicationId: applicationId.value!,
      roleId: roleId.value!,
    );
  }

  // Conjunto de metodos para recuperar la contraseña del usuario.

  /// Paso 1 de la recuperación de contraseña. Enviar el correo con el código de recuperación.
  Future<Result<String?>> recoverPasswordStep1(Email login) async {
    final r = <ValueObjectBase>[
      login,
    ].validate();

    if (r.isError) return r;

    return repository.recoverPasswordStep1(login.value);
  }

  /// Paso 2 Leer el codigo y validar que el codigo es el correcto en base de datos.
  Future<Result<String?>> recoverPasswordStep2(
    Email login,
    IntGreaterZero code,
  ) async {
    final r = <ValueObjectBase>[
      login,
      code,
    ].validate();

    if (r.isError) return r;

    return repository.recoverPasswordStep2(
      login.value,
      code.value!,
    );
  }

  /// Paso 3 Cambiar la contraseña del usuario. Ingresar la nueva contraseña.
  Future<Result<String?>> recoverPasswordStep3(
    Email login,
    Password password,
  ) async {
    final r = <ValueObjectBase>[
      login,
      password,
    ].validate();

    if (r.isError) return r;

    return repository.recoverPasswordStep3(
      login.value,
      CryptoUtil.sha256(password.value),
    );
  }

  /// Modifica la contraseña del usuario.
  Future<Result<String?>> modifyPassword(
      StringFilled login, Password password) async {
    final r = <ValueObjectBase>[
      login,
      password,
    ].validate();

    if (r.isError) return r;

    return repository.modifyPassword(
      login.value!,
      CryptoUtil.sha256(password.value),
    );
  }

  Future<List<CompanyModel>> getCompanies() => repository.getCompanies();

  Future<bool> hasCompanyTheApp(int companyInSSOId, SSOApplication app) =>
      repository.hasCompanyTheApp(companyInSSOId, app);

  Future<bool> hasAppTheCompany(int companyInSSOId, SSOApplication app) =>
      repository.hasAppTheCompany(companyInSSOId, app);

  @Deprecated("Usar getMunicipalitiesApp")
  Future<List<MunicipalityModel>> getMunicipalities() =>
      repository.getMunicipalities();

  @Deprecated("Usar getMunicipalitiesApp")
  Future<MunicipalityPointModel?> getMunicipalityPointByPk(int companyIdSSO) =>
      repository.getMunicipalityPointByPk(companyIdSSO);

  Future<List<MunicipalityApp>> getMunicipalitiesApp() =>
      repository.getMunicipalitiesApp();

  Future<String> getImageByMd5Application(String? md5) async {
    if (md5.isNullOrEmpty) return "";
    return repository.getImageByMd5Application(md5!);
  }

  Future<String> getImageByMd5Company(String? md5) async {
    if (md5.isNullOrEmpty) return "";
    return repository.getImageByMd5Company(md5!);
  }

  /// Permite obtener los datos de la empresa, como la url del servidor de la NP, cuando
  /// el usuario todavía no se ha logeado y se necesita llamar a la api de la NP.
  Future<CompanyAppModel?> getCompanyAppById(int companyIdInSSO) async {
    if (companyIdInSSO.isNullOrZero) return null;

    final r = await repository.getCompanyAppById(companyIdInSSO);
    if (r == null) return null;
    if (r.appId != user.app.id) return null;
    if (r.companyIdInSSO != companyIdInSSO) return null;

    return r;
  }
}

abstract class SSORepositoryBase {
  get companyId => companyId;

  Future<Result<String>> remove(int id);
  Future<Result<String>> insert({
    required String login,
    required String password,
    required String name,
    required String lastName,
    required LanguageTypeSSO languageType,
    required int companyId,
    required int applicationId,
    required int roleId,
  });

  Future<Result<String>> giveAccess({
    required String login,
    required int companyIdInSSO,
    required int applicationId,
    required int roleId,
  });

  Future<Result<String>> recoverPasswordStep1(String login);
  Future<Result<String>> recoverPasswordStep2(String login, int code);
  Future<Result<String>> recoverPasswordStep3(String login, String password);
  Future<Result<String>> modifyPassword(String login, String password);

  Future<Result<String>> update({
    required String? name,
    required String? surname,
    required String? image,
    required String? password,
    required String? email,
  });

  Future<List<CompanyModel>> getCompanies();

  Future<bool> hasCompanyTheApp(int companyInSSOId, SSOApplication app);
  Future<bool> hasAppTheCompany(int companyInSSOId, SSOApplication app);
  Future<List<MunicipalityModel>> getMunicipalities();
  Future<MunicipalityPointModel?> getMunicipalityPointByPk(int companyIdSSO);
  Future<List<MunicipalityApp>> getMunicipalitiesApp();
  Future<String> getImageByMd5Application(String md5);
  Future<String> getImageByMd5Company(String md5);
  Future<CompanyAppModel?> getCompanyAppById(int companyIdInSSO);
}
