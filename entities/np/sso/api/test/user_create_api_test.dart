import 'package:flutter_test/flutter_test.dart';
import 'package:sso_api/src/_exports.dart';
import 'package:testing/testing.dart';
import 'package:token_source_sso/token_source_sso.dart';

void main() {
  group("UserCreateApi", () {
    final email = "<EMAIL>";
    final password = "12345678aA*";
    final guid = Guid().get();

    final testing = TestStart(
      MockPrefs.server_DEV_SSO,
      MockPrefs.user_ServDesarrollo,
      TokenSourceSSO(),
    );

    setUpAll(() async {
      await testing.start();
    });

    test('Se crea el usuari', () async {
      final token = await testing.token.get();

      final result = await UserCreateApi().execute(
        url: testing.serverData.url,
        token: token,
        companyId: 0,
        login: email,
        passwordSha256: CryptoUtil.sha256(password),
        name: guid,
        lastName: guid,
        languageId: 1,
      );

      expect(result.isValid, true);
    });
  });
}
