import 'package:flutter_test/flutter_test.dart';
import 'package:sso_api/src/_exports.dart';
import 'package:testing/testing.dart';
import 'package:token_source_sso/token_source_sso.dart';

void main() {
  group("UserGrantAccess", () {
    final app = SSOApplication.EcoMovil;
    final companyId = 4;
    final email = "<EMAIL>";
    final role = SSORole.admin;

    final api = UserGrantAccessApi();
    final testing = TestStart(
      MockPrefs.server_PRE_SSO,
      MockPrefs.user_ServDesarrollo,
      TokenSourceSSO(),
    );

    setUpAll(() async {
      await testing.start();
    });

    test('La respuesta es válida', () async {
      final result = await api.execute(
        url: testing.serverData.url,
        token: await testing.token.get(),
        applicationId: app.id,
        companyId: companyId,
        login: email,
        roleId: role.id,
      );

      expect(result.isValid, true);

      final data = await UserGetApi().execute(
        url: testing.serverData.url,
        token: await testing.token.get(),
        applicationId: app.id,
        companyId: companyId,
        syncDate: '2021-01-01T00:00:00',
        isAdmin: testing.user.isAdmin,
        userId: testing.user.id ?? 0,
      );

      expect(data.isNotEmpty, true);

      final exists = data.firstWhere2((m) => m.email == email);

      expect(exists != null, true);
    });
  });
}
