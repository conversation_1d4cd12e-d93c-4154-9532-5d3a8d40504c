// @Skip()

import 'package:flutter_test/flutter_test.dart';
import 'package:sso_api/src/_exports.dart';
import 'package:testing/testing.dart';
import 'package:token_source_sso/token_source_sso.dart';

void main() {
  group("UserLoginApi", () {
    final api = UserLoginApi();
    final testStart = TestStart(
      MockPrefs.server_DEV_SSO,
      MockPrefs.user_ServDesarrollo,
      TokenSourceSSO(),
    );

    setUpAll(() async {
      await testStart.start();
    });

    test('La respuesta es válida', () async {
      final data = await api.execute(
        url: testStart.serverData.url,
        login: "<EMAIL>",
        passwordSha256OrMd5: CryptoUtil.sha256("12345678aA#"),
        token: ((await testStart.token.get())),
      );

      expect(data.isLeft && data.left!.accesos.isNotEmpty, true);

      data.left!.accesos.forEach((a) {
        print(
            "${a.empresa!.id!} / ${a.empresa!.idGestion} (${a.empresa!.nombre}) - ${a.aplicacion!.nombre} - ${a.rol!.nombre} - ${a.urlApi}");
      });
    });
  });
}
