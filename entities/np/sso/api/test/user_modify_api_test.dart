// @Skip()

import 'package:flutter_test/flutter_test.dart';
import 'package:sso_api/src/_exports.dart';
import 'package:testing/testing.dart';
import 'package:token_source_sso/token_source_sso.dart';

void main() {
  group("UserModifyApi", () {
    final apiPassModify = UserPasswordRecover3OrModifyApi();
    final apiModify = UserModifyApi();
    final apiLogin = UserLoginApi();
    final testStart = TestStart(
      MockPrefs.server_PRE_SSO,
      MockPrefs.user_ServDesarrollo,
      TokenSourceSSO(),
    );

    final currentPassword = Guid().get();
    final newPassword = Guid().get();
    late Either loginData;

    setUpAll(() async {
      await testStart.start();
      Log().info("Contraseña nueva: $newPassword");
    });

    test(
        'Forzamos que inicie con una contraseña determinada para poder realizar el test correctamente',
        () async {
      final response = await apiPassModify.execute(
        url: testStart.serverData.url,
        token: ((await testStart.token.get())),
        login: '<EMAIL>',
        newPassword: CryptoUtil.md5(currentPassword),
      );

      expect(response, true);
    });

    test('Se identifica con la contraseña actual', () async {
      await Future.delayed(Duration(seconds: 2));
      loginData = await apiLogin.execute(
        url: testStart.serverData.url,
        login: "<EMAIL>",
        passwordSha256OrMd5: CryptoUtil.md5(currentPassword),
        token: ((await testStart.token.get())),
      );

      expect(loginData.left.accesos != null && loginData.left.nombre.isNotEmpty,
          true);
    });

    test('Los datos del usuario se modifican', () async {
      final response = await apiModify.execute(
        url: testStart.serverData.url,
        token: ((await testStart.token.get())),
        email: '<EMAIL>',
        image: loginData.left.imagen == null || loginData.left.imagen.isEmpty
            ? MockPrefs.imageBase64
            : '',
        name: loginData.left.nombre + "0",
        surname: loginData.left.apellidos + "0",
        password: CryptoUtil.md5(newPassword),
      );

      expect(response, true);
    });

    test('Los datos han cambiado', () async {
      await Future.delayed(Duration(seconds: 2));
      final newData = await apiLogin.execute(
        url: testStart.serverData.url,
        login: "<EMAIL>",
        passwordSha256OrMd5: CryptoUtil.md5(currentPassword),
        token: ((await testStart.token.get())),
      );

      expect(
        newData.isLeft,
        true,
        reason: "No puede logear con la nueva contraseña",
      );
    });

    //Restablecemos datos de ciudadano para poder loguear
    () async => await apiModify.execute(
          url: testStart.serverData.url,
          token: ((await testStart.token.get())),
          email: '<EMAIL>',
          image: loginData.left.imagen == null || loginData.left.imagen.isEmpty
              ? MockPrefs.imageBase64
              : '',
          name: "Ciudadano2",
          surname: "Apellido2",
          password: CryptoUtil.md5('12345678'),
        );
  });
}
