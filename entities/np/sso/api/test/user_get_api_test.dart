import 'package:flutter_test/flutter_test.dart';
import 'package:sso_api/src/_exports.dart';
import 'package:sso_api/src/user_get_api.dart';
import 'package:testing/testing.dart';
import 'package:token_source_sso/token_source_sso.dart';

void main() {
  group("UserGetApi", () {
    final api = UserGetApi();
    final testing = TestStart(
      MockPrefs.server_PRE_SSO,
      MockPrefs.user_ServDesarrollo,
      TokenSourceSSO(),
    );

    setUpAll(() async {
      await testing.start();
    });

    test('La respuesta es válida', () async {
      final data = await api.execute(
        url: testing.serverData.url,
        token: await testing.token.get(),
        applicationId: SSOApplication.EcoSATPuntoLimpio.id,
        companyId: 4,
        syncDate: '2021-01-01T00:00:00',
        isAdmin: testing.user.isAdmin,
        userId: testing.user.id ?? 0,
      );

      expect(data.isNotEmpty, true);
    });
  });
}
