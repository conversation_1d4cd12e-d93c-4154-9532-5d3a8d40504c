// @Skip()

import 'package:flutter_test/flutter_test.dart';
import 'package:sso_api/src/_exports.dart';
import 'package:testing/testing.dart';
import 'package:token_source_sso/token_source_sso.dart';

void main() {
  group("UserModifyApi", () {
    String actualImage = "";
    final apiModify = UserModifyApi();
    final apiLogin = UserLoginApi();
    final testStart = TestStart(
      MockPrefs.server_PRE_SSO,
      MockPrefs.user_ServDesarrollo_NP,
      TokenSourceSSO(),
    );

    late Either loginData;

    setUpAll(() async {
      await testStart.start();
    });

    test('Se identifica con la contraseña actual', () async {
      await Future.delayed(Duration(seconds: 2));
      loginData = await apiLogin.execute(
        url: testStart.serverData.url,
        login: testStart.userData.login!,
        passwordSha256OrMd5: CryptoUtil.sha256(testStart.userData.password!),
        token: await testStart.token.get(),
      );

      expect(loginData.left.nombre.isNotEmpty, true);
    });

    test('Los datos del usuario se modifican', () async {
      actualImage = loginData.left.imagen == MockPrefs.imageBase64
          ? MockPrefs.imageBase64Alt
          : MockPrefs.imageBase64;
      final response = await apiModify.execute(
        url: testStart.serverData.url,
        token: ((await testStart.token.get())),
        email: testStart.userData.login!,
        image: actualImage,
        name: loginData.left.nombre + "0",
        surname: loginData.left.apellidos + "0",
        password: CryptoUtil.sha256(testStart.userData.password!),
      );

      expect(response, true);
    });

    test('Los datos han cambiado', () async {
      await Future.delayed(Duration(seconds: 2));
      final newData = await apiLogin.execute(
        url: testStart.serverData.url,
        login: testStart.userData.login!,
        passwordSha256OrMd5: CryptoUtil.sha256(testStart.userData.password!),
        token: ((await testStart.token.get())),
      );

      expect(
        newData.left!.imagen != loginData.left.imagen,
        true,
        reason: "No ha cambiado la imagen",
      );
    });
  });
}
