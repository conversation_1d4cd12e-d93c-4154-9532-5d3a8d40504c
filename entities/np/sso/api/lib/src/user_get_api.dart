import '_imports.dart';

class UserGetApi {
  Future<List<UserGetResponse>> execute({
    required String url,
    required String token,
    required int companyId,
    required int applicationId,
    required String syncDate,
    required bool isAdmin,
    required int userId,
  }) async {
    ApiResponse response;

    if (isAdmin)
      response = await Api().post(
        url: "$url/sso/usuarios/empresa/app?conImagenes=false",
        body: {
          "EmpresaId": companyId,
          "AplicacionId": applicationId,
          "From": syncDate.toDateNP(),
        },
        header: Prefs.getHeaderToken(token),
      );
    else
      response = await Api().get(
        url: "$url/sso/usuario/${userId}",
        header: Prefs.getHeaderToken(token),
      );

    if (response.checkErrors() != null) return [];

    if (isAdmin) {
      return response.response
          .map<UserGetResponse>((r) => UserGetResponse.fromJson(r))
          .toList();
    }

    return [UserGetResponse.fromJson(response.response)];
  }
}

class UserGetResponse {
  int? id;
  String? nombre;
  String? email;
  Idioma? idioma;
  bool? activo;
  String? imagen;
  String? imagenMD5;
  List<Accesos> accesos = [];
  Rol? rol;
  String? fechaAlta;
  String? fechaModificacion;
  String? fechaBaja;
  bool? interno;
  String? nombreCorto;
  String? apellidos;
  String? descripcion;

  UserGetResponse.fromJson(Map<String, dynamic> json) {
    id = json['Id'];
    nombre = json['Nombre'];
    nombreCorto = json['NombreCorto'];
    apellidos = json['Apellidos'];
    descripcion = json['Descripcion'];
    email = json['Email'];
    idioma = json['Idioma'] != null ? Idioma.fromJson(json['Idioma']) : null;
    activo = json['Activo'];
    imagen = json['Imagen'];
    imagenMD5 = json['ImagenMD5'];
    if (json['Accesos'] != null) {
      accesos = <Accesos>[];
      json['Accesos'].forEach((v) {
        accesos.add(Accesos.fromJson(v));
      });
    }
    rol = json['Rol'] != null ? Rol.fromJson(json['Rol']) : null;
    fechaAlta = json['FechaAlta'];
    fechaModificacion = json['FechaModificacion'];
    fechaBaja = json['FechaBaja'];
    interno = json['Interno'];
  }
}
