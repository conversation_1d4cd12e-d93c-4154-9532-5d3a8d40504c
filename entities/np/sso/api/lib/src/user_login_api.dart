import '_imports.dart';

enum UserLoginApiError {
  generic,
  emailOrPassNotValid,
  userNotVerified,
  userRemoved,
  passNotValid,
  userBlocked,
}

class UserLoginApi {
  Future<Either<UserLoginResponse, Failure2<UserLoginApiError>>> execute({
    required String url,
    required String login,
    required String passwordSha256OrMd5,
    required String token,
  }) async {
    final response = await Api().post(
      url: "$url/sso/usuario/valida?image=false",
      body: {
        "UsuarioLogin": login,
        "UsuarioPassword": passwordSha256OrMd5,
      },
      header: Prefs.getHeaderToken(token),
      isPrinterInLog: false,
    );

    if (response.isError) {
      if (response.response is String && isStringNullOrEmpty(response.response))
        return Either.right(Failure2.code(UserLoginApiError.generic));

      // Error 1; Usuario o contraseña no válidos
      // Error 2; Usuario dado de baja
      // Error 3; Usuario no validado
      // Error 4; Contraseña erronea, dispone de N intentos
      // Error 5; Usuario bloqueado durante 15 minutos tras tres intentos fallidos
      // Error 6; Usuario bloqueado, inténtelo de nuevo dentro de N minutos
      // Error 7; Usuario bloqueado tras tres meses sin actividad, póngase en contacto con su administrador
      try {
        final values = response.response.toString().split("; ");
        final code = values.length > 0 ? values[0] : "";
        final message = values.length > 1 ? values[1] : "";

        if (code == "Error 1")
          return Either.right(
              Failure2.code(UserLoginApiError.emailOrPassNotValid));
        if (code == "Error 2")
          return Either.right(Failure2.code(UserLoginApiError.userRemoved));
        if (code == "Error 3")
          return Either.right(Failure2.code(UserLoginApiError.userNotVerified));
        if (code == "Error 4")
          return Either.right(
              Failure2(UserLoginApiError.passNotValid, message));
        if (code == "Error 5" || code == "Error 6" || code == "Error 7")
          return Either.right(Failure2(UserLoginApiError.userBlocked, message));
        return Either.right(Failure2.code(UserLoginApiError.generic));
      } catch (e) {
        //
      }

      return Either.right(Failure2.code(UserLoginApiError.generic));
    }

    return Either.left(UserLoginResponse.fromJson(response.response));
  }
}

class UserLoginResponse {
  String? fechaAlta;
  String? fechaModificacion;
  String? fechaBaja;
  int? id;
  String? nombre;
  String? email;
  Idioma? idioma;
  bool? dobleFactor;
  String? fechaDF;
  bool? activo;
  bool? interno;
  String? imagen;
  String? imagenMD5;
  String? validadoFecha;
  List<Accesos> accesos = [];
  Rol? rol;

  UserLoginResponse.fromJson(Map<String, dynamic> json) {
    fechaAlta = json['FechaAlta'];
    fechaModificacion = json['FechaModificacion'];
    fechaBaja = json['FechaBaja'];
    id = json['Id'];
    nombre = json['Nombre'];
    email = json['Email'];
    idioma =
        json['Idioma'] != null ? new Idioma.fromJson(json['Idioma']) : null;
    dobleFactor = json['DobleFactor'];
    fechaDF = json['FechaDF'];
    activo = json['Activo'];
    interno = json['Interno'];
    imagen = json['Imagen'];
    imagenMD5 = json['ImagenMD5'];
    validadoFecha = json['ValidadoFecha'];
    if (json['Accesos'] != null) {
      json['Accesos'].forEach((v) {
        accesos.add(new Accesos.fromJson(v));
      });
    }
    rol = json['Rol'] != null ? new Rol.fromJson(json['Rol']) : null;
  }
}

class Idioma {
  String? fechaAlta;
  String? fechaModificacion;
  String? fechaBaja;
  int? id;
  String? nombre;
  String? codigo;

  Idioma.fromJson(Map<String, dynamic> json) {
    fechaAlta = json['FechaAlta'];
    fechaModificacion = json['FechaModificacion'];
    fechaBaja = json['FechaBaja'];
    id = json['Id'];
    nombre = json['Nombre'];
    codigo = json['Codigo'];
  }
}

class Accesos {
  int? id;
  Aplicacion? aplicacion;
  Empresa? empresa;
  Rol? rol;
  String? urlApi;
  String? urlApiIA;
  String? urlApiEcoSAT;
  String? urlWeb;
  bool? virtual;

  Accesos.fromJson(Map<String, dynamic> json) {
    id = json['Id'];
    aplicacion = json['Aplicacion'] != null
        ? new Aplicacion.fromJson(json['Aplicacion'])
        : null;
    empresa =
        json['Empresa'] != null ? new Empresa.fromJson(json['Empresa']) : null;
    rol = json['Rol'] != null ? new Rol.fromJson(json['Rol']) : null;
    urlApi = json['UrlApi'];
    urlApiIA = json['UrlApiIA'];
    urlApiEcoSAT = json['UrlApiEcoSAT'];
    urlWeb = json['UrlWeb'];
    virtual = json['Virtual'];
  }
}

class Aplicacion {
  String? fechaAlta;
  String? fechaModificacion;
  String? fechaBaja;
  int? id;
  String? nombre;
  String? imagen;
  String? imagenMD5;

  Aplicacion.fromJson(Map<String, dynamic> json) {
    fechaAlta = json['FechaAlta'];
    fechaModificacion = json['FechaModificacion'];
    fechaBaja = json['FechaBaja'];
    id = json['Id'];
    nombre = json['Nombre'];
    imagen = json['Imagen'];
    imagenMD5 = json['ImagenMD5'];
  }
}

class Empresa {
  String? fechaAlta;
  String? fechaModificacion;
  String? fechaBaja;
  int? id;
  String? nombre;
  String? imagen;
  String? imagenMD5;
  int? idGestion;
  int? cartoId;
  String? cartografia;

  Empresa.fromJson(Map<String, dynamic> json) {
    fechaAlta = json['FechaAlta'];
    fechaModificacion = json['FechaModificacion'];
    fechaBaja = json['FechaBaja'];
    id = json['Id'];
    nombre = json['Nombre'];
    imagen = json['Imagen'];
    imagenMD5 = json['ImagenMD5'];
    idGestion = json['IdGestion'];
    cartoId = json['CartoId'];
    cartografia = json['Cartografia'];
  }
}

class Rol {
  String? fechaAlta;
  String? fechaModificacion;
  String? fechaBaja;
  int? id;
  String? nombre;
  String? descripcion;

  Rol.fromJson(Map<String, dynamic> json) {
    fechaAlta = json['FechaAlta'];
    fechaModificacion = json['FechaModificacion'];
    fechaBaja = json['FechaBaja'];
    id = json['Id'];
    nombre = json['Nombre'];
    descripcion = json['Descripcion'];
  }
}
