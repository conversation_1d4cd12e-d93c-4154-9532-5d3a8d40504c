// ignore_for_file: public_member_api_docs, sort_constructors_first
import '_imports.dart';

class MunicipalityAppGetApi {
  Future<List<MunicipioAppDto>> execute({
    required String url,
    required String token,
    required int appId,
  }) async {
    // No necesitamos establecer la fecha porque este modelo siempre se obtiene del servidor.
    final response = await Api().get(
      url: "$url/sso/aplicacion/empresas/municipios/$appId",
      header: Prefs.getHeaderToken(token),
      isPrinterInLog: false,
    );

    if (response.checkErrors() != null) return [];

    return response
        .getResponseListDto<MunicipioAppDto>((o) => MunicipioAppDto.fromJson(o))
        .where2((m) => m.fechaBaja.isNullOrEmpty);
  }
}

class MunicipioAppDto {
  final int? idAplicacion;
  final int? idEmpresa;
  final int? idMunicipio;
  final int? id;
  final String? fechaCreacion;
  final String? fechaModificacion;
  final String? fechaBaja;
  final MunicipioAppEmpresaDto? empresa;
  final MunicipioAppMunicipioDto? municipio;

  MunicipioAppDto({
    required this.idAplicacion,
    required this.idEmpresa,
    required this.idMunicipio,
    required this.id,
    required this.fechaCreacion,
    required this.fechaModificacion,
    required this.fechaBaja,
    required this.empresa,
    required this.municipio,
  });

  factory MunicipioAppDto.fromJson(Map<String, dynamic> json) {
    return MunicipioAppDto(
      idAplicacion: json['idAplicacion'],
      idEmpresa: json['idEmpresa'],
      idMunicipio: json['idMunicipio'],
      id: json['id'],
      fechaCreacion: json['fechaCreacion'],
      fechaModificacion: json['fechaModificacion'],
      fechaBaja: json['fechaBaja'],
      empresa: json['empresa'] == null
          ? null
          : MunicipioAppEmpresaDto.fromJson(json['empresa']),
      municipio: json['municipio'] == null
          ? null
          : MunicipioAppMunicipioDto.fromJson(json['municipio']),
    );
  }
}

class MunicipioAppEmpresaDto {
  final int? id;
  final String? nombre;
  final int? idGestion;

  MunicipioAppEmpresaDto({
    required this.id,
    required this.nombre,
    required this.idGestion,
  });

  factory MunicipioAppEmpresaDto.fromJson(Map<String, dynamic> json) {
    return MunicipioAppEmpresaDto(
      id: json['id'],
      nombre: json['nombre'],
      idGestion: json['idGestion'],
    );
  }
}

class MunicipioAppMunicipioDto {
  final int? id;
  final String? municipio;
  final String? provincia;
  final String? comunidadAutonoma;
  final String? pais;
  final String? fechaCreacion;
  final String? fechaModificacion;
  final String? fechaBaja;

  MunicipioAppMunicipioDto({
    required this.id,
    required this.municipio,
    required this.provincia,
    required this.comunidadAutonoma,
    required this.pais,
    required this.fechaCreacion,
    required this.fechaModificacion,
    required this.fechaBaja,
  });

  factory MunicipioAppMunicipioDto.fromJson(Map<String, dynamic> json) {
    return MunicipioAppMunicipioDto(
      id: json['id'],
      municipio: json['municipio'],
      provincia: json['provincia'],
      comunidadAutonoma: json['comunidadAutonoma'],
      pais: json['pais'],
      fechaCreacion: json['fechaCreacion'],
      fechaModificacion: json['fechaModificacion'],
      fechaBaja: json['fechaBaja'],
    );
  }
}
