import '_imports.dart';

class UserGetByEmailApi {
  Future<UserGetResponse?> execute({
    required String url,
    required String token,
    required String email,
  }) async {
    final response = await Api().get(
      url: "$url/sso/usuario/email?email=$email",
      header: Prefs.getHeaderToken(token),
    );

    if (response.checkErrors() != null) return null;

    return UserGetResponse.fromJson(response.response);
  }
}
