import '../_imports.dart';

@Deprecated("Recuperar los datos de MunicipalityApp")
class MunicipalityGetUseCaseView {
  static Future<Either<List<MunicipalityModel>, String>> execute({
    required ConnectionServiceBase connection,
    required SSOUseCase ssoUseCase,
    required SSOApplication app,
    required bool Function(LatLong? position, List<LatLong> poligon)
        isInPolygon,
  }) async {
    if (!connection.hasGps)
      return Either.right(TTShared.necesitasPosicioniGpsParaContinuar.tt);

    bool? hasPositionValid = connection.hasPositionValid();

    if (!hasPositionValid)
      hasPositionValid =
          await connection.forceHasPositionValid().catchError((_) => false);

    if (hasPositionValid.isNullOrFalse)
      return Either.right(TTShared.necesitasPosicioniGpsParaContinuar.tt);

    List<MunicipalityModel>? municipalities =
        await ssoUseCase.getMunicipalities();

    municipalities.removeWhere((s) {
      if (!s.isVisibleInApp(app)) return true;
      if (!isDebugMode()) {
        if (!s.isVisibleForCitizen) return true;
        if (!isInPolygon(
          connection.currentLatLng,
          s.points,
        )) return true;
      }
      return false;
    });

    if (municipalities.isNullOrEmpty)
      return Either.right(TTShared.noSeHanEncontradoMunicipiosCercanos.tt);

    return Either.left(municipalities);
  }
}
