// ignore_for_file: public_member_api_docs, sort_constructors_first
import '../_imports.dart';

class MunicipalityAppDropdownState {
  final List<MunicipalityApp> municipalities;
  final MunicipalityApp selected;
  final List<MunicipalityApp> companiesInTheSameMunicipality;

  MunicipalityAppDropdownState(this.municipalities, this.selected)
      : companiesInTheSameMunicipality =
            selected.getCompaniesInTheSameMunicipality(municipalities);

  MunicipalityAppDropdownState copyWith({
    List<MunicipalityApp>? municipalities,
    MunicipalityApp? selected,
  }) =>
      MunicipalityAppDropdownState(
        municipalities ?? this.municipalities,
        selected ?? this.selected,
      );
}

class MunicipalityAppDropdown extends StatelessWidget {
  final MunicipalityAppDropdownState state;
  final void Function(MunicipalityApp) onChanged;

  const MunicipalityAppDropdown({
    super.key,
    required this.state,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        OptionDropdownWidgetV3<MunicipalityApp>(
          title: TTShared.municipio.tt,
          icon: FontAwesomeIcons.locationDot,
          autoselectionRemoved: [],
          isDense: true,
          isAutofillOneOption: true,
          isEditable: true,
          onSelected: (s) {
            if (s == null) return;
            onChanged(s);
          },
          builder: null,
          getTextFilter: (e) => e.filter,
          getDisplayValue: (e) => e.name,
          isEquals: (e1, e2) => e1.municipality?.id == e2.municipality?.id,
          options: state.municipalities.getUniques(
            isEquals: (m1, m2) => m1.municipality?.id == m2.municipality?.id,
          ),
          selection: state.selected,
        ),
        if (state.companiesInTheSameMunicipality.isGreaterOne)
          OptionDropdownWidgetV3<MunicipalityApp>(
            title: TTShared.proyecto.tt,
            icon: FontAwesomeIcons.city,
            autoselectionRemoved: [],
            isDense: true,
            isAutofillOneOption: true,
            isEditable: true,
            onSelected: (s) {
              if (s == null) return;
              onChanged(s);
            },
            builder: null,
            getTextFilter: (e) => e.company?.name ?? e.filter,
            getDisplayValue: (e) => e.company?.name ?? e.name,
            isEquals: (e1, e2) =>
                e1.municipality?.id == e2.municipality?.id &&
                e1.company?.id == e2.company?.id,
            options: state.companiesInTheSameMunicipality,
            selection: state.selected,
          ),
      ],
    );
  }
}
