import '../_imports.dart';

/// Versión stateless basada en claves.
class OptionDropdownWidgetV3<O> extends StatelessWidget {
  final bool isEditable;
  final O? selection;
  final List<O> options;
  final String Function(O o) getDisplayValue;
  final String Function(O o) getTextFilter;
  final Widget Function(O o)? builder;
  final bool Function(O o1, O o2) isEquals;
  final void Function(O? o) onSelected;
  final String title;
  final IconData? icon;
  final bool isDense;
  final double? itemHeight;
  final bool isAutofillOneOption;

  /// Este parámetro es un parche para solucionar un problema que hay con el widget.
  ///
  /// El problema se debe a que cuando hay 1 solo elemento y si autoselecciona, no se puede borrar.
  /// Al borrarlo se vuelve a seleccionar. Al ser un stateless no se puede guardar datos.
  ///
  /// El parámetor es un listado para que pueda compartirse entre otros widget en la misma página.
  final List<Type> autoselectionRemoved;

  OptionDropdownWidgetV3({
    required this.selection,
    required this.options,
    required this.title,
    required this.onSelected,
    required this.getDisplayValue,
    required this.getTextFilter,
    this.builder,
    required this.isEquals,
    required this.autoselectionRemoved,
    this.icon,
    this.isDense = true,
    this.isEditable = true,
    this.itemHeight,
    this.isAutofillOneOption = true,
  }) {
    // Si solo hay un elemento en la lista, lo marcamos como seleccionado.
    if (isAutofillOneOption &&
        options.length == 1 &&
        // Si se eliminó previamente, no se vuelve a seleccionar.
        !autoselectionRemoved.contains(runtimeType)) {
      onSelected(options.first);
      // Se estaba generando un bucle infinito cuando la página se reconstruía en el onSelected y no añadía el runtime al listado.
      // Lo añadimos manualmente para evitar este problema.
      autoselectionRemoved.add(runtimeType);
    }
  }

  @override
  Widget build(BuildContext context) {
    return CardButtonItemList(
      height: isDense ? 65 : 35,
      // Sin esto falla al añadirlo a una columna.
      width: double.maxFinite,
      color: isEditable ? themeColors.white : themeColors.grey2,
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          icon == null
              ? SizedBox()
              : Padding(
                  padding: EdgeInsets.only(right: isDense ? 15 : 2),
                  child: Icon(icon),
                ),
          Visibility(
            visible: !isDense,
            child: Expanded(
              child: !_isValueValid()
                  ? BodyText(title, color: themeColors.grey6)
                  : BodyText(
                      selection == null ? "" : getDisplayValue(selection!)),
            ),
          ),
          Visibility(
            visible: isDense,
            child: Expanded(
                child: !_isValueValid()
                    ? H1Text(title)
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Flexible(
                            flex: 1,
                            child: SmallText(
                              title.toUpperCase(),
                              color: themeColors.grey6,
                              maxLines: 1,
                            ),
                          ),
                          Flexible(
                            flex: 3,
                            child: H3Text(
                              selection == null
                                  ? ""
                                  : getDisplayValue(selection!),
                              color: themeColors.black,
                            ),
                          ),
                        ],
                      )),
          ),
          !isEditable
              ? SizedBox()
              : Container(
                  width: isDense ? 40 : 20,
                  child: Icon(
                    FontAwesomeIcons.caretDown,
                    color: themeColors.grey6,
                    size: isDense ? null : 22,
                  ),
                )
        ],
      ),
      onPressed: isEditable
          ? () {
              if (FocusScope.of(context).hasFocus) {
                FocusScope.of(context).unfocus();
              }
              _onPressed();
            }
          : null,
    );
  }

  bool _isValueValid() {
    return selection != null;
  }

  Future<void> _onPressed() async {
    if (isListNullOrEmpty(options)) {
      await DialogService()
          .showInfoDialog(tt(TTShared.noSeHanEncontradoResultados));
    }
    // Aunque tenga una única opción, comprobamos que haya seleccionado alguna para no bloquear la selección.
    // Esto puede darse cuando el widget empieza en un estado no editable y luego se actualiza a editable.
    else if (selection != null && options.length == 1) {
      await DialogService()
          .showInfoDialog(tt(TTShared.noExistenMasOpcionesSeleccionables));
    } else {
      await DialogService().showSelectWidgetDialog<O>(
        selection: selection,
        options: options,
        onSelected: (o) {
          // Cuando la opción es una y está marcado como autoselección,
          // lo añadimos para que no se vuelva a seleccionar.
          if (o == null && options.isOne && isAutofillOneOption)
            autoselectionRemoved.add(runtimeType);
          else
            autoselectionRemoved.remove(runtimeType);
          onSelected(o);
        },
        isEquals: isEquals,
        getTextFilter: getTextFilter,
        builder: builder,
        getDisplayValue: getDisplayValue,
        isClosedOnSelect: true,
        itemHeight: itemHeight,
      );
    }
  }
}
