import '_imports.dart';

class FloatingTabPage extends StatefulWidget {
  ProviderBaseV2? provider;
  Widget Function() getPage;
  List<AppMenuItemPermission> Function() getTabMenu;
  Widget? Function()? getDrawer = () => null;
  Widget? Function()? getEndDrawer = () => null;
  PreferredSizeWidget? Function()? getAppBar = () => null;
  Widget Function(AppMenuItemPermission e) getContent;

  Stream stream;

  FloatingTabPage({
    super.key,
    this.getDrawer,
    this.getEndDrawer,
    this.getAppBar,
    required this.getTabMenu,
    required this.provider,
    required this.getPage,
    required this.stream,
    required this.getContent,
  });

  @override
  State<FloatingTabPage> createState() => FloatingTabPageState();
}

class FloatingTabPageState extends State<FloatingTabPage> {
  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
      stream: widget.stream,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        return Scaffold(
          drawer: widget.getDrawer?.call(),
          endDrawer: widget.getEndDrawer?.call(),
          appBar: widget.getAppBar?.call(),
          body: Stack(
            alignment: Alignment.topCenter,
            children: [
              widget.getPage(),
              FloatingTabPageNavigator(
                provider: widget.provider,
                getTabMenu: widget.getTabMenu,
                getContent: widget.getContent,
              ),
            ],
          ),
        );
      },
    );
  }
}

class FloatingTabPageNavigator extends StatefulWidget {
  ProviderBaseV2? provider;
  List<AppMenuItemPermission> Function() getTabMenu;
  Widget Function(AppMenuItemPermission e) getContent;
  FloatingTabPageNavigator(
      {required this.getTabMenu,
      required this.provider,
      required this.getContent,
      super.key});
  @override
  State<FloatingTabPageNavigator> createState() =>
      FloatingTabPageNavigatorState();
}

class FloatingTabPageNavigatorState extends State<FloatingTabPageNavigator> {
  @override
  Widget build(BuildContext context) {
    return IntrinsicWidth(
      child: IntrinsicHeight(
        child: Container(
          margin: const EdgeInsets.all(7.5),
          decoration: BoxDecoration(
            color: themeColors.primaryBlue,
            borderRadius: BorderRadius.circular(5),
          ),
          child: Padding(
            padding: const EdgeInsets.all(2),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: widget
                  .getTabMenu()
                  .map(
                    (e) => widget.getContent(e),
                  )
                  .toList(),
            ),
          ),
        ),
      ),
    );
  }
}
