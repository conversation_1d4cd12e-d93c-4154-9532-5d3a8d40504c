import '_imports.dart';

class TranslationProvider {
  final _onChange = StreamCustom<Null>();
  Stream<Null> get onChange => _onChange.stream;

  void showLanguagueSelection(List<LanguageType> languagesAvailable) async {
    List<String> finalLanguages = [];

    languageTypeMap.forEach((k, v) {
      if (languagesAvailable.contains(k)) finalLanguages.add(v);
    });

    if (isListNullOrEmpty(finalLanguages)) return;

    LanguageType? selectedLanguage;

    await DialogService().showSelectDialog(
      items: finalLanguages,
      getCurrentValue: () =>
          languageTypeMap[TranslationServiceGlobal().language!],
      onChange: (index, value) {
        languageTypeMap.forEach((k, v) {
          if (value == v) {
            selectedLanguage = k;
            return;
          }
        });
      },
    );

    if (selectedLanguage == null) return;

    await DialogService().showProgressDialog((dialog, context) async {
      await TranslationServiceGlobal().setLanguage(selectedLanguage!);
    });
    _onChange.sink(null);
  }

  void setLanguage(LanguageType language) {
    TranslationServiceGlobal().setLanguage(language);
    _onChange.sink(null);
  }
}

class TranslationPage extends StatelessWidget {
  final TranslationProvider provider;
  final Stream? additionalRebuild;
  final WidgetBuilder builder;

  const TranslationPage({
    super.key,
    required this.provider,
    this.additionalRebuild,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return Provider(
      create: (_) => provider,
      child: StreamBuilder(
        stream: provider._onChange.stream,
        builder: (_, __) => StreamBuilder(
          stream: additionalRebuild ?? const Stream.empty(),
          builder: (ctxt, ___) => builder(ctxt),
        ),
      ),
    );
  }
}
