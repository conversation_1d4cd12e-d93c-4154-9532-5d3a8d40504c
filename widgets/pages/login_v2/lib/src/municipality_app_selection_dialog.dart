import 'package:ecoidentificacion_iot_app/ecoidentificacion_iot_app.dart';

import '_imports.dart';

class MunicipalityAppSelectionDialog extends StatefulWidget {
  final List<SSORole> roles;
  final List<MunicipalityApp> municipalities;
  final void Function(MunicipalityApp m, SSORole r) onConfirm;

  MunicipalityAppSelectionDialog({
    required this.roles,
    required this.onConfirm,
    required this.municipalities,
  });

  @override
  _MunicipalityAppSelectionDialogState createState() =>
      _MunicipalityAppSelectionDialogState();
}

class _MunicipalityAppSelectionDialogState
    extends State<MunicipalityAppSelectionDialog> {
  late MunicipalityApp selectedMunicipality = widget.municipalities.first;
  late SSORole selectedRole = widget.roles.first;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvoked: (_) async {
        final isConfirm = await DialogService()
            .showConfirmDialog(TTShared.quieresCancelarLaOperacionPregunta.tt);

        if (!isConfirm) return;

        NavigatorService().pop();
      },
      child: AlertDialog(
        shape: themeContainers.shapeCardAndDialog,
        elevation: themeElevations.card,
        backgroundColor: themeColors.white,
        title: Text(TTSSO.seleccionaUnMunicipio.tt),
        contentPadding: const EdgeInsets.all(16),
        content: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height,
            maxWidth: 500,
            minWidth: 300,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MunicipalityAppDropdown(
                state: MunicipalityAppDropdownState(
                  widget.municipalities,
                  selectedMunicipality,
                ),
                onChanged: (m) {
                  selectedMunicipality = m;
                  setState(() {});
                },
              ),
              if (widget.roles.isGreaterOne)
                OptionDropdownWidgetV3<SSORole>(
                  title: TTShared.usuario.tt,
                  icon: FontAwesomeIcons.user,
                  autoselectionRemoved: [],
                  isDense: true,
                  isAutofillOneOption: true,
                  isEditable: true,
                  onSelected: (s) {
                    if (s == null) return;
                    selectedRole = s;
                  },
                  builder: null,
                  getTextFilter: (e) => e.translation,
                  getDisplayValue: (e) => e.translation,
                  isEquals: (e1, e2) => e1.id == e2.id,
                  options: widget.roles,
                  selection: selectedRole,
                ),
              const SizedBox(height: 26),
              Row(
                children: [
                  Container(
                    width: 120,
                    height: 40,
                    child: GenericButton(
                      title: tt(TTShared.cancelar),
                      color: themeColors.grey6,
                      onPressed: () {
                        NavigatorService().pop();
                      },
                    ),
                  ),
                  const Spacer(),
                  Container(
                    width: 150,
                    height: 40,
                    child: GenericButton(
                      title: tt(TTShared.confirmar),
                      color: themeColors.secondaryGreen,
                      onPressed: () {
                        widget.onConfirm(selectedMunicipality, selectedRole);
                        NavigatorService().pop();
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _AccessWidget extends StatelessWidget {
  final AccessModel access;

  _AccessWidget(this.access);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              IconSubtitle(FontAwesomeIcons.building),
              Expanded(
                child: Text(
                  access.company.name,
                  maxLines: 3,
                  style: const TextStyle(fontSize: 12),
                ),
              )
            ],
          ),
          const SizedBox(height: 5),
          Row(
            children: [
              IconSubtitle(FontAwesomeIcons.tag),
              Expanded(
                child: Text(
                  access.role?.name ?? "",
                  style: const TextStyle(fontSize: 12),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
