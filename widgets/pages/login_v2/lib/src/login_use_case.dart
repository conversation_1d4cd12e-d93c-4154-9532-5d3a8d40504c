import '_imports.dart';

class LoginUseCase {
  static const _tag = "LoginUseCase";

  final loadingStream = StreamCustomValue<bool>(false);

  TicketGetResult? ticketResult;
  AccessModel? selectedAccess;

  String passwordEncriptedForSave = "";

  final SSOApplication app;
  final bool Function() isValidTicket;
  final void Function() pushLoginPage;
  final PreferenceBase<String> autologinUserEmail;
  final PreferenceBase<String> autologinUserPassword;
  final PreferenceBase<int> autologinSSOCompanyId;
  final PreferenceBase<int> autologinSSORoleId;
  final TicketUseCase ticketUseCase;
  final SSOUseCase ssoUseCase;
  final ConnectionServiceBase connection;
  final int applicationId;
  final void Function(TicketModel? ticket) setTicket;
  final void Function() onCompletedLogin;
  final Future<Either<UserBase, String>> Function() getLoggedUser;

  /// Permite personalizar el diálogo para no usar el que se muestra por defecto.
  final Future<void> Function(Future<Result<String>> Function() loging)?
      showLoginProgressDialog;

  /// Permite personalizar el diálogo para no usar el que se muestra por defecto.
  final Future<bool> Function(TicketUseCase ticketUseCase, String email)?
      showDoubleFactorDialog;

  /// Permite personalizar el diálogo para no usar el que se muestra por defecto.
  final Future<AccessModel> Function(List<AccessModel> access)?
      showSelectionAccessDialog;

  LoginUseCase({
    required this.app,
    required this.isValidTicket,
    required this.pushLoginPage,
    required this.autologinUserEmail,
    required this.autologinUserPassword,
    required this.autologinSSOCompanyId,
    required this.autologinSSORoleId,
    required this.ticketUseCase,
    required this.ssoUseCase,
    required this.connection,
    required this.applicationId,
    required this.setTicket,
    required this.onCompletedLogin,
    required this.getLoggedUser,
    this.showLoginProgressDialog,
    this.showDoubleFactorDialog,
    this.showSelectionAccessDialog,
  });

  Future<void> tryAutologin(String ticketId) async {
    await _loginWithTicketId(ticketId);

    if (!isValidTicket()) {
      Log().debug(
          "[$_tag] [tryAutologin] El ticket no es válido tras _loginWithTicket, intentando _loginWithTicketCached");
      await _loginWithTicketCached();
    }

    if (!isValidTicket()) {
      Log().debug(
          "[$_tag] [tryAutologin] El ticket no es válido tras _loginWithTicketCached, intentando _loginWithPasswordEncripted");
      final email = await autologinUserEmail.get();
      final passwordMd5 = await autologinUserPassword.get();
      final companyIdSSO = await autologinSSOCompanyId.get();
      final roleIdSSO = await autologinSSORoleId.get();

      if (email.isNotEmpty &&
          passwordMd5.isNotEmpty &&
          companyIdSSO > 0 &&
          roleIdSSO > 0) {
        Log().info(
            "[$_tag] [tryAutologin] Identificandose con preferencias $email $companyIdSSO $roleIdSSO");
        await _loginWithPasswordEncripted(
          email,
          passwordMd5,
          companyIdSSO <= 0 ? null : companyIdSSO,
          roleIdSSO <= 0 ? null : roleIdSSO,
        );
      }
    }

    if (!isValidTicket() ||
        (ticketResult?.isRequiredAccessSelection ?? false)) {
      pushLoginPage();
    } else {
      Log().debug("[$_tag] [tryAutologin] El ticket es válido");
      bool canContinue = true;
      if ((ticketResult?.isRequiredDoubleFactor ?? false)) {
        Log().debug("[$_tag] [tryAutologin] Requiere doble factor");
        canContinue = await _showDoubleFactorDialog(
            ticketResult!.ticket!.user?.email ?? "");
      }

      if (canContinue) {
        final result = await getLoggedUser();

        if (result.isRight || !(result.left?.isLogin() ?? false)) {
          Log().debug(
              "[$_tag] [tryAutologin] getLoggedUser no es válido, cargando loginPage");
          result.left?.logout();
          pushLoginPage();
        } else {
          onCompletedLogin();
        }
      } else {
        pushLoginPage();
      }
    }
  }

  Future<void> _loginWithTicketId(String ticketId) async {
    // Evitamos llamar a [getWithTicketId] porque eliminará el ticket de la caché
    // y lo necesitamos para el autologin cuando el usuario accede desde MyMovisat recuperando el ticket en caché.
    if (ticketId.trim().isEmpty) return;

    // Eliminamos las credenciales anteriores si se ha indicado un ticketId en la URL.
    await autologinUserEmail.set("");
    await autologinUserPassword.set("");
    await autologinSSOCompanyId.set(-1);
    await autologinSSORoleId.set(-1);

    Log().debug(
        "[$_tag] [_loginWithTicketId] Identificandose con ticket $ticketId");

    final result = await ticketUseCase.getWithTicketId(
      ticketId: ticketId,
      hasInternet: () => connection.forceCheckInternetState(),
    );

    Log().debug("[$_tag] [_loginWithTicketId] Resultado: ${result.left}");

    if (result.isRight || isNull(result.left?.ticket?.user?.id)) {
    } else {
      final t = result.left!.ticket!;
      setTicket(t);
      Log().info(
          "[$_tag] [_loginWithTicketId] Ticket obtenido del usuario: ${t.user!.id}");
    }
  }

  Future<void> _loginWithTicketCached() async {
    // This will result in the user's data not being updated until they logout.
    final ticket = await ticketUseCase
        .getFromCache(() => connection.forceCheckInternetState());
    Log().debug(
        "[$_tag] [_loginWithTicketCached] Ticket recuperado de cache $ticket");

    if (ticket?.user?.id == null) return;

    setTicket(ticket);
  }

  Future<void> loginWithEmail(
      String email, String password, bool isFirstIteration) async {
    if (isFirstIteration) clean();

    if (isNotNull(showLoginProgressDialog)) {
      await showLoginProgressDialog!(
        () => _loginWithEmailProgressContent(email, password),
      );
    } else {
      bool showSendActivationMail = false;
      await DialogService().showProgressDialog(
        (dialog, context) async {
          final result = await _loginWithEmailProgressContent(email, password);
          if (result.isError && isStringFilled(result.error)) {
            // TODO: Refactorizar para evitar comparar con el texto.
            showSendActivationMail = result.error ==
                TTSSO
                    .elUsuarioNoEstaActivoRevisaTuCorreoElectronicoParaActivarlo
                    .tt;
            dialog.error(result.error);
          }
        },
        timeout: const Duration(hours: 1),
      );
      if (showSendActivationMail) {
        final resend = await DialogService().showConfirmDialog(
          TTSSO
              .quieresQueVolvamosAEnviarElCorreoDeActivacionParaQuePuedasActivarTuCuenta
              .tt,
        );
        if (resend) resendActivationMail(email, null);
      }
    }
  }

  Future<void> resendActivationMail(String email, int? companyIdSSO) async {
    final companySaved = await autologinSSOCompanyId.get();
    final result = await ticketUseCase.sendActivationMail(
        email: email,
        companyIdSSO:
            companyIdSSO ?? selectedAccess?.company.idInSSO ?? companySaved);

    if (result.isRight) {
      DialogService().showSnackbar(
          result.right ?? TTShared.haOcurridoUnError.tt,
          backgroundColor: Colors.red);
    } else {
      DialogService().showSnackbar(TTSSO.correoEnviado.tt);
    }
  }

  Future<Result<String>> _loginWithEmailProgressContent(
      String email, String password) async {
    final r = await _loginProgressDialogContent(
      email: email,
      password: password,
    );
    if (r.isError) return r;

    bool canLogin = true;

    // Si tiene varios accesos, mostramos la página de selección y lo asignamos a la variable local.
    // Volvemos a ejecutar recursivamente el método de login para que vuelva a intentarlo.
    if (ticketResult!.isRequiredAccessSelection) {
      canLogin = false;
      selectedAccess = await _showSelectionAccessDialog(ticketResult!.access);
      if (selectedAccess != null) {
        // IMPORTANTE: sin await, para que finalice la ejecución actual.
        // ignore: unawaited_futures
        loginWithEmail(email, password, false);
      }
      return Result.valid();
    }

    if (ticketResult!.isRequiredDoubleFactor)
      canLogin = await _showDoubleFactorDialog(email);

    if (canLogin) {
      final result = await getLoggedUser();
      if (result.isRight || !(result.left?.isLogin() ?? false)) {
        result.left?.logout();
        return Result.error(result.right ?? "");
      }

      await autologinUserEmail.set(email);
      await autologinUserPassword.set(passwordEncriptedForSave);
      await autologinSSOCompanyId
          .set(ticketResult?.ticket?.company?.idInSSO ?? -1);
      await autologinSSORoleId.set(ticketResult?.ticket?.role?.id ?? -1);

      onCompletedLogin();
    }

    return Result.valid();
  }

  Future<bool> _showDoubleFactorDialog(String email) async {
    bool isValid = false;
    if (showDoubleFactorDialog != null) {
      isValid = await showDoubleFactorDialog!(ticketUseCase, email);
    } else {
      await DialogService().showCustomDialog(
        DoubleFactorDialog(
            loadingStream: loadingStream,
            onSetText: (v) {
              v = v.trim();
              if (isStringNullOrEmpty(v))
                return DialogService()
                    .showSnackbar(TTShared.elCodigoNoEsValido.tt);
              int? code = int.tryParse(v);
              if (isNullLesserOrEqualZero(code))
                return DialogService()
                    .showSnackbar(TTShared.elCodigoNoEsValido.tt);
              loadingStream.sink(true);

              ticketUseCase.doubleFactorVerify(code!, email).then((value) {
                isValid = value;
                if (isValid) {
                  // Cerramos el diálogo de doble factor.
                  NavigatorService().pop();
                } else {
                  DialogService().showSnackbar(TTShared.elCodigoNoEsValido.tt);
                }
                loadingStream.sink(false);
              });
            }),
        true,
      );
    }

    return isValid;
  }

  Future<AccessModel?> _showSelectionAccessDialog(
      List<AccessModel> access) async {
    AccessModel? a;
    if (showSelectionAccessDialog != null) {
      a = await showSelectionAccessDialog!(access);
    } else {
      await DialogService().showCustomDialog(
        AccessSelectionDialog(
            access: access,
            onPressedConfirm: (v) {
              a = v;
              NavigatorService().pop();
            }),
        true,
      );
    }

    return a;
  }

  Future<Result<String>> _loginProgressDialogContent({
    required String email,
    required String password,
  }) async {
    Result<Failure<TicketLoginError>?>? result;
    try {
      result = await _getLoginResult(
        email,
        password,
        selectedAccess?.company.idInSSO,
        selectedAccess?.role?.id,
      );
    } catch (e) {
      Log().error("[$_tag] [_loginProgressDialogContent] Error al loggear: $e");
    }

    if (result == null) return TTShared.haOcurridoUnError.tt.toResult();

    if (result.isError) return Result.error(result.error?.message ?? "");

    return Result.valid();
  }

  Future<Result<Failure<TicketLoginError>?>?> _getLoginResult(
    String email,
    String passsword,
    int? companyIdSSO,
    int? roleId,
  ) async {
    Result<Failure<TicketLoginError>?> result;
    try {
      // El SSO ha tenido que cambiar la encriptación de la contraseña.
      // Se intenta realizar login con ambas encriptaciones para pertir el acceso a los antiguos usuarios.
      result = await _loginWithPasswordEncripted(
        email,
        CryptoUtil.sha256(passsword),
        companyIdSSO,
        roleId,
      );

      if (result.isValid) return result;
      if (result.error?.code == null) return result;

      switch (result.error!.code!) {
        // Si dan estos errores, probamos con la antigua encriptación.
        case TicketLoginError.emailOrPassNotValid:
          return await _loginWithPasswordEncripted(
            email,
            CryptoUtil.md5(passsword),
            companyIdSSO,
            roleId,
          );
        case TicketLoginError.noAppAccess:
          if (app.hasCitizenUsers) {
            final result2 = await createAccessMunicipality(
              app: app,
              ssoUseCase: ssoUseCase,
              email: email,
            );

            if (result2 == null) return result;
            if (result2.error != null) result2.error!.toResult();

            return _getLoginResult(
              email,
              passsword,
              result2.municipality!.companyIdSSO,
              result2.role!.id,
            );
          }

          return result;
        case TicketLoginError.generic:
        case TicketLoginError.userRemoved:
        case TicketLoginError.userNotVerified:
        case TicketLoginError.passNotValid:
        case TicketLoginError.userBlocked:
          return result;
      }
    } catch (e) {
      Log().error("[$_tag] [_getLoginResult] Error al loggear: $e");
    }

    return null;
  }

  static Future<CreateAccessMunicipalityReturn?> createAccessMunicipality({
    required SSOApplication app,
    required SSOUseCase ssoUseCase,
    required String email,
    int? currentMunicipaliyIdSSO,
  }) async {
    if (!app.hasCitizenUsers) return null;

    final municipalities = await ssoUseCase.getMunicipalitiesApp();

    if (currentMunicipaliyIdSSO != null)
      municipalities.removeWhere(
        (m) => m.companyIdSSO == currentMunicipaliyIdSSO,
      );

    if (municipalities.isEmpty)
      return CreateAccessMunicipalityReturn(
          error: TTSSO.noSeHanEncontradoMunicipios.tt);

    final roles = SSORole.values.where2((r) => r.isCitizen && r.isFromApp(app));

    MunicipalityApp? municipality;
    SSORole? role;

    // Mostramos el diálogo si hay que seleccionar municipio o rol.
    if (municipalities.isGreaterOne || roles.isGreaterOne) {
      await DialogService().showCustomDialog(
        MunicipalityAppSelectionDialog(
          roles: roles,
          onConfirm: (m, r) {
            municipality = m;
            role = r;
          },
          municipalities: municipalities,
        ),
        false,
      );
    } else {
      municipality = municipalities.firstOrNull;
      role = roles.firstOrNull;
    }

    if (municipality == null) return null;
    if (role == null) return null;
    if (!municipality!.companyIdSSO.isGreaterZero) return null;

    final result = await ssoUseCase.giveAccess(
      login: StringFilled(email),
      companyIdInSSO: IntGreaterZero(municipality!.companyIdSSO),
      applicationId: IntGreaterZero(app.id),
      roleId: IntGreaterZero(role!.id),
    );

    if (result.isError)
      return CreateAccessMunicipalityReturn(error: result.error ?? "");

    return CreateAccessMunicipalityReturn(
      municipality: municipality,
      role: role,
    );
  }

  Future<Result<Failure<TicketLoginError>?>> _loginWithPasswordEncripted(
    String email,
    String passwordMd5OrSha256,
    int? companyIdSSO,
    int? roleId,
  ) async {
    if (isStringNullOrEmpty(email) || isStringNullOrEmpty(passwordMd5OrSha256))
      return Result.error(
        Failure(TicketLoginError.generic, tt(TTShared.noSeHaAsignadoUnValor)),
      );

    final result = await ticketUseCase.getWithCredentials(
      hasInternet: () => connection.forceCheckInternetState(),
      email: email,
      passwordSha256: passwordMd5OrSha256,
      applicationId: applicationId,
      companyIdSSO: selectedAccess?.company.idInSSO ?? companyIdSSO,
      roleId: selectedAccess?.role?.id ?? roleId,
    );

    if (result.isRight) {
      if (isStringFilled(result.right!.message))
        return Result.error(result.right);
      return Result.error(Failure(
        result.right!.code,
        tt(TTShared.haOcurridoUnError),
      ));
    }

    // Si require seleccionar accesos por tener varios, no continuamos para que no valide el ticket.
    if (result.left!.isRequiredAccessSelection) {
      ticketResult = result.left;
      return Result.valid();
    }

    if (isNullOrLesserZero(result.left?.ticket?.role?.id)) {
      return Result.error(Failure(
        TicketLoginError.noAppAccess,
        tt(TTShared.noTienesAccesoAEstaAplicacion),
      ));
    }

    setTicket(result.left!.ticket);
    ticketResult = result.left;

    passwordEncriptedForSave = passwordMd5OrSha256;

    return Result.valid();
  }

  void dispose() {
    loadingStream.close();
  }

  void clean() {
    ticketResult = null;
    selectedAccess = null;
  }
}

class CreateAccessMunicipalityReturn {
  final String? error;
  final MunicipalityApp? municipality;
  final SSORole? role;

  CreateAccessMunicipalityReturn({
    this.error,
    this.municipality,
    this.role,
  });
}
