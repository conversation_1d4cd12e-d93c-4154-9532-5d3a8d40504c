import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:log/log.dart';
import 'package:service_interface/service_interface.dart';
import 'package:translation_service_global/translation_service_global.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:flutter/rendering.dart';
import 'dart:typed_data';

class DialogQRWidget extends StatelessWidget {
  final GlobalKey _qrKey = GlobalKey();

  final String name;
  final String data;
  final ImageServiceBase imageService;
  final void Function(bool isSaved)? onFinished;

  DialogQRWidget({
    required this.name,
    required this.data,
    required this.imageService,
    this.onFinished,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 10),
        Text(
          TTShared.codigoQrDe.tt + ": " + name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 10),
        RepaintBoundary(
          key: _qr<PERSON><PERSON>,
          child: QrImageView(
            data: data,
            size: 200,
            backgroundColor: Colors.white,
          ),
        ),
        const SizedBox(height: 20),
        ElevatedButton.icon(
          onPressed: () => _saveQRToGallery(context, _qrKey),
          icon: const Icon(Icons.download),
          label: Text(TTShared.descargarQr.tt),
        ),
      ],
    );
  }

  /// Captura el widget del QR y lo guarda en la galería
  Future<void> _saveQRToGallery(BuildContext context, GlobalKey qrKey) async {
    try {
      RenderRepaintBoundary boundary =
          qrKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage();
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null)
        throw Exception("Error al convertir la imagen a bytes");

      Uint8List pngBytes = byteData.buffer.asUint8List();
      if (pngBytes.isEmpty)
        throw Exception(
            "byteData.buffer.asUint8List() ha generado un listado vacío");

      final r = await imageService.downloadImage(pngBytes,
          StringUtil.toFileName(TTShared.codigoQrDe.tt + ": " + name));

      if (!r) throw Exception("No se ha guardado la imagen");

      onFinished?.call(true);
      Navigator.pop(context);
    } catch (e) {
      Log().error("[DialogQRWidget] [_saveQRToGallery] $e");
      onFinished?.call(false);
    }
  }
}
