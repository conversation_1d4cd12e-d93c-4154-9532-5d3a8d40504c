import '../_imports.dart';

class MapCenterData {
  final LatLng? position;
  final double? zoom;
  final bool isHome;

  MapCenterData({
    required this.position,
    required this.zoom,
    this.isHome = false,
  });

  factory MapCenterData.withCurrentZoom(LatLng position) =>
      MapCenterData(position: position, zoom: null);

  factory MapCenterData.withElementZoom(LatLng position) =>
      MapCenterData(position: position, zoom: MapPrefs().centerElementZoom);

  factory MapCenterData.withStreetZoom(LatLng position) =>
      MapCenterData(position: position, zoom: MapPrefs().centerStreetZoom);

  factory MapCenterData.withAreaZoom(LatLng? position) =>
      MapCenterData(position: position, zoom: MapPrefs().centerAreaZoom);

  factory MapCenterData.withCustomZoom(LatLng position, double zoom) =>
      MapCenterData(position: position, zoom: zoom);

  factory MapCenterData.withDefault() => MapCenterData(
        position: LatLng(Environment().mapStartLat, Environment().mapStartLong),
        zoom: MapPrefs().centerAreaZoom,
      );

  factory MapCenterData.home() => MapCenterData(
        // Estos datos no importan porque se cogerá la posición y zoom de la preferencia de casa.
        position: LatLng(Environment().mapStartLat, Environment().mapStartLong),
        zoom: MapPrefs().centerAreaZoom,
        isHome: true,
      );

  bool get isValid =>
      position != null && position!.latitude != 0 && position!.longitude != 0;
}
