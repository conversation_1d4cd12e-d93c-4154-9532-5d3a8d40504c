import '../_imports.dart';

class NavigatorService {
  String get currentRoute {
    return Get.currentRoute;
  }

  void pop() => Get.back(canPop: true);

  void popConsideringWillPopScore() => Get.key.currentState?.maybePop();

  bool canPop() => Navigator.canPop(Get.context!);

  Future<void>? push(Widget page) => Get.to(page);

  Future<void>? pushWithNoAnimation(Widget page) =>
      Get.to(page, transition: Transition.noTransition);

  void pushHideDrawer(Widget page) {
    closeDrawer();
    push(page);
  }

  /// Método para cerrar el menú lateral
  void closeDrawer() => Get.back(canPop: true);

  void pushReplacement(Widget page) {
    popToMainPage();
    Get.off(page);
  }

  /// Vuelve hasta la página principal
  void popToMainPage() => Get.until((route) => route.isFirst);

  void popCount(int count) {
    int amount = 0;

    Get.until((_) => amount++ >= count);
  }

  void popAndPush(Widget page) {
    pop();
    push(page);
  }

  /*
   * Singleton factory
   */
  static NavigatorService? _singleton;
  factory NavigatorService() => _singleton ??= NavigatorService._internal();
  NavigatorService._internal();
}
