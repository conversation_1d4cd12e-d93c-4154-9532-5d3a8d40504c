import '../_imports.dart';

class WidgetUtil {
  static Uint8List uInt8ListFromBase64(String image) {
    return Base64Decoder().convert(image);
  }

  static Future<Uint8List> getBytesFromAsset(String asset) async {
    if (asset.isEmpty) return Uint8List(0);

    final data = await rootBundle.load(asset);
    return data.buffer.asUint8List();
  }

  static String? boolToText(bool? value) =>
      isTrue(value) ? tt(TTShared.si) : tt(TTShared.no);

  static Future<String?> getImageBase64Multiplatform(
      ImageServiceBase imageService) async {
    ImageData? image;
    // En las PWA, todavía no se puede obtener imágenes desde la cámara.
    if (!isMobile) {
      image = await imageService.getGalleryImage();
    } else {
      int selectionType = 0;

      await DialogService().showOptionDialog([
        OptionButton(
          icon: FontAwesomeIcons.camera,
          onPressed: () async {
            selectionType = 1;
          },
          text: tt(TTShared.camara),
          radius: 0,
        ),
        OptionButton(
          icon: FontAwesomeIcons.images,
          onPressed: () async {
            selectionType = 2;
          },
          text: tt(TTShared.galeria),
          radius: 0,
        )
      ]);

      if (selectionType == 1)
        image = await imageService.getCameraImage();
      else if (selectionType == 2) image = await imageService.getGalleryImage();
    }

    if (image == null) return null;

    String? base64;
    if (image.isFile)
      base64 = WidgetUtil.fileToBase64(image.file!);
    else if (image.isBytes) base64 = WidgetUtil.uInt8ListToBase64(image.bytes!);
    return base64;
  }

  static String? uInt8ListToBase64(Uint8List data) {
    try {
      final List<int> intData = List<int>.from(data);
      return base64Encode(intData);
    } catch (e) {
      Log().error("No se ha podido convertir Uint8List en Base64: $e");
    }
    return null;
  }

  static String? bytesToBase64(List<int> data) {
    try {
      return base64Encode(data);
    } catch (e) {
      Log().error("No se ha podido convertir bytes en Base64: $e");
    }
    return null;
  }

  static String uuidToTextView(String uuid) {
    if (isStringNullOrEmpty(uuid)) return uuid;
    final parts = uuid.split("-");
    if (isListNullOrEmpty(parts)) return uuid;
    return parts.first;
  }

  static String uuidToTextViewV2(String uuid) {
    if (isStringNullOrEmpty(uuid)) return uuid;
    return base64Encode(uuid.codeUnits);
  }

  static String? fileToBase64(File file) {
    List<int> imageBytes = file.readAsBytesSync();
    return base64Encode(imageBytes);
  }

  static File? fileFromBase64({
    required String base64,
    required String fileName,
  }) {
    try {
      final decodedBytes = base64Decode(base64);
      final file = File(fileName);
      file.writeAsBytesSync(decodedBytes);
      return file;
    } catch (e) {
      Log().error(
          "[fileFromBase64] No se ha podido convertir base64 en File ($fileName): $e");
      return null;
    }
  }

  /// Indica si la página actual es visible.
  ///
  /// Cuando el drawer está visible devuelve false.
  static bool isParentPageVisible(BuildContext context) {
    return ModalRoute.of(context)!.isCurrent;
  }

  static void rebuildAllChildren(BuildContext context) {
    void rebuild(Element el) {
      el.markNeedsBuild();
      el.visitChildren(rebuild);
    }

    (context as Element).visitChildren(rebuild);
  }

  static void removeCurrentInputFocus(BuildContext context) =>
      FocusScope.of(context).requestFocus(FocusNode());

  static void cleanFocus(BuildContext context) {
    if (FocusScope.of(context).hasFocus) {
      FocusScope.of(context).unfocus();
    }
  }

  static void closeApp() {
    if (Platform.isAndroid) {
      SystemChannels.platform.invokeMethod<void>('SystemNavigator.pop', false);
    } else if (Platform.isIOS) {
      exit(0);
    }
  }

  static String? getDistanceText(double distanceInMeters, [int? maxDistance]) {
    if (isNullOrLesserZero(distanceInMeters)) return tt(TTShared.desconocido);

    final rounded = distanceInMeters.round();

    if (maxDistance != null && distanceInMeters > maxDistance)
      return "+${maxDistance}m";

    return rounded.toString() + "m";
  }

  static String distanceMetersView(int? distanceInMeters) {
    if (isNullOrLesserZero(distanceInMeters)) return "";
    if (distanceInMeters! < 1000)
      return "$distanceInMeters ${tt(TTShared.metros).toLowerCase()}";
    final kms = distanceInMeters / 1000;
    String kmsString = kms.toStringAsFixed(1);
    if (kms % 1 == 0) kmsString = kms.round().toString();
    return "$kmsString ${tt(TTShared.kilometros).toLowerCase()}";
  }

  static void closeDrawer(BuildContext context) {
    ScaffoldState? sf;
    try {
      sf = Scaffold.of(context);
    } catch (e) {}

    if (sf == null) return;
    if (sf.hasDrawer && sf.isDrawerOpen)Scaffold.of(context).closeDrawer();
    if (sf.hasEndDrawer && sf.isEndDrawerOpen)
      Scaffold.of(context).closeEndDrawer();
  }

  // static Rect getWidgetGlobalRect(GlobalKey key) {
  //   RenderBox renderBox = key.currentContext.findRenderObject();
  //   var offset = renderBox.localToGlobal(Offset.zero);
  //   return Rect.fromLTWH(
  //       offset.dx, offset.dy, renderBox.size.width, renderBox.size.height);
  // }

  // static Offset calculateOffset(BuildContext context) {
  //   double dx = _showRect.left + _showRect.width / 2.0 - menuWidth() / 2.0;
  //   if (dx < 10.0) {
  //     dx = 10.0;
  //   }

  //   if (dx + menuWidth() > _screenSize.width && dx > 10.0) {
  //     double tempDx = _screenSize.width - menuWidth() - 10;
  //     if (tempDx > 10) dx = tempDx;
  //   }

  //   double dy = _showRect.top - menuHeight();
  //   if (dy <= MediaQuery.of(context).padding.top + 10) {
  //     // The have not enough space above, show menu under the widget.
  //     dy = arrowHeight + _showRect.height + _showRect.top;
  //     _isDown = false;
  //   } else {
  //     dy -= arrowHeight;
  //     _isDown = true;
  //   }

  //   return Offset(dx, dy);
  // }
}
