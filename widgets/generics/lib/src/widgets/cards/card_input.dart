import '../../_imports.dart';

class CardInput extends StatefulWidget {
  final String? id;
  final String? title;
  final String? subtitle;
  final String? regex;
  final IconData? iconRight, iconLeft;
  final String? placeholder;
  final Widget? placeholderWidget;

  final String? leftText;
  final String? rightText;
  final bool isRequired;
  final bool allowSearch;
  final FieldType fieldType;
  final dynamic value;
  final List<dynamic>? list;
  final List<CheckboxOption>? checkboxOptions;
  final void Function(bool)? onPressedIconRight;
  final void Function(dynamic)? onChange;
  final void Function(dynamic)? onChangeIndex;
  final void Function(dynamic)? onSubmit;

  final Future<ReferenciaCatastral?> Function(dynamic)? asyncValidator;

  late bool simple;
  final int? maxLength;
  final int? minLength;
  final TextInputAction? textInputAction;
  final int? lines;
  final bool readOnly;
  final bool showCounter;
  final bool denySpaces;
  final bool allowEmojis;
  final double? verticalMargin;
  final double? horizontalMargin;
  final bool upperCase;
  final bool decimal;
  final bool allowNegatives;
  final bool multiple;
  final bool showValidator;
  final bool isUser;
  final bool isTextArea;
  final bool showUnderline;
  final bool showLabel;
  final Color? iconColor;
  final bool isCard;
  final TextAlign? textAlign;
  late final bool isSmall;

  final bool showInitialValidatorIcon;
  final bool showInitialPassword;
  final bool showTogglePasswordVisibility;

  /// Función que valida el campo es valida el campo desde afuera
  final bool Function(dynamic)? isValid;
  final bool enableSuggestions;
  final TextInputType? textInputType;
  final List<String>? autofillHints;

  final TextEditingController? controller;
  final Color? backgroundColor;

  final bool validateCadastre;

  /// Bordes para el input
  /// [border] se aplica cuando el input está vacío
  final Border? border;

  /// [validBorder] se aplica cuando el input es valido
  final Border? validBorder;

  /// [errorBorder] se aplica cuando el input es invalido
  final Border? errorBorder;

  /// [borderRadius] se aplica al input
  final BorderRadiusGeometry? borderRadius;

  /// [showErrorMessage] se aplica cuando el input es invalido
  bool showErrorMessage;

  /// [textFontSize] se aplica al input
  final double? textFontSize;

  CardInput({
    super.key,
    this.id,
    this.title,
    this.onChange,
    this.onChangeIndex,
    this.onSubmit,
    this.textInputAction,
    this.regex,
    this.subtitle,
    this.iconRight,
    this.onPressedIconRight,
    this.iconLeft,
    this.placeholder,
    this.placeholderWidget,
    this.leftText,
    this.rightText,
    this.isRequired = false,
    bool? isSmall,
    this.fieldType = FieldType.text,
    this.controller,
    this.list,
    this.checkboxOptions,
    this.value,
    simple = false,
    this.maxLength,
    this.minLength,
    this.lines,
    this.verticalMargin,
    this.horizontalMargin,
    this.readOnly = false,
    this.showCounter = false,
    this.upperCase = false,
    this.allowSearch = false,
    this.denySpaces = false,
    this.showInitialValidatorIcon = false,
    this.showInitialPassword = false,
    this.showTogglePasswordVisibility = true,
    this.allowEmojis = false,
    this.decimal = false,
    this.allowNegatives = false,
    this.multiple = false,
    this.showUnderline = false,
    this.showLabel = false,
    this.isUser = false,
    this.isTextArea = false,
    this.isCard = true,
    this.enableSuggestions = true,
    this.showValidator = true,
    this.isValid,
    this.textInputType,
    this.iconColor,
    this.autofillHints,
    this.textAlign,
    this.backgroundColor,
    this.asyncValidator,
    this.validateCadastre = false,
    this.border,
    this.validBorder,
    this.errorBorder,
    this.borderRadius,
    this.showErrorMessage = true,
    this.textFontSize,
  }) : isSmall = isSmall ?? isWeb {
    this.simple = simple || (title == null && subtitle == null);
  }

  @override
  State<CardInput> createState() => _CardInputState();
}

class _CardInputState extends State<CardInput> {
  late TextInputType? keyboardType;

  // TODO - Mejorar la estructura de este componente y dividirlo en partes

  // Para el dropdown
  final TextEditingController _controller = TextEditingController();
  final GlobalKey<TooltipState> tooltipkey = GlobalKey<TooltipState>();
  late Color iconColor;

  // Para hacer validaciones
  bool validInput = true;
  bool validRepeatPassword = true;
  bool needValidation = false;
  String? errorMessage;
  bool showInitialValidatorIcon = false;
  bool passwordIsShow = false;

  // Valores actuales
  String? currentValue;
  int index = 0;
  bool currentBooleanValue = false;

  // TODO - Corregir la lista de valores a una lista de dropdownoption
  List<dynamic> currentListValues = [];
  List<int> currentListIndexValues = [];

  // Valores para las funciones asincronas
  Timer? _debounceAsync;
  bool loadingValidator = false;
  bool? preLoadingValidator;

  IconRotating iconRotating = IconRotating();

  late FocusNode _focusNode;

  ReferenciaCatastral? referenciaCatastral;

  // Convertir el tipo de campo a un tipo de teclado
  TextInputType convertType(FieldType fieldType) {
    // Retorno inmediato si se proporciona un tipo de entrada personalizado
    if (widget.textInputType != null) return widget.textInputType!;

    // Retorno para áreas de texto
    if (widget.isTextArea) return TextInputType.multiline;

    // Retorno para campos numéricos
    if (fieldType == FieldType.number) return TextInputType.number;

    // Retorno para campos decimales con opciones adicionales
    if (widget.decimal)
      return TextInputType.numberWithOptions(
        decimal: true,
        signed: widget.allowNegatives,
      );

    // Retorno para texto simple
    if (fieldType == FieldType.text) return TextInputType.text;

    // Retorno para campos de correo electrónico
    if (fieldType == FieldType.email || widget.isUser)
      return TextInputType.emailAddress;

    // Retorno para campos de teléfono
    if (fieldType == FieldType.phone) return TextInputType.phone;

    // Valor predeterminado si ninguna condición anterior se cumple
    return TextInputType.text;
  }

  // Para filtrar cuando se escribe en el input del dropdown
  void _filterList() {
    if (_controller.text != currentValue) {
      if (widget.onChange != null) widget.onChange!(null);
      currentValue = null;
    }

    List listValues = widget.list!
        .where((item) =>
            item.toLowerCase().contains(_controller.text.toLowerCase()))
        .toList();

    // Hacemos el texto buscable mas grande si la lista es muy grande
    int valorTexto = 6;
    if (listValues.length < 50) {
      valorTexto = 2;
    } else if (listValues.length < 100) {
      valorTexto = 3;
    } else if (listValues.length < 500) {
      valorTexto = 4;
    } else if (listValues.length < 1000) {
      valorTexto = 5;
    }

    if (_controller.text.length >=
            (widget.allowSearch ? (widget.minLength ?? valorTexto) : 0) ||
        listValues.length < 10) {
      setState(() {
        currentListValues = listValues;
      });
    } else {
      setState(() {
        currentListValues = [];
      });
    }
  }

  Widget generateIcon() {
    if (preLoadingValidator != null && preLoadingValidator!) {
      return Container();
    }
    if (loadingValidator) {
      // Make a loading icon animation giating the icon
      return iconRotating;
    }
    if (widget.showValidator && needValidation && showInitialValidatorIcon) {
      return validInput
          ? Icon(
              Icons.check_circle,
              color: colorValido,
            )
          : Tooltip(
              key: tooltipkey,
              message: errorMessage ?? "",
              child: GestureDetector(
                onTap: () {
                  if (!kIsWeb) tooltipkey.currentState?.ensureTooltipVisible();
                },
                child: Icon(
                  Icons.error,
                  color: widget.isRequired ? colorError : colorAlerta,
                ),
              ),
            );
    } else {
      return Container();
    }
  }

  // Validador de email
  bool validateEmail() {
    if (widget.fieldType == FieldType.email && !currentValue.isNullOrEmpty) {
      needValidation = true;
      errorMessage = "No es un correo válido";
      return StringUtil.isEmail(currentValue ?? "");
    }
    return true;
  }

  // Validador de length
  bool validateLength() {
    if (widget.minLength != null) {
      needValidation = true;

      if (!widget.isRequired &&
          currentValue.isNullOrEmpty &&
          currentListValues.isEmpty) {
        needValidation = false;
        return true;
      }

      // Si es de tipo lista
      if (widget.fieldType == FieldType.list && widget.multiple) {
        errorMessage =
            "Debe seleccionar al menos ${widget.minLength} ${widget.minLength == 1 ? 'opción' : 'opciones'}";
        return currentListValues.length >= widget.minLength!;
      }
      if (currentValue == null || currentValue == "") return false;
      errorMessage =
          "Debe tener al menos ${widget.minLength} ${widget.minLength == 1 ? 'carácter' : 'caracteres'}";
      return currentValue!.length >= widget.minLength!;
    }
    return true;
  }

  // Validate Regex
  bool validateRegex() {
    if (widget.regex != null && widget.regex!.isNotEmpty) {
      needValidation = true;
      errorMessage = "No cumple con el formato";
      return RegExp(widget.regex!, caseSensitive: false)
          .hasMatch(currentValue ?? "");
    }
    return true;
  }

// Validator Repit Password
  bool validateConfirmatePassword() {
    if (validRepeatPassword && widget.regex == null && validInput)
      errorMessage = "La contraseña no coincide";
    return true;
  }

// Valida si es de tipo numero
  bool validateNumber() {
    if (widget.fieldType == FieldType.number) {
      if (!widget.isRequired && (currentValue == null || currentValue == "")) {
        needValidation = false;
        return true;
      }
      if ((widget.decimal || widget.allowNegatives)) {
        needValidation = true;
        errorMessage = "Debe ser un número válido";
        if (widget.isRequired && (currentValue == null || currentValue == "")) {
          return false;
        }
        if (widget.decimal) {
          return double.tryParse(currentValue ?? "") != null;
        }
        return int.tryParse(currentValue ?? "") != null;
      }
    }

    return true;
  }

  bool validateDni() {
    if (widget.id?.equalsIgnoreCase('dni') == true &&
        !currentValue.isNullOrEmpty) {
      needValidation = true;
      errorMessage = 'Formato erroneo. DNI debe tener 8 dígitos y una letra.';
      return StringUtil.isDni(currentValue ?? "");
    }
    return true;
  }

  bool validateNie() {
    if (widget.id?.equalsIgnoreCase('nie') == true &&
        !currentValue.isNullOrEmpty) {
      needValidation = true;
      errorMessage =
          "Formato erroneo. NIE debe tener XYZ, 7 dígitos, y una letra.";
      return StringUtil.isNie(currentValue ?? "");
    }
    return true;
  }

  bool validateDniNie() {
    if (widget.id?.equalsIgnoreCase('dniNie') == true &&
        !currentValue.isNullOrEmpty) {
      needValidation = true;
      errorMessage =
          "Formato erroneo. DNI debe tener 8 dígitos y una letra. NIE debe tener XYZ, 7 dígitos, y una letra.";
      return StringUtil.isDni(currentValue ?? "") ||
          StringUtil.isNie(currentValue ?? "");
    }
    return true;
  }

  bool validatePhone() {
    if (widget.fieldType == FieldType.phone && !currentValue.isNullOrEmpty) {
      needValidation = true;
      errorMessage = "No cumple con el formato";
      return StringUtil.isPhone(currentValue ?? "");
    }
    return true;
  }

  bool validateCadastre() {
    if (widget.validateCadastre) {
      if (!widget.isRequired && currentValue.isNullOrEmpty) {
        needValidation = false;
        return true;
      }
      referenciaCatastral = null;
      needValidation = true;
      errorMessage = 'La referencia catastral consta de 15 dígitos y 5 letras';
      return StringUtil.isCadastre(currentValue ?? "");
    }
    return true;
  }

  void cadastreNotFound() {
    if (referenciaCatastral == null) {
      needValidation = true;
      errorMessage = 'Referencia catastral no encontrada';
    }
  }

  // Validador de required
  validateRequired() {
    if (widget.isRequired) {
      needValidation = true;
      if (widget.fieldType == FieldType.boolean) {
        return true;
      } else if (widget.fieldType == FieldType.checkbox) {
        errorMessage = "Debe seleccionar una opción";
        return currentBooleanValue;
      } else if (widget.fieldType == FieldType.list && widget.multiple) {
        errorMessage = "Debe seleccionar al menos una opción";
        return currentListValues.isNotEmpty;
      } else if (widget.fieldType == FieldType.list && !widget.multiple) {
        errorMessage = "Debe seleccionar una opción";
        return currentValue != null && currentValue != "";
      } else if (widget.fieldType == FieldType.dropdown) {
        errorMessage = "Debe seleccionar una opción";
        return currentValue != null && currentValue != "";
      } else {
        errorMessage = "Este campo es obligatorio";
        return currentValue != null && currentValue != "";
      }
    }
    return true;
  }

  bool validator() {
    errorMessage = null;

    // TODO - Revisar la legin¡bilidad y mejorar el initial validator
    if (showInitialValidatorIcon == false &&
        (currentValue != null ||
            (currentListValues.isNotEmpty &&
                widget.fieldType == FieldType.list) ||
            currentBooleanValue)) {
      setState(() {
        showInitialValidatorIcon = true;
      });
    }
    validInput = validateEmail() &&
        validateLength() &&
        validateRegex() &&
        validateRequired() &&
        validateNumber() &&
        validateDni() &&
        validateNie() &&
        validateDniNie() &&
        validatePhone() &&
        validateCadastre();

    validRepeatPassword = validateConfirmatePassword();

    if (widget.isValid != null) {
      validRepeatPassword = true;
      validInput = validInput && widget.isValid!(currentValue);
    }

    if (!validInput) return validInput;

    // Funcion que hace la validacion de los asincronos
    if (widget.asyncValidator != null && validInput) {
      setState(() {
        preLoadingValidator = true;
      });
      if (_debounceAsync?.isActive ?? false) _debounceAsync?.cancel();
      _debounceAsync = Timer(const Duration(milliseconds: 650), () {
        if (currentValue == null) return;
        setState(() {
          preLoadingValidator = false;
          loadingValidator = true;
        });
        widget.asyncValidator!(currentValue).then((value) {
          setState(() {
            validInput = (value != null) ? true : false;
            loadingValidator = false;
            preLoadingValidator = null;
            referenciaCatastral = value;
            cadastreNotFound();
            if (!validInput) {
              widget.onChange?.call(null);
            }
          });
        });
      });
    }

    return validInput;
  }

  @override
  initState() {
    iconColor = widget.iconColor ?? colorDeactivatedText;
    keyboardType = convertType(widget.fieldType);
    showInitialValidatorIcon = widget.showInitialValidatorIcon;
    passwordIsShow = widget.showInitialPassword;

    if (widget.fieldType == FieldType.boolean ||
        widget.fieldType == FieldType.checkbox) {
      currentBooleanValue = widget.value ?? false;
      currentValue = widget.value != null ? widget.value.toString() : null;
    } else if (widget.fieldType == FieldType.list && widget.multiple) {
      currentListValues = widget.value ?? [];
    } else if (widget.fieldType == FieldType.dropdown) {
      // if (!widget.allowSearch) {
      currentListValues = widget.list ?? [];
      // }
      currentValue = widget.value != null ? widget.value.toString() : null;
      if (currentValue != null && currentValue != "")
        _controller.text = currentValue ?? "";
    } else {
      currentValue = widget.value != null ? widget.value.toString() : null;
    }

    // Inicializamos el FocusNode
    _focusNode = FocusNode();
    _focusNode.addListener(_onFocusChange);

    // filteredList = widget.list!;
    _controller.addListener(_filterList);
    super.initState();
  }

  void _onFocusChange() {
    if (!_focusNode.hasFocus) {
      // El campo ha perdido el foco
      setState(() {
        showInitialValidatorIcon = true;
      });
      validator();

      // Si el campo está vacío y es requerido, forzamos la validación
      if (widget.isRequired &&
          (currentValue == null || currentValue!.isEmpty)) {
        widget.onChange?.call(null);
      }
    }
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    _debounceAsync?.cancel();
    super.dispose();
  }

  Widget _buildErrorMessage() {
    if (!showInitialValidatorIcon ||
        validInput ||
        !needValidation ||
        !widget.showErrorMessage) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: widget.simple
          ? const EdgeInsets.only(left: 8, right: 8, bottom: 4)
          : const EdgeInsets.only(left: 8, top: 4, right: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.error_outline,
            color: widget.isRequired ? colorError : colorAlerta,
            size: 16,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              errorMessage ?? "Este campo es obligatorio",
              style: TextStyle(
                color: widget.isRequired ? colorError : colorAlerta,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _cadastreInfo() {
    if (referenciaCatastral != null && widget.validateCadastre) {
      return Padding(
        padding: const EdgeInsets.only(top: 4.0, left: 8.0, right: 8.0),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextBase(
                  'Titular:',
                  bold: true,
                ),
                const SizedBox(width: 4),
                TextBase(referenciaCatastral!.titular ?? ''),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextBase(
                  'Dirección:',
                  bold: true,
                ),
                const SizedBox(width: 4),
                TextBase(referenciaCatastral!.direccion ?? ''),
              ],
            )
          ],
        ),
      );
    } else {
      return const SizedBox.shrink();
    }
  }

  Color _borderColor() {
    if (!validInput && needValidation && showInitialValidatorIcon) {
      return widget.isRequired ? colorError : colorAlerta;
    } else {
      return colorBorder;
    }
  }

  @override
  Widget build(BuildContext context) {
    validator();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: widget.isSmall ? globalButtonHeightTag : null,
          alignment: Alignment.center,
          padding: !widget.isCard
              ? null
              : EdgeInsets.symmetric(
                  horizontal:
                      (widget.simple && widget.fieldType == FieldType.dropdown)
                          ? 0.0
                          : marginxs,
                  vertical: widget.isSmall || widget.simple ? 0.0 : marginxs,
                ),
          margin: !widget.isCard
              ? null
              : EdgeInsets.symmetric(
                  vertical:
                      widget.isSmall ? 0.0 : widget.verticalMargin ?? marginxxs,
                  horizontal: widget.horizontalMargin ?? 0.0,
                ),
          constraints:
              !widget.isCard ? null : const BoxConstraints(minWidth: 300),
          decoration: !widget.isCard
              ? null
              : BoxDecoration(
                  borderRadius: widget.borderRadius ??
                      BorderRadius.circular(globalRadius),
                  color: widget.backgroundColor ??
                      (widget.readOnly ? colorDeactivated : colorBlanco),
                  border: (widget.showValidator &&
                          widget.simple &&
                          needValidation &&
                          showInitialValidatorIcon)
                      ? (validInput && widget.border != null)
                          ? widget.validBorder
                          : (!validInput && widget.errorBorder != null)
                              ? widget.errorBorder
                              : Border.all(
                                  color: colorBorder,
                                  width: globalBorderSize,
                                )
                      : (widget.border ?? null)),
          child: Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Margin.h(),
                      Column(
                        children: [
                          if (!widget.simple) Margin.v(marginxs),
                          if (!widget.simple &&
                              (widget.title != null || widget.subtitle != null))
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextBase(
                                  widget.title!,
                                  fontSize: widget.isSmall ? textCaption : null,
                                  // bold: true,
                                ).flex(),
                                if (widget.isRequired)
                                  TextBase(
                                    " *",
                                    textType: widget.isSmall
                                        ? TextType.caption
                                        : TextType.body,
                                    color: colorError,
                                    textAlign: TextAlign.start,
                                  )
                              ],
                            ),
                          if (widget.subtitle != null &&
                              widget.subtitle!.isNotEmpty &&
                              !widget.simple)
                            Row(
                              children: [
                                TextBase(
                                  "${widget.subtitle!} ",
                                  textType: widget.isSmall
                                      ? TextType.caption
                                      : TextType.caption,
                                ).flex(),
                                if (widget.isRequired && widget.title == null)
                                  TextBase(
                                    " *",
                                    textType: widget.isSmall
                                        ? TextType.caption
                                        : TextType.body,
                                    color: colorError,
                                    textAlign: TextAlign.start,
                                  ),
                              ],
                            ),
                          if (!widget.simple) Margin.v(marginxxs),
                        ],
                      ).expand(),
                      Margin.h(),
                      if (needValidation &&
                          widget.showValidator &&
                          !widget.simple &&
                          showInitialValidatorIcon)
                        validInput
                            ? Icon(Icons.check_circle, color: colorValido)
                            : Tooltip(
                                key: tooltipkey,
                                message: errorMessage ?? "",
                                child: GestureDetector(
                                  onTap: () {
                                    if (!kIsWeb)
                                      tooltipkey.currentState
                                          ?.ensureTooltipVisible();
                                  },
                                  child: Icon(
                                    Icons.error,
                                    color: widget.isRequired
                                        ? colorError
                                        : colorAlerta,
                                  ),
                                ),
                              )
                    ],
                  ),

                  if (!widget.simple)
                    Column(
                      children: [
                        Margin.v(margins),
                        Container(
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: colorBorder,
                                width: globalBorderSize,
                              ),
                            ),
                          ),
                        ),
                        Margin.v(marginxxs),
                      ],
                    ),

                  // Si es tipo texto
                  if (widget.fieldType != FieldType.boolean &&
                      widget.fieldType != FieldType.checkbox &&
                      widget.fieldType != FieldType.info &&
                      widget.fieldType != FieldType.list &&
                      widget.fieldType != FieldType.dropdown)
                    Container(
                      padding: EdgeInsets.only(
                        top: marginxxs,
                      ),
                      constraints: BoxConstraints(
                        minHeight: widget.isSmall
                            ? globalButtonHeightTag - (globalBorderSize * 2)
                            : globalButtonHeight - (globalBorderSize * 2),
                      ),
                      alignment: Alignment.center,
                      child: TextFormField(
                        controller: widget.controller,
                        enabled: !widget.readOnly,
                        keyboardType: keyboardType,
                        enableSuggestions: widget.enableSuggestions,
                        textInputAction: widget.textInputAction ??
                            (widget.isTextArea
                                ? TextInputAction.newline
                                : null),
                        onFieldSubmitted: widget.isTextArea
                            ? null
                            : (value) {
                                if (widget.onSubmit != null) {
                                  widget.onSubmit!(value);
                                }
                                validator();
                              },

                        autofillHints: widget.autofillHints != null
                            ? widget.autofillHints!
                            : widget.fieldType == FieldType.email
                                ? const [AutofillHints.email]
                                : widget.fieldType == FieldType.password
                                    ? const [AutofillHints.password]
                                    : widget.isUser
                                        ? const [AutofillHints.username]
                                        : null,
                        readOnly: widget.readOnly,

                        minLines: widget.lines != null && widget.lines! > 0
                            ? widget.lines
                            : 1,
                        maxLines: widget.isTextArea
                            ? null
                            : widget.lines != null && widget.lines! > 0
                                ? widget.lines
                                : 1,
                        initialValue:
                            widget.controller != null ? null : currentValue,
                        textAlignVertical: TextAlignVertical.center,
                        textAlign: widget.textAlign ?? TextAlign.start,

                        style: TextStyle(
                          color: widget.readOnly
                              ? colorDeactivatedText
                              : colorNegro,
                          fontSize: widget.textFontSize ??
                              (widget.isSmall ? textCaption : textBody),
                          height: widget.isSmall ? 1 : null,
                        ),
                        onChanged: (value) {
                          if (value.isEmpty) {
                            widget.onChange?.call(null);
                            setState(() => currentValue = null);
                            return;
                          }

                          value =
                              widget.upperCase ? value.toUpperCase() : value;
                          setState(() => currentValue = value);

                          widget.onChange?.call(validator() ? value : null);
                        },
                        // change title style
                        obscureText: widget.fieldType == FieldType.password &&
                            !passwordIsShow,
                        inputFormatters: [
                          // Denegar emojis
                          if (!widget.allowEmojis)
                            FilteringTextInputFormatter.deny(RegExp(
                                '(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])')),

                          // Si es tipo correo
                          if (widget.fieldType == FieldType.email)
                            FilteringTextInputFormatter.deny(RegExp(r"\s")),

                          // Si es tipo telefono permite numeros y +
                          if (widget.fieldType == FieldType.phone)
                            FilteringTextInputFormatter.allow(
                                RegExp(r'[0-9+]')),

                          // Si es tipo id solo permite espacios
                          if (widget.denySpaces)
                            FilteringTextInputFormatter.deny(RegExp(r"\s")),

                          // Si es uppercase
                          if (widget.upperCase) UpperCaseTextFormatter(),

                          // Si es numero
                          if (widget.fieldType == FieldType.number)

                            // Allow - on negative and on decimal
                            FilteringTextInputFormatter.allow(
                                RegExp(widget.allowNegatives
                                    ? widget.decimal
                                        ? r'[-0-9.]'
                                        : r'[-0-9]'
                                    : widget.decimal
                                        ? r'[0-9.]'
                                        : r'[0-9]')),

                          // Limitar el numero de caracteres
                          if (widget.maxLength != null)
                            LengthLimitingTextInputFormatter(widget.maxLength),
                        ],
                        textCapitalization: widget.upperCase
                            ? TextCapitalization.characters
                            : TextCapitalization.none,
                        decoration: InputDecoration(
                          hintText: widget.simple
                              ? widget.placeholder ?? widget.title
                              : widget.placeholder,
                          hintStyle: TextStyle(
                            color: colorGris5,
                            fontSize: widget.isSmall ? textCaption : textBody,
                            fontWeight: FontWeight.w400,
                          ),
                          suffixIconColor: iconColor,
                          prefixIconConstraints: BoxConstraints(
                            maxHeight: widget.isSmall
                                ? globalButtonHeightTag
                                : globalButtonHeight,
                          ),
                          prefixIcon: widget.iconLeft != null
                              ? Container(
                                  padding: EdgeInsets.only(
                                    left: margins,
                                    right: marginm,
                                  ),
                                  child: Icon(
                                    widget.iconLeft,
                                    size: globalIconSize,
                                    color: iconColor,
                                  ),
                                )
                              : null,
                          prefixIconColor: iconColor,
                          enabled: true,

                          // Input border like all borders
                          disabledBorder: InputBorder.none,
                          enabledBorder: !widget.showUnderline
                              ? InputBorder.none
                              : UnderlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorBorder,
                                    width: globalBorderSize,
                                  ),
                                ),
                          focusedBorder: !widget.showUnderline
                              ? InputBorder.none
                              : UnderlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorBorder,
                                    width: globalBorderSize,
                                  ),
                                ),
                          isDense: widget.simple || !widget.showUnderline,
                          contentPadding: !widget.showUnderline
                              ? EdgeInsets.only(
                                  left: widget.iconLeft != null ? 0.0 : margins,
                                  top: 0.0,
                                  bottom: 0.0,
                                )
                              : const EdgeInsets.all(0.0),
                          labelStyle: TextStyle(
                            color: colorDeactivatedText,
                            fontSize: widget.isSmall ? textCaption : textBody,
                          ),

                          counter: !widget.showCounter ? Container() : null,
                          label: widget.simple && widget.showLabel
                              ? Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    TextBase(
                                      widget.title ?? widget.placeholder ?? "",
                                      textType: widget.isSmall
                                          ? TextType.caption
                                          : TextType.caption,
                                    ),
                                    if (widget.isRequired)
                                      TextBase(
                                        " *",
                                        textType: widget.isSmall
                                            ? TextType.caption
                                            : TextType.body,
                                        color: colorError,
                                        textAlign: TextAlign.start,
                                      ),
                                  ],
                                )
                              : null,
                        ),
                      ),
                    ),

                  // Si es tipo booleano
                  if (widget.fieldType == FieldType.boolean)
                    Column(
                      children: [
                        (widget.simple)
                            ? Margin.v(margins)
                            : Margin.v(marginxs),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            if (widget.simple && widget.title != null)
                              Row(
                                children: [
                                  Margin.h(margins),
                                  TextBase(widget.title!),
                                  Margin.h(marginl),
                                ],
                              ),
                            Row(
                              children: [
                                if (!widget.simple) Margin.h(marginm),
                                TextBase(widget.leftText ?? "No"),
                                Margin.h(),
                                Switch(
                                  value: currentBooleanValue,
                                  trackOutlineWidth: MaterialStateProperty.all(
                                      globalBorderSize),
                                  inactiveTrackColor: colorBorder,
                                  activeTrackColor: colorPrimario,
                                  trackOutlineColor: MaterialStateProperty.all(
                                      Colors.transparent),
                                  thumbColor: currentBooleanValue
                                      ? MaterialStateProperty.all(
                                          colorBlancoConst)
                                      : MaterialStateProperty.all(colorNegro),
                                  overlayColor: currentBooleanValue
                                      ? MaterialStateProperty.all(
                                          colorPrimario.withOpacity(0.2))
                                      : MaterialStateProperty.all(
                                          colorNegro.withOpacity(0.2)),
                                  onChanged: widget.readOnly
                                      ? null
                                      : (value) {
                                          if (widget.isRequired && !value) {
                                            widget.onChange!(null);
                                          } else {
                                            widget.onChange!(value);
                                          }
                                          setState(() {
                                            currentBooleanValue = value;
                                          });
                                        },
                                ),
                                Margin.h(),
                                TextBase(widget.rightText ?? "Si"),
                                Margin.h(margins),
                              ],
                            )
                          ],
                        ),
                        (widget.simple)
                            ? Margin.v(margins)
                            : Margin.v(marginxs),
                      ],
                    ),

                  // Si es tipo info
                  if (widget.fieldType == FieldType.info)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: marginxs,
                            vertical: marginm,
                          ),
                          child: TextBase(
                            widget.placeholder ?? "---",
                            textType: widget.isSmall
                                ? TextType.caption
                                : TextType.caption,
                            isSelectable: true,
                          ),
                        ),
                      ],
                    ),

                  // Si es tipo chekbox
                  if (widget.fieldType == FieldType.checkbox)
                    GestureDetector(
                      onTap: widget.readOnly
                          ? null
                          : () {
                              setState(() {
                                currentBooleanValue = !currentBooleanValue;
                              });
                              if (widget.onChange != null)
                                widget.onChange?.call(
                                    widget.isRequired && !currentBooleanValue
                                        ? null
                                        : !currentBooleanValue);
                            },
                      child: Column(
                        children: [
                          Margin.v(marginm),
                          Row(
                            children: [
                              Checkbox(
                                value: currentBooleanValue,
                                onChanged: widget.readOnly
                                    ? null
                                    : (bool? value) {
                                        value ??= false;

                                        if (widget.onChange != null)
                                          widget.onChange?.call(
                                              widget.isRequired && !value
                                                  ? null
                                                  : value);
                                        setState(() {
                                          currentBooleanValue =
                                              (value ?? false);
                                        });
                                      },
                              ),
                              Margin.h(
                                marginxs,
                              ),
                              TextBase(widget.placeholder ?? "---").expand(),
                            ],
                          ),
                          Margin.v(marginm),
                        ],
                      ),
                    ),

                  // TODO - Limitar a max 100 items en cheklist y radio button
                  // TODO - Eliminar la duplicicdad de codigo en cheklist y radio button y en gesturedetector
                  // Si es tipo lista multiple o cheklist
                  if (widget.fieldType == FieldType.list &&
                      widget.multiple &&
                      widget.checkboxOptions == null)
                    Column(
                      children: [
                        Margin.v(marginm),
                        for (var item in widget.list ?? [])
                          GestureDetector(
                            onTap: widget.readOnly
                                ? null
                                : () {
                                    if (!currentListValues.contains(item)) {
                                      if (currentListValues.length <
                                          (widget.maxLength ?? 100)) {
                                        setState(() {
                                          // save index of item
                                          currentListValues.add(item);
                                          index = widget.list!.indexOf(item);
                                          currentListIndexValues.add(index + 1);
                                        });
                                      }
                                    } else {
                                      setState(() {
                                        currentListValues.remove(item);
                                        currentListIndexValues
                                            .remove(index + 1);
                                      });
                                    }

                                    if (validator()) {
                                      if (widget.onChange != null) {
                                        widget.onChange!(currentListValues);
                                        widget.onChangeIndex!(
                                            currentListIndexValues);
                                      }
                                    } else {
                                      widget.onChange!(null);
                                      widget.onChangeIndex!(null);
                                    }
                                  },
                            child: Container(
                              alignment: Alignment.centerLeft,
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Checkbox(
                                    value: currentListValues.contains(item),
                                    onChanged: widget.readOnly
                                        ? null
                                        : (value) {
                                            if (!currentListValues
                                                .contains(item)) {
                                              if (currentListValues.length <
                                                  (widget.maxLength ?? 100)) {
                                                setState(() {
                                                  currentListValues.add(item);
                                                  index = widget.list!
                                                      .indexOf(item);
                                                  currentListIndexValues
                                                      .add(index + 1);
                                                });
                                              }
                                            } else {
                                              setState(() {
                                                currentListValues.remove(item);
                                                currentListIndexValues
                                                    .remove(index + 1);
                                              });
                                            }

                                            if (validator()) {
                                              if (widget.onChange != null) {
                                                widget.onChange!(
                                                    currentListValues);
                                                widget.onChangeIndex!(
                                                    currentListIndexValues);
                                              }
                                            } else {
                                              widget.onChange!(null);
                                              widget.onChangeIndex!(null);
                                            }
                                          },
                                  ),
                                  Margin.h(marginxs),
                                  TextBase(item).expand(),
                                ],
                              ),
                            ),
                          ),
                        Margin.v(marginm),
                      ],
                    ),

                  if (widget.fieldType == FieldType.list &&
                      widget.multiple &&
                      widget.checkboxOptions != null)
                    Column(
                      children: [
                        Margin.v(marginm),
                        for (var option in widget.checkboxOptions!)
                          GestureDetector(
                            onTap: widget.readOnly
                                ? null
                                : () {
                                    setState(() {
                                      if (!currentListValues
                                          .contains(option.value)) {
                                        if (currentListValues.length <
                                            (widget.maxLength ?? 100)) {
                                          currentListValues.add(option.value);
                                        }
                                      } else {
                                        currentListValues.remove(option.value);
                                      }
                                    });

                                    if (validator()) {
                                      widget.onChange?.call(currentListValues);
                                      widget.onChangeIndex
                                          ?.call(currentListValues);
                                    } else {
                                      widget.onChange?.call(null);
                                      widget.onChangeIndex?.call(null);
                                    }
                                  },
                            child: Container(
                              alignment: Alignment.centerLeft,
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Checkbox(
                                    value: currentListValues
                                        .contains(option.value),
                                    onChanged: widget.readOnly
                                        ? null
                                        : (value) {
                                            setState(() {
                                              if (value == true) {
                                                if (currentListValues.length <
                                                    (widget.maxLength ?? 100)) {
                                                  currentListValues
                                                      .add(option.value);
                                                }
                                              } else {
                                                currentListValues
                                                    .remove(option.value);
                                              }
                                            });

                                            if (validator()) {
                                              widget.onChange
                                                  ?.call(currentListValues);
                                              widget.onChangeIndex
                                                  ?.call(currentListValues);
                                            } else {
                                              widget.onChange
                                                  ?.call(currentListValues);
                                              widget.onChangeIndex?.call(null);
                                            }
                                          },
                                  ),
                                  Margin.h(marginxs),
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: option.onClick,
                                      child: option.label,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        Margin.v(marginm),
                      ],
                    ),

                  // Si es tipo radioButton
                  if (widget.fieldType == FieldType.list && !widget.multiple)
                    Column(
                      children: [
                        Margin.v(marginm),
                        for (var item in widget.list ?? [])
                          Row(
                            children: [
                              Radio(
                                value: item,
                                groupValue: currentValue,
                                onChanged: (value) {
                                  setState(() {
                                    currentValue = item;
                                  });

                                  if (widget.onChange != null) {
                                    widget.onChange!(value);
                                  }
                                },
                              ),
                              Margin.h(
                                marginxs,
                              ),
                              TextBase(item).expand(),
                            ],
                          ),
                        Margin.v(marginm),
                      ],
                    ),

                  // Si es dropdown
                  if (widget.fieldType == FieldType.dropdown)
                    Column(
                      children: [
                        if (!widget.simple) Margin.v(),
                        GestureDetector(
                          // Si es movil el comportamiento es por medio de tap, si es web de dropdown
                          onTap: !widget.readOnly && !isWeb
                              ? () {
                                  var options = currentListValues
                                      .map(
                                        (e) => DropdownMenuEntry<String>(
                                          value: e,
                                          label: e,
                                        ),
                                      )
                                      .toList();

                                  if (isListNullOrEmpty(options)) {
                                    DialogService().showInfoDialog(tt(
                                        TTShared.noSeHanEncontradoResultados));
                                  }
                                  // Aunque tenga una única opción, comprobamos que haya seleccionado alguna para no bloquear la selección.
                                  // Esto puede darse cuando el widget empieza en un estado no editable y luego se actualiza a editable.
                                  else if (currentValue != null &&
                                      options.length == 1) {
                                    DialogService().showInfoDialog(tt(TTShared
                                        .noExistenMasOpcionesSeleccionables));
                                  } else {
                                    DialogService().showSelectDialog(
                                      // parse currentListValues to string
                                      items: currentListValues
                                          .map2((e) => e.toString()),
                                      getCurrentValue: () => currentValue,
                                      onChange: (i, t) {
                                        if (widget.onChange != null)
                                          widget.onChange!(options[i].value);

                                        if (widget.onChangeIndex != null)
                                          widget.onChangeIndex!(i);

                                        setState(() {
                                          currentValue = options[i].value;
                                        });
                                      },
                                      closeOnSelectItem: true,
                                    );
                                  }
                                }
                              : null,
                          child: Container(
                            height: widget.isSmall
                                ? globalButtonHeightTag - 2 * globalBorderSize
                                : null,
                            decoration: (!widget.simple)
                                ? BoxDecoration(
                                    border: Border.all(
                                      color: colorBorder,
                                      width: globalBorderSize,
                                    ),
                                    borderRadius:
                                        BorderRadius.circular(globalRadius),
                                  )
                                : null,
                            child: DropdownMenu<String>(

                                // enabled: !widget.readOnly && kIsWeb,
                                enabled: !widget.readOnly && isWeb,
                                controller:
                                    widget.allowSearch ? _controller : null,
                                initialSelection: currentValue,
                                enableFilter: widget.allowSearch,
                                enableSearch: widget.allowSearch,
                                menuHeight:
                                    currentListValues.isEmpty ? 0.0 : null,
                                hintText: widget.placeholder ??
                                    ((widget.fieldType == FieldType.dropdown &&
                                            !widget.allowSearch)
                                        ? tt(TTShared.seleccioneUnaOpcion)
                                        : tt(TTShared.escribaParaFiltrar)),
                                menuStyle: MenuStyle(
                                  backgroundColor:
                                      MaterialStateProperty.all(colorBlanco),
                                  elevation: MaterialStateProperty.all(10),
                                  shadowColor: MaterialStateProperty.all(
                                      colorBlanco.withOpacity(0.5)),
                                  surfaceTintColor:
                                      MaterialStateProperty.all(colorBlanco),
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius:
                                          BorderRadius.circular(globalRadius),
                                      side: BorderSide(
                                        color: colorBorder,
                                        width: globalBorderSize,
                                      ),
                                    ),
                                  ),
                                ),
                                inputDecorationTheme: InputDecorationTheme(
                                  border: InputBorder.none,
                                  isDense: widget.isSmall,
                                  isCollapsed: widget.isSmall,
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: 15,
                                    vertical: widget.isSmall ? 0.0 : margins,
                                  ),
                                  hintStyle: TextStyle(
                                    color: colorGris5,
                                    fontSize:
                                        widget.isSmall ? textCaption : textBody,
                                  ),
                                  labelStyle: TextStyle(
                                    color: colorDeactivatedText,
                                    fontSize:
                                        widget.isSmall ? textCaption : textBody,
                                  ),
                                ),
                                textStyle: TextStyle(
                                  color: widget.readOnly
                                      ? colorDeactivatedText
                                      : colorNegro,
                                  fontSize:
                                      widget.isSmall ? textCaption : textBody,
                                ),
                                requestFocusOnTap: true,
                                expandedInsets: EdgeInsets.symmetric(
                                  horizontal: widget.isSmall ? 0 : marginm,
                                  vertical: widget.isSmall ? 0 : marginxs,
                                ),
                                onSelected: (String? value) {
                                  if (value == null) return;
                                  if (widget.onChange != null) {
                                    widget.onChange!(value);
                                  }
                                  // setState(() {
                                  currentValue = value;
                                  // });
                                },
                                trailingIcon: widget.isSmall
                                    ? Stack(
                                        children: [
                                          Transform.translate(
                                            offset: const Offset(0,
                                                -7.5), // Mueve 15px hacia arriba
                                            child: const Icon(
                                                Icons.arrow_drop_down),
                                          ),
                                        ],
                                      )
                                    : null,
                                selectedTrailingIcon: widget.simple
                                    ? Stack(
                                        children: [
                                          Transform.translate(
                                            offset: const Offset(0,
                                                -7.5), // Mueve 15px hacia arriba
                                            child:
                                                const Icon(Icons.arrow_drop_up),
                                          ),
                                        ],
                                      )
                                    : null,
                                dropdownMenuEntries: currentListValues
                                    .map(
                                      (e) => DropdownMenuEntry<String>(
                                        value: e,
                                        label: e,
                                      ),
                                    )
                                    .toList()),
                          ),
                        ),
                        if (!widget.simple) Margin.v()
                      ],
                    ),
                ],
              ).expand(),

              // Icono derecho
              if (widget.iconRight != null ||
                  (widget.fieldType == FieldType.password &&
                      widget.showTogglePasswordVisibility))
                Row(
                  children: [
                    if (widget.simple) Margin.h(marginm),
                    InkWell(
                      highlightColor: colorDeactivated,
                      hoverColor: colorDeactivated,
                      radius: marginxs,
                      onTap: () {
                        setState(() {
                          passwordIsShow = !passwordIsShow;
                          // request focus
                        });
                        if (widget.onPressedIconRight != null) {
                          widget.onPressedIconRight!(passwordIsShow);
                        }
                      },
                      child: Icon(
                        widget.fieldType == FieldType.password
                            ? (passwordIsShow
                                ? Icons.visibility
                                : Icons.visibility_off)
                            : widget.iconRight,
                        color: iconColor,
                      ),
                    ),
                    // if (widget.simple) Margin.h(marginm),
                  ],
                ),

              // Icono de validacion
              if (widget.simple && widget.fieldType != FieldType.dropdown)
                Margin.h(),

              if (widget.showValidator &&
                  widget.simple &&
                  needValidation &&
                  showInitialValidatorIcon)
                validInput
                    ? Padding(
                        padding: const EdgeInsets.only(right: 16.0),
                        child: Icon(
                          Icons.check,
                          color: colorValido,
                        ),
                      )
                    : Tooltip(
                        key: tooltipkey,
                        message: errorMessage ?? "",
                        child: GestureDetector(
                          onTap: () {
                            if (!kIsWeb)
                              tooltipkey.currentState?.ensureTooltipVisible();
                          },
                          child: Padding(
                            padding: const EdgeInsets.only(right: 16.0),
                            child: Icon(
                              Icons.error_outline,
                              color:
                                  widget.isRequired ? colorError : colorAlerta,
                            ),
                          ),
                        ),
                      ),
              // if (widget.simple) Margin.h(marginxs),
            ],
          ),
        ),

        // Texto inferior de validacion
        if (widget.showValidator &&
            widget.simple &&
            needValidation &&
            showInitialValidatorIcon &&
            widget.regex != null &&
            widget.regex!.isNotEmpty &&
            !validInput &&
            widget.showErrorMessage)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Margin.v(marginxs),
              TextBase(
                errorMessage ?? "",
                color: colorError,
              ),
              // Margin.v(marginxs),
            ],
          ),
        if (validRepeatPassword &&
            !validInput &&
            widget.regex == null &&
            showInitialValidatorIcon &&
            errorMessage != null &&
            widget.showErrorMessage)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextBase(
                errorMessage ?? "",
                color: colorError,
              ),
            ],
          )
      ],
    );
  }
}

class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}

class CheckboxOption {
  final String value;
  final Widget label;
  final VoidCallback? onClick;

  CheckboxOption({
    required this.value,
    required this.label,
    this.onClick,
  });
}

// TODO - Pasar cada uno de los componentes personalizados como el dropdown o el switch a un archivo aparte
// TODO - Implementar placeholderWidget en el resto de composnentes, actualmente solo esta implementado en checkbox

class ReferenciaCatastral {
  final int? id;
  final String? referenciaCatastral;
  final String? titular;
  final String? direccion;
  final String? codigoPostal;
  final String? municipio;
  final String? provincia;
  final String? comunidadAutonoma;
  final String? poblacion;
  final String? numero;
  final String? planta;
  final String? puerta;
  final String? escalera;
  final String? nif;
  final DateTime? fechaCreacion;
  final DateTime? fechaModificacion;
  final DateTime? fechaBaja;

  ReferenciaCatastral({
    this.id,
    this.referenciaCatastral,
    this.titular,
    this.direccion,
    this.codigoPostal,
    this.municipio,
    this.provincia,
    this.comunidadAutonoma,
    this.poblacion,
    this.numero,
    this.planta,
    this.puerta,
    this.escalera,
    this.nif,
    this.fechaCreacion,
    this.fechaModificacion,
    this.fechaBaja,
  });

  factory ReferenciaCatastral.fromJson(Map<String, dynamic> json) {
    return ReferenciaCatastral(
      id: json['id'],
      referenciaCatastral: json['referenciaCatastral'],
      titular: json['titular'],
      direccion: json['direccion'],
      codigoPostal: json['codigoPostal'],
      municipio: json['municipio'],
      provincia: json['provincia'],
      comunidadAutonoma: json['comunidadAutonoma'],
      poblacion: json['poblacion'],
      numero: json['numero'],
      planta: json['planta'],
      puerta: json['puerta'],
      escalera: json['escalera'],
      nif: json['nif'],
      fechaCreacion: json['fechaCreacion'] != null
          ? DateTime.parse(json['fechaCreacion'])
          : null,
      fechaModificacion: json['fechaModificacion'] != null
          ? DateTime.parse(json['fechaModificacion'])
          : null,
      fechaBaja:
          json['fechaBaja'] != null ? DateTime.parse(json['fechaBaja']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'referenciaCatastral': referenciaCatastral,
      'titular': titular,
      'direccion': direccion,
      'codigoPostal': codigoPostal,
      'municipio': municipio,
      'provincia': provincia,
      'comunidadAutonoma': comunidadAutonoma,
      'poblacion': poblacion,
      'numero': numero,
      'planta': planta,
      'puerta': puerta,
      'escalera': escalera,
      'nif': nif,
      'fechaCreacion': fechaCreacion?.toIso8601String(),
      'fechaModificacion': fechaModificacion?.toIso8601String(),
      'fechaBaja': fechaBaja?.toIso8601String(),
    };
  }
}
