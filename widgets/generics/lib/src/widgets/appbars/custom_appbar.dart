import '../../_imports.dart';

List<Widget> appbarFixedActions = [];

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? text;
  final List<Widget>? actions;
  final TabBar? tabBar;
  final bool automaticLeading;
  final bool isFixedActionsShowed;
  final Widget? leading;
  final int titleMaxLines;
  final bool onlyTitle;
  final Widget Function(String text)? textWidgetBuilder;

  CustomAppBar(
    this.text, {
    this.actions,
    this.tabBar,
    this.automaticLeading = true,
    this.isFixedActionsShowed = true,
    this.leading,
    this.titleMaxLines = 2,
    this.textWidgetBuilder,
    this.onlyTitle = false,
  });

  List<Widget> _getActions() {
    List<Widget> finalActions = [];

    if (appbarFixedActions.isFilled && isFixedActionsShowed)
      finalActions.addAll(appbarFixedActions);

    if (actions.isFilled) finalActions.addAll(actions!);

    if (finalActions.isFilled) finalActions.add(const SizedBox(width: 10));

    return finalActions;
  }

  @override
  Widget build(BuildContext context) {
    final t = text ?? "";

    return AppBar(
      backgroundColor: themeColors.primaryBlue,
      // titleSpacing: 0,
      leading:
          // This avoid a error when the appbar is rebuild but not visible (home page) the drawer button disappear.
          !Scaffold.of(context).hasDrawer &&
                  !Scaffold.of(context).hasEndDrawer &&
                  leading == null &&
                  Navigator.canPop(context)
              ? LeadingAppBar()
              : leading,
      automaticallyImplyLeading: automaticLeading,
      actions: _getActions(),
      elevation: themeElevations.appbar,
      centerTitle: true,
      bottom: tabBar == null ? null : BarCustom(tabBar),
      title: onlyTitle
          ? TextBase(
              t,
              color: themeColors.white,
            )
          : automaticLeading
              ? TitleAppBar(
                  text,
                  maxLines: titleMaxLines,
                  textWidgetBuilder: textWidgetBuilder,
                )
              : Container(
                  padding: const EdgeInsets.only(top: 2, bottom: 2),
                  child: SizedBox(
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(width: 10),
                          Flexible(
                            child: textWidgetBuilder != null
                                ? textWidgetBuilder!(t)
                                : AppBarText(t, titleMaxLines),
                          )
                        ]),
                  ),
                ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(tabBar != null ? 130 : 60);

  // static List<Widget> editSaveOptions({
  //   required bool isEditing,
  //   required void Function() onEditPressed,
  //   required void Function() onSavePressed,
  //   required List<ActionDataFunction> options,
  // }) =>
  //     [
  //       isEditing
  //           ? ActionButtonAppBar(
  //               icon: FontAwesomeIcons.solidSave,
  //               tooltip: translate(TTShared.guardarCambios),
  //               onPressed: onSavePressed,
  //             )
  //           : ActionButtonAppBar(
  //               icon: FontAwesomeIcons.solidEdit,
  //               tooltip: translate(TTShared.modificar),
  //               onPressed: onEditPressed,
  //             ),
  //       Visibility(
  //         visible: isListFilled(options),
  //         child: ActionButtonAppBar.options(options),
  //       )
  //     ];
}

class CustomAppBarStream extends StatelessWidget
    implements PreferredSizeWidget {
  late PreferredSizeWidget _appbarCopy;

  final PreferredSizeWidget Function() appbarBuilder;
  final Stream stream;

  CustomAppBarStream(this.appbarBuilder, this.stream) {
    _appbarCopy = appbarBuilder();
  }

  @override
  Size get preferredSize => _appbarCopy.preferredSize;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
      stream: stream,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        return appbarBuilder();
      },
    );
  }
}
