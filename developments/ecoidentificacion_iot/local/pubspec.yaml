name: ecoidentificacion_local_source

version: 1.0.0+1
publish_to: none

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  ecoidentificacion_repository:
    path: ../repository
  database_hive:
    path: ../../../sources/database_hive
  image_local_hive:
    path: ../../../entities/shared/image/local_hive

dev_dependencies:
  hive_generator: ^1.1.0
  build_runner:
  flutter_test:
    sdk: flutter
  testing:
    path: ../../../testing
