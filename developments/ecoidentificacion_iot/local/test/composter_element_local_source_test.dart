// import 'package:ecocompostaje_local_source/src/_imports.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:testing/testing.dart';

// void main() {
//   group("ComposterElementLocal", () {
//     late ComposterElementLocal local;

//     setUpAll(() async {
//       await DatabaseHiveService().startDatabase(DatabaseHiveProviderDesktop());
//       local = ComposterElementLocal(
//         DataServiceMock(),
//         UserMock(
//           isAdmin: false,
//           id: 1,
//           companyId: 1,
//         ),
//       );
//       await local.createTable();
//       await local.removeAll();
//     });

//     test('No hay modelos en la base de datos', () async {
//       final int? count = await local.count();
//       expect(count == 0, true);
//     });

//     test('Existen modelos después de insertar', () async {
//       await local.insert(ComposterElementModel(
//         uuid: "1",
//         id: 1,
//         description: "1",
//         companyId: 1,
//         plate: "1",
//         masterId: 1,
//         lat: 1.0,
//         long: 1.0,
//         modelId: 1,
//         isSync: false,
//         modifyDate: DateTime.now(),
//         isRemoved: false,
//         address: '',
//         state: ComposterElementState.functional,
//         citizenId: 1,
//         creationDate: DateTime.now(),
//         hasImage: false,
//         image: '',
//         municipality: '',
//       ));
//       final int? count = await local.count();
//       expect(count == 1, true);
//     });

//     test('Se puede recuperar por PK', () async {
//       final ComposterElementModel? model = await local.getByPk("1");
//       expect(
//         model != null &&
//             model.uuid == "1" &&
//             model.id == 1 &&
//             model.plate == "1" &&
//             model.masterId == 1 &&
//             model.companyId == 1 &&
//             model.lat == 1.0 &&
//             model.long == 1.0,
//         true,
//       );
//     });

//     test('Modificamos el modelo y es válido', () async {
//       await local.update(ComposterElementModel(
//         uuid: "1",
//         id: 1,
//         description: "2",
//         companyId: 2,
//         plate: "2",
//         masterId: 2,
//         lat: 2.0,
//         long: 2.0,
//         modelId: 2,
//         isSync: false,
//         modifyDate: DateTime.now(),
//         isRemoved: false,
//         address: '',
//         state: ComposterElementState.functional,
//         citizenId: 2,
//         creationDate: DateTime.now(),
//         hasImage: false,
//         image: '',
//         municipality: '',
//       ));
//       final int? count = await local.count();
//       expect(count == 1, true);

//       final ComposterElementModel? model = await local.getByPk("1");
//       expect(
//         model != null &&
//             model.uuid == "1" &&
//             model.plate == "2" &&
//             model.masterId == 2 &&
//             model.companyId == 2 &&
//             model.lat == 2.0 &&
//             model.long == 2.0,
//         true,
//       );
//     });
//   });
// }
