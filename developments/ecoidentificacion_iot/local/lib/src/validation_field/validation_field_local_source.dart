import '../_imports.dart';

class ValidationFieldLocal extends ValidationFieldDao
    implements ValidationFieldLocalBase {
  final DataServiceBase dataService;
  final UserBase user;

  ValidationFieldLocal(this.dataService, this.user) {
    dataService.addRepository(this);
  }

  @override
  Future<List<ValidationFieldModel>> getNoSync() async {
    return getWhere((m) => !m.isSync);
  }

  @override
  Stream<List<ValidationFieldModel>> getAllStream() async* {
    yield await getAll();
  }

}