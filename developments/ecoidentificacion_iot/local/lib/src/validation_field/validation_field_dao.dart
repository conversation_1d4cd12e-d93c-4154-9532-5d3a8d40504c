import '../_imports.dart';

class ValidationFieldDao
    extends DaoBase<ValidationFieldModel, int,
        ValidationFieldModelAdapter> {
  static const dbTableName = "validation_field";

  ValidationFieldDao()
      : super(
          ValidationFieldModelAdapter(),
          isStoreInMemory: false,
          isPhysicalRemoval: true,
        );

  @override
  String get tableName => dbTableName;

  @override
  List<Future<void> Function()> get updateTableScripts => [];
}