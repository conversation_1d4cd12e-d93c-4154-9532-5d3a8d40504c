import '../_imports.dart';

class ValidationFieldModelAdapter
    extends TypeAdapter<ValidationFieldModel> {
  @override
  final int typeId = 82;

  @override
  ValidationFieldModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ValidationFieldModel(
      id: fields[0] as int,
      label: fields[1] as String,
      jsonName: fields[2] as String,
      companyId: fields[3] as int,
      creationDate: fields[4] as DateTime,
      modifyDate: fields[5] as DateTime,
      isRemoved: fields[6] as bool,
      isSync: fields[7] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, ValidationFieldModel obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.label)
      ..writeByte(2)
      ..write(obj.jsonName)
      ..writeByte(3)
      ..write(obj.companyId)
      ..writeByte(4)
      ..write(obj.creationDate)
      ..writeByte(5)
      ..write(obj.modifyDate)
      ..writeByte(6)
      ..write(obj.isRemoved)
      ..writeByte(7)
      ..write(obj.isSync);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ValidationFieldModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}