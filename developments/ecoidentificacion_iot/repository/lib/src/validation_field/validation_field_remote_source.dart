import '../_imports.dart';

class ValidationFieldRemote extends RemoteWrapper<ValidationFieldModel, int> {
  final _api = ValidationFieldApi();

  final TokenBase token;
  final UserNPBase user;
  final ValidationFieldLocalBase local;

  ValidationFieldRemote({
    required this.token,
    required this.user,
    required this.local,
  }) : super(
          token: token,
          user: user,
          logTag: 'ValidationFieldRemote',
          jsonFieldModifyDate: 'modifyDate',
          fromJson: (j) => modelFromJson(j),
          toJson: (m) => modelToJson(m),
          apiSave: (u, t, j) => Api().post(
            url: "$u/api/ValidationField",
            body: j,
            header: Api().getHeaderToken(t),
          ),
          apiGetByPk: (u, t, p) => Api().get(
            url: "$u/api/ValidationField?id=$p",
            header: Api().getHeaderToken(t),
          ),
          apiGetAll: (u, t, d) => Api().get(
            url:
                "$u/api/ValidationField/list?date=$d&companyId=${user.companyId}",
            header: Api().getHeaderToken(t),
          ),
          apiDelete: null,
        );

  static ValidationFieldModel modelFromJson(Map<String, dynamic> json) =>
      ValidationFieldModel(
        id: json['id']!,
        label: json['label'] ?? "",
        jsonName: json['nombreJson'] ?? "",
        companyId: json['empresaId'] ?? 0,
        creationDate: DateTime.now(),
        modifyDate: DateTime.now(),
        isSync: true,
        isRemoved: false,
      );

  static Map<String, dynamic> modelToJson(ValidationFieldModel m) => {
        'id': m.id,
        'label': m.label,
        'nombreJson': m.jsonName,
        'fechaCreacion': m.creationDate.toDatabaseNP(),
        'fechaModificacion': m.modifyDate.toDatabaseNP(),
      };

  Future<Either<ValidationFieldModel, Failure2<SendErrorType>>>
      getByCompany() async {
    final response = await _api.getByCompany(
      url: await token.getUrl(),
      token: await token.get(),
      companyId: user.companyId!,
    );

    final error = response.checkErrors();
    if (error != null) return Either.right(error);

    return Either.left(
        ValidationFieldDto.fromJson(response.response).toModel(user.companyId));
  }
}
