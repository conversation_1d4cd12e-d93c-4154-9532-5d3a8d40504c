import '../_imports.dart';

extension ValidationFieldDtoExtension on ValidationFieldDto {
  ValidationFieldModel toModel(int? empresaActual) => ValidationFieldModel(
        // Informacion del SSO
        id: id!,
        label: label ?? "",
        jsonName: jsonName ?? "",
        companyId: companyId ?? empresaActual!,
        modifyDate: modifyDate ?? DateTime.now(),
        creationDate: creationDate ?? DateTime.now(),
        isSync: isSync ?? true,
        isRemoved: isRemoved ?? false,

      
      );
}

extension ValidationFieldModelExtension on ValidationFieldModel {
  ValidationFieldDto toDto() => ValidationFieldDto(
        // Informacion del SSO
        id: id,
        label: label,
        jsonName: jsonName,
        companyId: companyId,
        creationDate: creationDate, 
        modifyDate: modifyDate,
        isSync: isSync,
        isRemoved: isRemoved,
  );
}
