import '../_imports.dart';

class ValidationFieldApi {
  // Obtener el campo de validación por empresa
  Future<ApiResponse> getByCompany({
    required String url,
    required String token,
    required int companyId,
  }) async {
    final _baseUrl =
        "$url/api/configuracion/ecoidentificacion/find/field?enterprise=${companyId}";
    final response = await Api().get(
      url: _baseUrl,
      header: Api().getHeaderToken(token),
    );
    return response;
  }
}
