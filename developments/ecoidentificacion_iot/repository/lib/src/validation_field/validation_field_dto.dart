class ValidationFieldDto {
  // Informacion SSO
  int? id;
  String? label;
  String? jsonName;
  int? companyId;
  DateTime? creationDate;
  DateTime? modifyDate;
  bool? isRemoved;
  bool? isSync;

  ValidationFieldDto({
    // Informacion SSO
    required this.id,
    required this.label,
    required this.jsonName,
    required this.companyId,
    required this.creationDate,
    required this.modifyDate,
    required this.isRemoved,
    required this.isSync,
  });

  ValidationFieldDto.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    label = json["label"];
    jsonName = json["jsonName"];
    creationDate = DateTime.now();
    modifyDate = DateTime.now();
    isRemoved = json["isRemoved"] ?? false;
    isSync = json["isSync"] ?? true;
  }

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "label": label,
      "jsonName": jsonName,
    };
  }
}
