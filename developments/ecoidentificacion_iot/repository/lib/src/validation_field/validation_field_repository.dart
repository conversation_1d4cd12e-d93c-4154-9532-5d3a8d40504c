import '../_imports.dart';

class ValidationFieldRepository extends RepositoryOnlineWriteOfflineReadAdapter<
    ValidationFieldModel,
    int,
    ValidationFieldLocalBase,
    ValidationFieldRemote> implements ValidationFieldRepositoryBase {
  ValidationFieldRepository({
    required ValidationFieldLocalBase local,
    required ConnectionServiceBase connection,
    required ValidationFieldRemote remote,
    required Synchronizer<ValidationFieldModel, int> synchronizable,
  }) : super(local, remote, connection, synchronizable);

  @override
  Future<Either<ValidationFieldModel, Failure2<SendErrorType>>>
      getByCompany() async {
    ValidationFieldModel? u;
    if (!connection.hasInternet)
      return Either.right(
          Failure2(SendErrorType.error, 'No internet connection'));
    final response = await remote.getByCompany();
    return response;
  }
}
