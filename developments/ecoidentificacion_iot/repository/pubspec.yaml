name: ecoidentificacion_repository

version: 1.0.0+1
publish_to: none

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  ecoidentificacion_domain:
    path: ../domain
  data_service_interface:
    path: ../../../interfaces/data_service
  data_interface_v9:
    path: ../../../interfaces/data_v9
  token_interface:
    path: ../../../interfaces/token
  sso_repository:
    path: ../../../entities/np/sso/repository
  # Esto se necesita porque uno de los endpoint se debe llamar antes de que
  # el usuario inicie sesión, por lo tanto no se puede utilizar el token injectado
  # ya que no se conoce todavía la url real del acceso.
  token_source_np:
    path: ../../../sources/token_source_np
  
