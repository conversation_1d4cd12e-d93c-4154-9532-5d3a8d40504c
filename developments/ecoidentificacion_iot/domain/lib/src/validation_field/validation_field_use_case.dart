import '../_imports.dart';

class ValidationFieldUseCase extends UseCaseBase<ValidationFieldModel, int,
    ValidationFieldRepositoryBase> {
  ValidationFieldUseCase(
      ValidationFieldRepositoryBase repository, UserNPBase user)
      : super(repository, user);

  @override
  bool internalReadGlobal() => true;

  @override
  Future<bool> internalReadModel(ValidationFieldModel model) async => true;

  Future<Either<ValidationFieldModel, Failure2<SendErrorType>>> getByCompany() {
    return repository.getByCompany();
  }
}

abstract class ValidationFieldRepositoryBase
    implements RepositoryBase<ValidationFieldModel, int> {
  Future<Either<ValidationFieldModel, Failure2<SendErrorType>>> getByCompany();
}
