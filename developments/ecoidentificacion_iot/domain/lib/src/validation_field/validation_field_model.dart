import '../_imports.dart';

class ValidationFieldModel implements ModelSync<int> {
  final int id;
  final String label;
  final String jsonName;
  final int companyId;
  DateTime creationDate;
  DateTime modifyDate;
  bool isRemoved;
  bool isSync;

  ValidationFieldModel({
    required this.id,
    required this.label,
    required this.jsonName,
    required this.companyId,
    required this.creationDate,
    required this.modifyDate,
    required this.isRemoved,
    required this.isSync,
  });

  int get pk => id;

    @override
  String toString() {
    return 'ValidationFieldModel(id: $id, label: $label, jsonName: $jsonName, companyId: $companyId, creationDate: $creationDate, modifyDate: $modifyDate, isRemoved: $isRemoved, isSync: $isSync)';
  }
}

extension ValidationFieldModelListDomainExt on List<ValidationFieldModel> {}
