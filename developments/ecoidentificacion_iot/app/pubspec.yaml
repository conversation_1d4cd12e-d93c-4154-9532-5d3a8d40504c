name: ecoidentificacion_iot_app
description: Gestión de composteras y aportaciones

version: 1.0.0+1
publish_to: none

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter


  ## BACKGROUND
  workmanager: ^0.5.1
  database_sqlite_background:
    path: ../../../sources/database_sqlite_background

  # PÁGINAS
  translation_page:
    path: ../../../widgets/pages/translation  
  login_page:
    path: ../../../widgets/pages/login_v2
  # We put json_table here because it is needed by the test info.
  json_table_page:
    path: ../../../widgets/pages/json_table


  # ENTITIES
  ecoidentificacion_local_source:
    path: ../local
  citizen_injector:
    path: ../../../entities/np/citizen/injector
  preference_injector:
    path: ../../../entities/shared/preference/injector
  preference_repository_hive:
    path: ../../../entities/shared/preference/repository_hive
  lock_injector:
    path: ../../../entities/np/lock/injector
  cadastre_injector:
    path: ../../../entities/np/cadastre/injector
  ecoidentificacion_repository:
    path: ../repository
  token:
    path: ../../../sources/token
  token_source_np:
    path: ../../../sources/token_source_np  
  element_injector:
    path: ../../../entities/np/element/injector
  equipment_injector:
    path: ../../../entities/np/equipment/injector
  lock_identification_injector:
    path: ../../../entities/np/lock_identification/injector
  token_source_sso:
    path: ../../../sources/token_source_sso
  sso_injector:
    path: ../../../entities/np/sso/injector
  sso_local:
    path: ../../../entities/np/sso/local
  message_user_injector:
    path: ../../../entities/np/message_user/injector
  residue_injector:
    path: ../../../entities/np/residue/injector
  preference_controller:
    path: ../../../entities/shared/preference/controller
  geographic_frame_injector:
    path: ../../../entities/np/geographic_frame/injector


  # SERVICIOS
  service_interface:
    path: ../../../interfaces/service  
  position_util_service:
    path: ../../../services/position_util  
  service_injector_quick_access:
    path: ../../../interfaces/service_injector_quick_access
  data_service:
    path: ../../../services/data
  sync_service:
    path: ../../../services/sync
  injector_service_global:
    path: ../../../services_global/injector
  package_info_service:
    path: ../../../services/package_info
  external_link_service:
    path: ../../../services/external_link  
  # firestore_service_global:
  #   path: ../../../services_global/firestore
  bluetooth_lock_service:
    path: ../../../services/bluetooth_lock_service
  map_info_service_here:
    path: ../../../services/map_info_here
  connection_provider_location:
    path: ../../../services/connection_provider_location
  connection_service:
    path: ../../../services/connection
  connection_provider_internet:
    path: ../../../services/connection_provider_internet
  connection_provider_gps:
    path: ../../../services/connection_provider_gps
  connection_state_provider_connectivity_plus:
    path: ../../../services/connection_state_provider_connectivity_plus
  # firebase_core_service_global:
  #   path: ../../../services_global/firebase_core
  package_info_plus_service:
    path: ../../../services/package_info_plus
  device_id_service_custom:
    path: ../../../services/device_id_custom
  restart_service_mobile:
    path: ../../../services/restart_mobile
  notification_service:
    path: ../../../services/notification
  server_time_service:
    path: ../../../services/server_time

  # OTROS
  widgets_map_v4:
    path: ../../../widgets/map_v4   
  animated_text_kit: ^4.2.1 
  pipeline_new_version:
    path: ../../../tools/pipeline_new_version
  pipeline_log:
    path: ../../../tools/pipeline_log
  pipeline_date_formatting:
    path: ../../../tools/pipeline_date_formatting
  
  # report_repository_mobile:
  #   path: ../../../entities/shared/report/repository_mobile
  translation_provider_json:
    path: ../../../services_global/translation_provider_json
  database_hive_provider:
    path: ../../../sources/database_hive_provider
  introduction_screen: ^3.0.0
  markdown_widget:
    path: ../../../widgets/markdown




flutter:
  uses-material-design: true
