import '../_imports.dart';

class IdentificationUseCaseValidateResult {
  final LockBase lock;
  final LockModel lockModel;
  final CitizenModel citizen;

  IdentificationUseCaseValidateResult(this.lock, this.lockModel, this.citizen);
}

class IdentificationUseCase {
  static final _tag = 'IdentificationUseCase';

  static Either<IdentificationUseCaseValidateResult, String> validateAccess(
      LockBase lock) {
    final lockModel = lockManager.models.getByBluetoothLockName(lock.name);

    if (lockModel == null)
      return Either.right(
          TTEI.noSeHanEncontradoLosDatosAsociadosALaCerradura.tt);

    if (global.citizen!.cards.isEmpty)
      return Either.right(
          "${TTEI.noTienesTarjetasAsociadas.tt}. ${TTEI.contacteConSuAyuntamiento.tt}");

    if (!lockModel.isCardAllowedAny(global.citizen!.cardIds))
      return Either.right(
          "${TTEI.usuarioNoPermitido.tt}. ${TTEI.contacteConSuAyuntamiento.tt}");

    if (user.isCitizen) {
      final calendars = lockModel.getCalendars(lockCalendarManager.models);

      if (calendars.isEmpty)
        return Either.right(
          TTEI.noSeHanEncontradoLosDatosAsociadosALaCerradura.tt,
        );

      final canOpenAny = calendars.any((c) => c.canOpen(now));

      if (!canOpenAny)
        return Either.right(
            "${TTEI.noSePuedeAbrirElContenedor.tt}. ${TTEI.estaFueraDelHorarioEstablecido.tt}");
    }

    return Either.left(
        IdentificationUseCaseValidateResult(lock, lockModel, global.citizen!));
  }

  static Stream<Either<String, Result<String>>> tryOpenDebug(
      LockBase lock) async* {
    if (!isReallyDebugMode()) {
      yield Either.right("No es debug".toResult());
      return;
    }

    int cardIdToOpen = 0;
    try {
      if (global.citizen!.cards.isEmpty) {
        yield Either.right("No hay tarjetas".toResult());
        return;
      }

      final random = Random().nextInt(global.citizen!.cards.length);
      final card = global.citizen!.cards[random];
      cardIdToOpen = card.openBlueetothLockId;
      yield Either.left(
          "Probando tarjeta aleatoria: ${card.cardId} - ${card.description}. Total: ${global.citizen!.cards.length}");

      final isConnected =
          await lock.connect().timeout(const Duration(seconds: 33));

      if (!isConnected) {
        yield Either.right(
            "${TTEI.noSeHaPodidoConectarConLaCerradura.tt} (1)".toResult());
        return;
      }

      yield Either.left(TTEI.identificando.tt.ellipsis());

      final result =
          await lock.open(cardIdToOpen).timeout(const Duration(seconds: 33));

      if (result) {
        yield Either.right(Result.valid());
        return;
      }
    } catch (e) {
      Log().debug(
          "[$_tag] [tryOpenDebug] Error al abrir la cerradura ${lock.name} con la tarjeta ${cardIdToOpen}: $e");
    }

    yield Either.right(
        "${TTEI.noSeHaPodidoConectarConLaCerradura.tt} (2)".toResult());
  }

  static Stream<Either<String, Result<String>>> tryOpen(
      IdentificationUseCaseValidateResult data) async* {
    int cardIdToOpen = 0;
    try {
      for (final card in data.citizen.cards) {
        if (data.lockModel.isCardAllowed(card.openBlueetothLockId)) {
          cardIdToOpen = card.openBlueetothLockId;
          break;
        }
      }

      if (cardIdToOpen == 0) {
        yield Either.right(
            TTEI.noEstasAutorizadoParaAbrirEstaCerradura.tt.toResult());
        return;
      }

      yield Either.left(TTShared.conectando.tt.ellipsis());

      final isConnected =
          await data.lock.connect().timeout(const Duration(seconds: 33));

      if (!isConnected) {
        yield Either.right(
            "${TTEI.noSeHaPodidoConectarConLaCerradura.tt} (3)".toResult());
        return;
      }

      yield Either.left(TTEI.identificando.tt.ellipsis());

      final isOpened = await data.lock
          .open(cardIdToOpen)
          .timeout(const Duration(seconds: 33));

      if (isOpened) {
        yield Either.right(Result.valid());
      } else {
        yield Either.right(
            TTEI.elIdentificadorNoHaPodidoVerificarse.tt.toResult());
      }
    } catch (e) {
      Log().error(
          "[$_tag] [tryOpen] Error al abrir la cerradura ${data.lock.name} con la tarjeta ${cardIdToOpen}: $e");
      yield Either.right(TTShared.haOcurridoUnError.tt.toResult());
    }
  }

  // Commented out because this part is hard to implement and for now we don't need it.
  // static Future<Result<String>> setTimeout(
  //     IdentificationUseCaseValidateResult data) async {
  //   if (!data.lockModel.hasInstalledLock) return Result.valid();

  //   final minutes = 3;

  //   for (var i = 0; i < (minutes * 2); i++) {
  //     await Future.delayed(Duration(seconds: 33));
  //     final isClosed = !(await data.lock.isOpen());
  //     if (isClosed) return Result.valid();
  //   }

  //   return "${TTEI.seHaAgotadoElTiempoMaximoDeAperturaDeCerradura.tt}. ${TTEI.vuelvaAIdentificarsePorFavor} "
  //       .toResultError();
  // }

  static Future<void> onPressedLock(
    LockBase lock,
    void Function() onCompleted,
  ) async {
    IdentificationUseCaseValidateResult? validationResult;

    await DialogService().showProgressDialog((dialog, context) async {
      if (isReallyDebugMode()) {
        await for (final response in tryOpenDebug(lock)) {
          if (response.isLeft) {
            dialog.update(response.left!);
            continue;
          }

          if (response.right!.isError) {
            dialog.error(response.right!.error!);
            return;
          }
        }

        dialog.valid(
          TTEI.identificadoCorrectamente.tt,
          onCompleted,
        );
        return;
      }

      // We pause the service to try to reduce the time to open the lock.
      await bluetoothLockService.pause();

      final validation = IdentificationUseCase.validateAccess(lock);
      if (validation.isRight) {
        dialog.error(validation.right!);
        return;
      }

      validationResult = validation.left!;

      await for (final response
          in IdentificationUseCase.tryOpen(validation.left!)) {
        if (response.isLeft) {
          dialog.update(response.left!);
          continue;
        }

        if (response.right!.isError) {
          dialog.error(response.right!.error!);
          return;
        }
      }

      dialog.valid(
        TTEI.identificadoCorrectamente.tt,
        onCompleted,
      );
    });

    bluetoothLockService.resume();

    if (validationResult == null) return;

    // final timeout = await IdentificationUseCase.setTimeout(validationResult!);
    // // ignore: unawaited_futures
    // if (timeout.isError) DialogService().showInfoDialog(timeout.error!);
  }

  static Future<IdentificationLockResult> onPressedLock2(
    LockBase lock,
    void Function() onCompleted,
  ) async {
    IdentificationUseCaseValidateResult? validationResult;

    if (isReallyDebugMode()) {
      await for (final response in tryOpenDebug(lock)) {
        if (response.right?.isError == true) {
          return IdentificationLockResult(
            message: response.right!.error!,
            isError: true,
          );
        }
      }

      onCompleted();
      return IdentificationLockResult(
        message: TTEI.identificadoCorrectamente.tt + " (Debug)",
        isError: false,
      );
    }

    await bluetoothLockService.pause();

    final validation = IdentificationUseCase.validateAccess(lock);
    if (validation.isRight) {
      return IdentificationLockResult(
        message: validation.right!,
        isError: true,
      );
    }

    validationResult = validation.left!;

    await for (final response
        in IdentificationUseCase.tryOpen(validationResult)) {
      if (response.right?.isError == true) {
        return IdentificationLockResult(
          message: response.right!.error!,
          isError: true,
        );
      }
    }

    bluetoothLockService.resume();

    onCompleted();
    return IdentificationLockResult(
      message: TTEI.identificadoCorrectamente.tt,
      isError: false,
    );
  }
}

class IdentificationLockResult {
  final String message;
  final bool isError;

  IdentificationLockResult({required this.message, required this.isError});
}