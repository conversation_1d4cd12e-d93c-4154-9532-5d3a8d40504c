import '../../_imports.dart';

class UserProfileScreen extends StatefulWidget {
  final UserProfileProvider provider;

  const UserProfileScreen({
    super.key,
    required this.provider,
  });

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  bool isVisibleInfo = false;
  bool isVisibleConfig = false;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToEnd() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _scrollController.animateTo(_scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
    });
  }

  @override
  Widget build(BuildContext context) {
    final configIcon = isVisibleConfig
        ? Icons.keyboard_arrow_down_outlined
        : Icons.arrow_forward_ios;

    return StreamBuilder(
      stream: widget.provider.rebuild.stream,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        // Informacion general
        return ScaffoldCustom(
          isScrollable: false,
          padding: EdgeInsets.zero,
          isCenter: true,
          body: SingleChildScrollView(
            controller: _scrollController,
            child: Container(
              alignment: Alignment.topCenter,
              child: Container(
                constraints: const BoxConstraints(
                  maxWidth: globalMaxWidthContainer,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Margin.v(),
                    Margin.v(),

                    // Perfil
                    GestureDetector(
                      onTap: () {
                        // onPressedMenuAction(MenuAction.perfil);
                      },
                      child: Column(
                        children: [
                          // Imagen
                          FutureBuilder<String>(
                            future: userSSOUseCase.getImageByPk(
                                UserSSOPK(user.id!, user.role.id)),
                            builder: (_, snapshot) => AvatarImageContent(
                              image: snapshot.data ?? "",
                              icon: FontAwesomeIcons.user,
                              radius: 60,
                            ),
                          ),

                          Margin.v(marginm),

                          // Nombre
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  TextBase(
                                    user.name,
                                    bold: true,
                                    fontSize: 20,
                                    elipsis: true,
                                  ),
                                  Container(
                                    margin: EdgeInsets.symmetric(
                                      horizontal: marginxxs,
                                    ),
                                    child: Icon(
                                      Icons.edit,
                                      color: colorPrimarioOscuro,
                                    ),
                                  )
                                ],
                              ),
                              Margin.v(),
                              TextBase(
                                user.role.translation,
                                textType: TextType.caption,
                              ),
                            ],
                          ),
                        ],
                      ).cardBase(padding: const EdgeInsets.all(16)),
                    ),

                    ///Ajustes
                    Margin.v(),
                    TextTitle(
                      title: tt(TTShared.ajustes),
                      child: Margin.v(),
                    ),

                    GenericButton(
                      color: colorBlanco,
                      iconRight: Icons.arrow_forward_ios,
                      iconLeft: Icons.info,
                      textColor: colorNegro,
                      title: tt(TTShared.informacionDeLaAPP),
                      borderColor: colorBorder,
                      textAlign: TextAlign.start,
                      onPressed: () {
                        setState(() {
                          isVisibleInfo = !isVisibleInfo;
                        });
                        _scrollToEnd();
                      },
                    ),
                    Margin.v(),
                    GenericButton(
                      color: colorBlanco,
                      iconRight: Icons.arrow_forward_ios,
                      iconLeft: Icons.home_rounded,
                      textColor: colorNegro,
                      title: tt(TTEI.domicilios.tt),
                      borderColor: colorBorder,
                      textAlign: TextAlign.start,
                      onPressed: () => NavigatorService()
                          .push(AddressListPage(AddressListCubit(global.citizen!.addresses))),
                    ),
                    Margin.v(),
                    GenericButton(
                        color: colorBlanco,
                        iconRight: Icons.arrow_forward_ios,
                        iconLeft: Icons.sync,
                        textColor: colorNegro,
                        title: tt(TTShared.sincronizar),
                        borderColor: colorBorder,
                        textAlign: TextAlign.start,
                        onPressed: () => DialogService().showProgressDialog(
                              (dialog, context) async {
                                try {
                                  injector.get<SyncServiceBase>().forceSync();
                                  NavigatorService().popToMainPage();
                                } catch (e) {
                                  dialog.error(Exception() as String?);
                                }
                              },
                            )),
                    // Si no lo vamos a usar de forma correcta, pues no activarlo aún
                    // Margin.v(),
                    // GenericButton(
                    //   color: colorBlanco,
                    //   iconRight: Icons.arrow_forward_ios,
                    //   iconLeft: Icons.bug_report,
                    //   textColor: colorNegro,
                    //   title: tt(TTShared.enviarReporte),
                    //   borderColor: colorBorder,
                    //   textAlign: TextAlign.start,
                    //   onPressed: () {},
                    // ),
                    Margin.v(),
                    GenericButton(
                      color: colorBlanco,
                      iconRight: Icons.arrow_forward_ios,
                      iconLeft: Icons.restore,
                      textColor: colorNegro,
                      title: tt(TTShared.restablecerDatos),
                      borderColor: colorBorder,
                      textAlign: TextAlign.start,
                      onPressed: () async {
                        // En web no se la ventana de confirmación sin un delay.
                        await Future.delayed(const Duration(milliseconds: 200));
                        final isConfirmed =
                            await (DialogService().showConfirmDialog(
                          "${tt(TTShared.quieresRestablecerLosDatosPregunta)}\n\n${tt(TTShared.todosLosDatosSeRestableceranYSeraNecesarioVolverAIniciarSesion)}",
                        ));

                        if (isConfirmed)
                          DialogService()
                              .showProgressDialog((dialog, context) async {
                            await DatabaseHiveService()
                                .repair(isRemovedAll: true);
                            await injector.get<RestartServiceBase>().restart();
                          });
                      },
                    ),
                    Margin.v(),
                    GenericButton(
                        color: colorBlanco,
                        iconRight: Icons.arrow_forward_ios,
                        iconLeft: Icons.translate,
                        textColor: colorNegro,
                        title: tt(TTShared.idioma),
                        borderColor: colorBorder,
                        textAlign: TextAlign.start,
                        onPressed: () {
                          GlobalCubit()
                              .translationProvider
                              .showLanguagueSelection(global.languages);
                        }),

                    Margin.v(),
                    GenericButton(
                        color: colorBlanco,
                        iconRight: Icons.arrow_forward_ios,
                        iconLeft: Icons.location_city,
                        textColor: colorNegro,
                        title: tt(TTSSO.registrarseEnOtroMunicipio),
                        borderColor: colorBorder,
                        textAlign: TextAlign.start,
                        onPressed: () {
                          DialogService()
                              .showProgressDialog((dialog, context) async {
                            final result =
                                await LoginUseCase.createAccessMunicipality(
                              app: user.app,
                              ssoUseCase: ssoUseCase,
                              email: user.email,
                              currentMunicipaliyIdSSO: user.companyIdSSO,
                            );

                            if (result == null) return null;

                            if (result.error != null)
                              return dialog.error(result.error!);

                            return dialog.valid(tt(
                                  TTSSO.teHasRegistroEnX,
                                  [result.municipality!.name],
                                ).jump() +
                                TTSSO
                                    .vuelveAIniciarSesionParaPoderSeleccionarElNuevoMunicipio
                                    .tt);
                          });
                        }),

                    Margin.v(),
                    GenericButton(
                      color: colorError,
                      iconRight: Icons.arrow_forward_ios,
                      iconLeft: Icons.logout,
                      // textColor: colorNegro,
                      title: tt(TTShared.cerrarSesion),
                      // borderColor: colorError,
                      textAlign: TextAlign.start,
                      onPressed: () async {
                        final response = await DialogService()
                            .showConfirmDialog(
                                tt(TTShared.quieresCerrarSesion));

                        if (!isTrue(response)) return;
                        NavigatorService().popToMainPage();

                        await DialogService()
                            .showProgressDialog((dialog, context) async {
                          await GlobalCubit().onLogout();
                        });
                      },
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 20),
                      child: Visibility(
                        visible: isVisibleInfo,
                        child: FutureBuilder<PackageInfoData?>(
                          future: packageInfoService.get(),
                          builder: (context, snapshot) {
                            return Column(
                              children: [
                                BottomVersionWidget(externalLinkService,
                                    packageInfoService, null),
                                SeparatorFiori.half(),
                                if (isDebugMode()) ...[
                                  const Divider(),
                                  TextBase(
                                    'package'.concat(
                                        snapshot.data?.packageName ?? ""),
                                    textType: TextType.caption,
                                  ),
                                  TextBase(
                                    'appName'
                                        .concat(snapshot.data?.appName ?? ''),
                                    textType: TextType.caption,
                                  ),
                                ],
                              ],
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ).marginH().scroll().marginSymmetric(
                    horizontal: marginxxs, vertical: marginxxs),
              ),
            ),
          ),
        );
      },
    );
  }
}
