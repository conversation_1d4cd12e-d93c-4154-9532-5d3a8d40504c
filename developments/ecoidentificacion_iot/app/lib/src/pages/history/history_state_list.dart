import '../../_imports.dart';

class HistoryStateList extends HistoryStateBase {
  final List<LockIdentificationModel> identifications;
  final HistoryFilterType filterType;
  final DateTime? date;

  HistoryStateList(this.identifications, this.filterType, this.date);

  @override
  List<Object> get props => [
        identifications.map2((e) => e.id),
        filterType,
        date ?? now,
      ];
}

class HistoryStateListScreen extends StatelessWidget {
  final HistoryStateList state;

  HistoryStateListScreen(this.state);

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<HistoryCubit>();

    return Stack(
      children: [
        state.identifications.isEmpty
            ? Center(
                child: BodyText(
                  TTEI.noSeHanEncontradoIdentificaciones.tt,
                ),
              )
            : ListViewAnimated(
                padding: themeContainers.scaffoldPadding
                    .get()
                    .get()
                    .copyWith(top: 120, bottom: 10),
                paddingChild: const EdgeInsets.only(bottom: 20),
                children: state.identifications.map2(
                  (e) => HistoryItem(identification: e),
                ),
              ),
        Container(
          padding: themeContainers.scaffoldPadding
              .get()
              .get()
              .copyWith(top: 0, bottom: 0),
          child: const InfoHeader(),
        ),
        Container(
          width: double.maxFinite,
          height: 50,
          padding: themeContainers.scaffoldPadding
              .get()
              .get()
              .copyWith(top: 0, bottom: 0),
          margin: const EdgeInsets.only(top: 55),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: 37,
                  decoration: BoxDecoration(
                    color: themeColors.primaryBlueLight,
                    borderRadius:
                        BorderRadius.circular(themeContainers.borderRadius),
                  ),
                  child: DropdownButton<HistoryFilterType>(
                    // isDense: true,
                    isExpanded: true,
                    iconEnabledColor: themeColors.white,
                    value: state.filterType,
                    underline: const SizedBox(),
                    dropdownColor: themeColors.primaryBlueLight,
                    items: HistoryFilterType.values.map2(
                      (e) => DropdownMenuItem(
                        alignment: Alignment.centerLeft,
                        value: e,
                        child: Container(
                          padding: const EdgeInsets.only(left: 10),
                          child: BodyText(
                            e.tt,
                            color: themeColors.white,
                          ),
                        ),
                      ),
                    ),
                    onChanged: (ee) {
                      if (ee == null) return;
                      cubit.onChangedFilters(ee, state.date);
                    },
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: FadeInOutContainer(
                  getIsShowed: () =>
                      state.filterType != HistoryFilterType.last15Days,
                  child: GenericIconButton(
                    onPressed: () {
                      DialogService()
                          .showDatePickerExtendedDialog(initialDate: state.date)
                          .then(
                            (value) => cubit.onChangedFilters(
                              state.filterType,
                              value ?? state.date,
                            ),
                          );
                    },
                    elevation: 0,
                    icon: FontAwesomeIcons.calendar,
                    color: themeColors.primaryBlueLight,
                    iconColor: themeColors.white,
                    textColor: themeColors.white,
                    text: state.date?.viewDate() ?? now.viewDate(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
