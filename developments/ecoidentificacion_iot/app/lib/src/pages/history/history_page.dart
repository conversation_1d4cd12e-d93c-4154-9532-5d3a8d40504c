import '../../_imports.dart';

abstract class HistoryStateBase extends Equatable {}

class HistoryPage extends StatefulWidget {
  final HistoryCubit cubit;

  HistoryPage(
    this.cubit,
  );

  @override
  State<HistoryPage> createState() => _HistoryPageState();
}

class _HistoryPageState extends State<HistoryPage> {
  @override
  void initState() {
    widget.cubit.init();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<HistoryCubit>(
      create: (context) => widget.cubit,
      child: BlocBuilder<HistoryCubit, HistoryStateBase>(
        builder: (context, state) => ScaffoldCustom(
          padding: EdgeInsets.zero,
          isScrollable: false,
          backgroundColor: themeColors.greyBackground,
          appBar: CustomAppBar(TTEI.identificaciones.tt),
          endDrawer: const HomeDrawer(page: CurrentPage.history),
          body: SafeArea(
            child: ResponsiveGlobalBuilder(
              child: Builder(
                builder: (_) {
                  if (state is HistoryStateLoading)
                    return HistoryStateLoadingScreen();
                  else if (state is HistoryStateEmpty)
                    return HistoryStateEmptyScreen();
                  else if (state is HistoryStateList)
                    return HistoryStateListScreen(state);
                  return Container();
                },
              ),
            ),
          ),
        ),
      ),
    );
  }
}
