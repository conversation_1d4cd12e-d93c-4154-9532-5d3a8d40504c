import '../../_imports.dart';

class LockListItem extends StatelessWidget {
  final LockBase lock;
  final void Function() onPressed;

  LockListItem(this.lock, this.onPressed);

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<LockListCubit>();

    final data = IdentificationUseCase.validateAccess(lock);
    final lockModel = lockManager.models.firstWhere2((m) =>
        m.lockBlueetothName == lock.name &&
        m.isCardAllowedAny(global.citizen!.cardIds));

    double? distance;
    ElementModel? element;
    EquipmentModel? equipment;

    if (lockModel != null) {
      element = lockModel.getElement(elementManager.models);
      if (element != null) {
        equipment = element.getEquipment(equipmentManager.modelsMap);
        distance = positionUtilService.getDistanceInMeters(
          point1Lat: element.lat,
          point1Long: element.lng,
          point2Lat: connection.currentLatLng?.lat,
          point2Long: connection.currentLatLng?.long,
        );
      }
    }

    String name = TTShared.desconocido.tt;

    if (element != null)
      name = element.name;
    else if (lock.name.isNotEmpty) name = lock.name;

    return ColoredTopListItem(
      onPressed: onPressed,
      topContent: [
        Expanded(
          child: BodyText(
            name,
            textAlign: TextAlign.start,
            color: themeColors.white,
          ),
        )
      ],
      bottomContent: [
        TooltipCustom(
          message:
              data.isLeft ? TTShared.disponible.tt : TTShared.noDisponible.tt,
          triggerMode: TooltipTriggerMode.tap,
          child: Row(
            children: [
              Icon(
                FontAwesomeIcons.lock,
                size: 18,
                color:
                    data.isLeft ? themeColors.validGreen : themeColors.errorRed,
              ),
              const SizedBox(width: 20),
            ],
          ),
        ),
        FutureBuilder<int>(
          future: lock.getSignalStrength(),
          initialData: lock.lastSignalStrength,
          builder: (_, snapshot) {
            String signal = TTShared.desconocido.tt;
            if (snapshot.data != null) {
              try {
                signal =
                    (min(max(2 * (snapshot.data! + 100), 0), 100)).toString() +
                        "%";
              } on Exception {}
            }

            return TooltipCustom(
              message: TTEI.intensidadDeLaSenial.tt,
              triggerMode: TooltipTriggerMode.tap,
              child: Row(
                children: [
                  Icon(
                    FontAwesomeIcons.signal,
                    size: 18,
                    color: themeColors.primaryBlue,
                  ),
                  const SizedBox(width: 10),
                  BodyText(
                    signal,
                    color: themeColors.primaryBlue,
                  ),
                  const SizedBox(width: 10),
                ],
              ),
            );
          },
        ),
        if (distance != null)
          TooltipCustom(
            message: TTShared.distancia.tt +
                " (${TTShared.metros.tt.toLowerCase()})",
            triggerMode: TooltipTriggerMode.tap,
            child: Row(
              children: [
                Icon(
                  FontAwesomeIcons.personWalking,
                  size: 18,
                  color: themeColors.primaryBlue,
                ),
                const SizedBox(width: 5),
                BodyText(
                  '${distance > 10 ? '10+' : distance} m',
                  color: themeColors.primaryBlue,
                ),
                const SizedBox(width: 10),
              ],
            ),
          ),
      ],
      image: element != null && equipment != null
          ? Padding(
              padding: const EdgeInsets.all(3),
              child: element.getIcon(equipment),
            )
          : Padding(
              padding: const EdgeInsets.all(8).copyWith(right: 12),
              child: Icon(
                FontAwesomeIcons.dumpster,
                color: themeColors.grey8,
              ),
            ),
    );
  }
}
