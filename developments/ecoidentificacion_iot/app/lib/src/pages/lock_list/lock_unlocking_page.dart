import '../../_imports.dart';
import 'dart:ui';

class LockUnlockingState extends Equatable {
  final LockBase lock;

  LockUnlockingState(this.lock);

  @override
  List<Object?> get props => [
        lock.id,
      ];

  LockUnlockingState copyWith({
    LockBase? lock,
  }) {
    return LockUnlockingState(
      lock ?? this.lock,
    );
  }
}

class LockUnlockingPage extends StatefulWidget {
  final LockUnlockingState state;

  const LockUnlockingPage(this.state, {super.key});

  @override
  State<LockUnlockingPage> createState() => _LockUnlockingPageState();
}

class _LockUnlockingPageState extends State<LockUnlockingPage> {
  @override
  void initState() {
    super.initState();
    tryToUnlock();
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: const Color(0xff213984),
      body:  Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
       RadarPulseAnimation(
            size: screenWidth,
            pulseCount: 5,
            duration: const Duration(seconds: 6),
            baseColor: const Color(0xFF7F97E2),
          ),
          Padding(
            padding:const EdgeInsets.symmetric(horizontal: 32, vertical: 10),
            child: Text(
              TTEI.sostenTuTelefonoCercaDelContenedor.tt,
              textAlign: TextAlign.center,
              style:const TextStyle(
                fontSize: 30,
                color: Colors.white,
                height: 1.2,
                fontWeight: FontWeight.w900,
                letterSpacing: 0,
                fontFamily: 'Roboto',
              ),
            ),
          ),
        const  SizedBox(height: 40),
        ],
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        height: 106,
        alignment: Alignment.center,
        child: AddressRegisterFooterItem(
          title: TTShared.cerrar.tt,
          isSecondary: true,
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
    );
  }

 Future<void> tryToUnlock() async {
  IdentificationLockResult result =
      await IdentificationUseCase.onPressedLock2(widget.state.lock, () {});

  if (!mounted) return; // 🛡️ Asegura que no se intente usar context si el widget ya no existe

  if (result.isError) {
    DialogService().showDialogEID(
      TTEI.haHabidoUnErrorAlIntentarAbrirElContenedor.tt,
      subtitle: TTShared.quieresVolverAIntentarlo.tt,
      icon: Icons.cancel_outlined,
      iconColor: const Color(0xffCD3232),
      secondaryButtonText: TTShared.cerrar.tt,
      onSecondaryButtonPressed: () {
        if (Navigator.of(context).canPop()) Navigator.of(context).pop();
        if (Navigator.of(context).canPop()) Navigator.of(context).pop();
      },
      primaryButtonText: TTShared.volverAIntentar.tt,
      onPrimaryButtonPressed: () {
        if (Navigator.of(context).canPop()) Navigator.of(context).pop();
        tryToUnlock();
      },
    );
  } else {
    DialogService().showDialogEID(
      TTEI.contenedorAbiertoConExito.tt,
      icon: Icons.check_circle_outline,
      iconColor: const Color(0xff4CAF50),
      primaryButtonText: TTShared.aceptar.tt,
      onPrimaryButtonPressed: () {
        if (Navigator.of(context).canPop()) Navigator.of(context).pop();
        if (Navigator.of(context).canPop()) Navigator.of(context).pop();
      },
    );
  }
}

}
