import '../../_imports.dart';

class LockListPage extends StatefulWidget {
  final LockListCubit cubit;

  LockListPage(this.cubit);

  @override
  State<LockListPage> createState() => _LockListState();
}

class _LockListState extends State<LockListPage> {
  List<StreamSubscription> subscriptions = [];

  bool _showRefreshingIcon = false;
  Timer? _refreshIconTimer;

  @override
  void initState() {
    subscriptions.add(
      bluetoothLockService.onReceiveLocks.listen(
        (s) {
          if (!mounted) return;
          widget.cubit.onDetectedNewLocks(s);
        },
      ),
    );

    subscriptions.add(
      bluetoothLockService.onScanning.listen(
        (s) {
          if (!mounted) return;
          setState(() {});
        },
      ),
    );

    super.initState();
  }

  @override
  void dispose() {
    subscriptions.forEach((s) => s.cancel());
    _refreshIconTimer?.cancel();
    super.dispose();
  }

  Future<void> _onPressedRefresh() async {
    if (bluetoothLockService.isScanning) return;

    setState(() {
      _showRefreshingIcon = true;
    });

    _refreshIconTimer?.cancel();
    _refreshIconTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showRefreshingIcon = false;
        });
      }
    });

    final isTurnedOn = await bluetoothLockService.turnOn();
    if (!isTurnedOn) {
      DialogService().showSnackbar(TTEI.elBluetoothNoEstaActivo.tt);
      return;
    }

    // The bluetooth plugin is stopped automatically, so we need to force it to scan again.
    // This is related to the time set in [_cancellableBluetooth] (1 second less).
    await bluetoothLockService.startScan(const Duration(seconds: 20));
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<LockListCubit>(
      create: (context) => widget.cubit,
      child: BlocBuilder<LockListCubit, LockListState>(
        builder: (context, state) {
          return ScaffoldCustom(
            padding: EdgeInsets.zero,
            isScrollable: false,
            appBar: CustomAppBar(
              TTEI.identificacion.tt + " (${state.locks.length})",
              actions: [
                FadeInOutContainer(
                  getIsShowed: () => !bluetoothLockService.isScanning,
                  child: GestureDetector(
                    child:
                        (bluetoothLockService.isScanning || _showRefreshingIcon)
                            ? const SpinningIcon(icon: FontAwesomeIcons.arrowsRotate)
                            : const Icon(FontAwesomeIcons.arrowsRotate),
                    onTap: () => _onPressedRefresh(),
                  ),
                ),
              ],
            ),
            body: Stack(
              children: [
                state.locks.isEmpty
                    ? Column(
                        children: [
                          _textInfo(
                            TTEI.noSeHaEncontradoNingunContenedorPermitidoCerca.tt,
                            TTEI.aproximateAUnoDeTusContenedoresParaPoderAbrirlo.tt,
                          ),
                          FadeInOutContainer(
                            getIsShowed: () =>
                                bluetoothLockService.isScanning ||
                                _showRefreshingIcon,
                            isRemovedWhenNotShowed: true,
                            child: Column(children: [
                              SeparatorFiori.half(),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Transform.scale(
                                      scale: 0.7, child: LoadingContainer()),
                                  const SizedBox(width: 10),
                                  BodyText(TTShared.buscando.tt.ellipsis()),
                                ],
                              ),
                              SeparatorFiori.half(),
                            ]),
                          ),
                        ],
                      )
                    : Column(
                        children: [
                          _textInfo(
                            TTEI.seleccionaElContenedorQueQuiereAbrir.tt,
                            TTEI.aquiPuedesVerTusContenedoresSeleccionaElQueQuieresAbrir
                                .tt
                          ),
                          ListViewAnimated(
                            shrinkWrap: true,
                            reAnimateOnVisibility: true,
                            children: state.locks.map2(
                              (m) => LockListItem(
                                m,
                                () => NavigatorService().push(
                                  LockUnlockingPage(LockUnlockingState(m)),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
              ],
            ),
            footer: Container(
              padding: const EdgeInsets.fromLTRB(24, 16, 24, 16),
              child: Column(
                children: [
                  CustomBottom(
                    enabled: !(bluetoothLockService.isScanning ||
                        _showRefreshingIcon),
                    label: TTShared.volverABuscar.tt,
                    secondary: true,
                    onTap: () => _onPressedRefresh(),
                    icon: FontAwesomeIcons.arrowsRotate,
                  ),
                  const SizedBox(height: 16),
                  CustomBottom(
                    label: TTShared.cerrar.tt,
                    onTap: () => NavigatorService().pop(),
                    enabled: true,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

_textInfo(String title, String subtitle) {
  return Container(
    padding: const EdgeInsets.fromLTRB(24, 32, 24, 32),
    child: Column(
      children: [
        Text(
          title,
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 26,
            fontWeight: FontWeight.w700,
            height: 1.3,
            letterSpacing: 0,
            fontFamily: 'Roboto',
          ),
        ),
        const SizedBox(height: 16),
        Text(
          subtitle,
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            height: 1.2,
            letterSpacing: 0,
            fontFamily: 'Roboto',
          ),
        ),
      ],
    ),
  );
}
