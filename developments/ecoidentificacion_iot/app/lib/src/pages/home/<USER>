// ignore_for_file: public_member_api_docs, sort_constructors_first
import '../../_imports.dart';

class HomeState extends Equatable {
  final List<LockBase> locks;
  final String? message;

  HomeState(this.locks, this.message);
  @override
  List<Object?> get props => [
        ...locks.map2((e) => e.id),
        message,
      ];

  HomeState copyWith({
    List<LockBase>? locks,
    String? message,
  }) {
    return HomeState(
      locks ?? this.locks,
      message ?? this.message,
    );
  }
}

class HomeCubit extends BlocBase<HomeState> {
  HomeCubit() : super(HomeState([], null));
  
  MapBlocBasic mapBloc = MapBlocBasic(
    connection,
    getBoundLimit: () async {
      final u = await getMapBoundLimit(connection, syncService);
      if (u == null) return null;
      return MapBoundLimit(
        northEast: u.object1,
        southWest: u.object2,
        positionUtilService: positionUtilService,
      );
    },
    languageType: TranslationServiceGlobal().language,
  );

  Future<void> centerInMap({
    required LatLng ll,
    required String id,
    required String category,
  }) async {
    NavigatorService().popToMainPage();
    await Future.delayed(const Duration(milliseconds: 200));
    mapBloc.centerMapStream(
      MapCenterData.withElementZoom(ll),
    );
    Future.delayed(
      const Duration(milliseconds: 100),
      () => mapBloc.markerPlugin
          .stateChange(id, category, MarkerCustomState.jumping),
    );
  }

  void onDetectedNewLocks(List<LockBase> s) {
    emit(state.copyWith(locks: s));
  }
}
