import '../../_imports.dart';

class TutorialPage extends StatefulWidget {
  final void Function(BuildContext context) onCompleted;

  TutorialPage(this.onCompleted);

  @override
  _TutorialPageState createState() => _TutorialPageState();
}

class _TutorialPageState extends State<TutorialPage> {
  final introKey = GlobalKey<IntroductionScreenState>();

  void _goToNextPage() {
    introKey.currentState?.next();
  }

  @override
  Widget build(BuildContext context) {
    final pageDecoration = PageDecoration(
      contentMargin: EdgeInsets.zero,
      titlePadding: const EdgeInsets.only(
        top: 32,
        left: 24,
        right: 24,
        bottom: 16,
      ),
      pageMargin: EdgeInsets.zero,
      bodyPadding: const EdgeInsets.symmetric(horizontal: 24.0),
      pageColor: themeColors.white,
      imagePadding: EdgeInsets.zero,
      imageAlignment: Alignment.center,
      imageFlex: 0,
      footerFlex: 0,
      bodyFlex: 1,
      footerPadding: const EdgeInsets.symmetric(
        horizontal: 24,
        vertical: 16,
      ),
    );

    return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        insetPadding: const EdgeInsets.symmetric(
          horizontal: 24.0,
          vertical: 52,
        ),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(30),
              child: IntroductionScreen(
                key: introKey,
                globalBackgroundColor: themeColors.white, // 👈 Importante
                pages: [
                  PageViewModel(
                    titleWidget: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '¡${TTShared.bienvenido.tt}!',
                          textAlign: TextAlign.left,
                          style: const TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.w700,
                            fontFamily: 'Roboto',
                            height: 1.2,
                            letterSpacing: 0,
                            color: Color(0xFF1E1E1E),
                          ),
                        ),
                        Text(
                          TTEI.descubreTodasLasVentajasDeLaAPP.tt,
                          textAlign: TextAlign.left,
                          style: const TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.w400,
                            fontFamily: 'Roboto',
                            height: 1.2,
                            letterSpacing: 0,
                            color: Color(0xFF1E1E1E),
                          ),
                        ),
                      ],
                    ),
                    bodyWidget: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildBullet(
                            TTShared.abre.tt, TTShared.contenedores.tt),
                        _buildBullet(
                            TTShared.encuentra.tt, TTShared.tusContenedores.tt),
                        _buildBullet(
                            TTShared.registra.tt, TTShared.tusDomicilios.tt),
                      ],
                    ),
                    footer: _footer(_goToNextPage),
                    image: ImageAssetFadeContainer(
                        'assets/images/onboarding_1.jpg'),
                    decoration: pageDecoration,
                  ),
                  PageViewModel(
                    titleWidget: _title(TTEI.abreContenedoresConTuApp.tt),
                    bodyWidget: _body(TTEI
                        .conLaAplicacionPuedesAbrirTusContenedoresAsignadosYTenerUnHistoricoDeSuUso
                        .tt),
                    footer: _footer(_goToNextPage),
                    image: ImageAssetFadeContainer(
                        'assets/images/onboarding_2.jpg'),
                    decoration: pageDecoration,
                  ),
                  PageViewModel(
                    titleWidget:
                        _title(TTEI.encuentraTusContenedoresMasCercanos.tt),
                    bodyWidget: _body(
                      TTEI.localizaEnElMapaTusContenedoresMasCercanos.tt,
                    ),
                    footer: _footer(_goToNextPage),
                    image: ImageAssetFadeContainer(
                        'assets/images/onboarding_3.jpg'),
                    decoration: pageDecoration,
                  ),
                  PageViewModel(
                    titleWidget: _title(TTEI.registraTusDomicilios.tt),
                    bodyWidget: _body(
                      TTEI.registraTusDomiciliosEIdentificalosEnElMapaParaPoderVerTusContenedoresAsignados
                          .tt,
                    ),
                    footer:
                        _footer(() => widget.onCompleted(context), last: true),
                    image: ImageAssetFadeContainer(
                        'assets/images/onboarding_4.jpg'),
                    decoration: pageDecoration,
                  ),
                ],
                onDone: () => widget.onCompleted(context),
                showDoneButton: false,
                showBackButton: false,
                showSkipButton: false,
                showNextButton: false,
                rtl: false,
                curve: Curves.fastLinearToSlowEaseIn,
                bodyPadding: EdgeInsets.zero,
                controlsMargin: const EdgeInsets.only(bottom: 12),
                controlsPadding: EdgeInsets.zero,
                dotsDecorator: const DotsDecorator(
                  size: Size(10.0, 10.0),
                  color: Color(0xFFBDBDBD),
                  activeSize: Size(22.0, 10.0),
                  activeShape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(25.0)),
                  ),
                ),
              ),
            ),
            // Botón 'X' en la esquina superior derecha
            Positioned(
              top: 15,
              right: 15,
              child: GestureDetector(
                onTap: () => widget.onCompleted(context),
                child: const Icon(
                  Icons.close,
                  size: 28,
                  color: Color(0xFfffffff),
                ),
              ),
            ),
          ],
        ));
  }

  Widget _title(String text) => Text(
        text,
        textAlign: TextAlign.left,
        style: const TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.w700,
          fontFamily: 'Roboto',
          height: 1.2,
          letterSpacing: 0,
          color: Color(0xFF1E1E1E),
        ),
      );

  Widget _body(String text) => Text(
        text,
        textAlign: TextAlign.left,
        style: const TextStyle(
          fontSize: 16.0,
          color: Color(0xFF1E1E1E),
          fontWeight: FontWeight.w400,
          fontFamily: 'Roboto',
          height: 1.2,
          letterSpacing: 0,
        ),
      );

  Widget _footer(void Function()? onTapNext, {bool last = false}) {
    return Column(
      children: [
        GestureDetector(
          onTap: onTapNext,
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xff3358C7),
              borderRadius: BorderRadius.circular(30),
            ),
            height: 53,
            margin: const EdgeInsets.symmetric(
              vertical: 8,
              horizontal: 24,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  last ? TTShared.empezarAUsar.tt : TTShared.siguiente.tt,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Roboto',
                    height: 1.2,
                    letterSpacing: 0,
                    color: Color(0xFFE7EDFF),
                  ),
                ),
                if (!last) ...[
                  const SizedBox(width: 8),
                  const Icon(Icons.arrow_forward_ios_outlined,
                      color: Color(0xFFE7EDFF), size: 20),
                ]
              ],
            ),
          ),
        ),
        GestureDetector(
          onTap: () => widget.onCompleted(context),
          child: Text(
            TTShared.saltarIntroduccion.tt,
            style: const TextStyle(
              fontSize: 16,
              decoration: TextDecoration.underline,
              fontWeight: FontWeight.w400,
              fontFamily: 'Roboto',
              height: 1.4,
              letterSpacing: 0,
              color: Color(0xFf1E1E1E),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBullet(String boldText, String normalText) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const Text('•\t', style: TextStyle(fontSize: 20)),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: const TextStyle(
                color: Color(0xFF1E1E1E),
                fontSize: 20,
                fontFamily: 'Roboto',
                height: 1,
                letterSpacing: 0,
              ),
              children: [
                TextSpan(
                  text: ' $boldText ',
                  style: const TextStyle(
                    fontWeight: FontWeight.w700,
                  ),
                ),
                TextSpan(
                  text: normalText,
                  style: const TextStyle(
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
