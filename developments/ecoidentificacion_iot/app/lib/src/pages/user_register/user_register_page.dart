import '../../_imports.dart';

class UserRegisterPage extends StatefulWidget {
  final UserRegisterCubit cubit;

  UserRegisterPage(this.cubit);

  @override
  _UserRegisterPageState createState() => _UserRegisterPageState();
}

class _UserRegisterPageState extends State<UserRegisterPage> {
  // We store the controllers here for store the information if the
  // user decide return to server selection state.
  final name = FieldController(isRequired: true);
  final surname = <PERSON>Controller(isRequired: true);
  final email = FieldController(isRequired: true);
  final repeatEmail = FieldController(isRequired: true);
  final password = FieldController(isRequired: true);
  final repeatPassword = FieldController(isRequired: true);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    name.dispose();
    surname.dispose();
    email.dispose();
    repeatEmail.dispose();
    password.dispose();
    repeatPassword.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<UserRegisterCubit>(
      create: (_) => widget.cubit,
      child: BlocBuilder<UserRegisterCubit, UserRegisterStateBase>(
        builder: (context, state) {
          if (state is UserRegisterStateForm)
            return UserRegisterStateFormScreen(
              state: state,
              name: name,
              surname: surname,
              email: email,
              repeatEmail: repeatEmail,
              password: password,
              repeatPassword: repeatPassword,
            );
          return SizedBox();
        },
      ),
    );
  }
}
