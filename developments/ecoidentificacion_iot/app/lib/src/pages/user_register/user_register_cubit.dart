import '../../_imports.dart';

abstract class UserRegisterStateBase extends Equatable {}

class UserRegisterCubit extends BlocBase<UserRegisterStateBase> {
  UserRegisterCubit(List<MunicipalityApp> municipality)
      : super(
          UserRegisterStateForm(
            MunicipalityAppDropdownState(municipality, municipality.first),
          ),
        );

  Future<Result<String>> onPressedConfirm({
    required String email,
    required String repeatEmail,
    required String password,
    required String repeatPassword,
    required String name,
    required String surname,
  }) async {
    if (password != repeatPassword)
      return TTShared.lasContraseniasNoCoinciden.tt.toResult();

    if (email != repeatEmail)
      return TTShared.losEmailsNoCoinciden.tt.toResult();

    if (name.trim().isEmpty)
      return TTShared.nombre.tt
          .colon(TTShared.noSeHaAsignadoUnValor.tt)
          .toResult();

    if (surname.trim().isEmpty)
      return TTShared.apellidos.tt
          .colon(TTShared.noSeHaAsignadoUnValor.tt)
          .toResult();

    return ssoUseCase.create(
      login: Email(email),
      password: Password(
        password,
        minLength: 12,
      ),
      name: StringFilled(name),
      surname: StringFilled(surname),
      language: NotNull(LanguageTypeSSO.spanish),
      companyIdInSSO: IntGreaterZero((state as UserRegisterStateForm)
          .municipalityState
          .selected
          .companyIdSSO),
      applicationId: IntGreaterZero(user.app.id),
      roleId: IntGreaterZero(SSORole.citizen.id),
    );
  }

  void onChangedMunicipality(MunicipalityApp selected) {
    final e = state as UserRegisterStateForm;
    emit(
      UserRegisterStateForm(e.municipalityState.copyWith(selected: selected)),
    );
  }
}
