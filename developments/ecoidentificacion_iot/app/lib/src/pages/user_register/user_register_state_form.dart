// ignore_for_file: public_member_api_docs, sort_constructors_first
import '../../_imports.dart';

class UserRegisterStateForm extends UserRegisterStateBase {
  final MunicipalityAppDropdownState municipalityState;

  UserRegisterStateForm(this.municipalityState);

  @override
  List<Object?> get props => [
        ...municipalityState.municipalities.map2((e) => e.id),
        municipalityState.selected.id,
      ];
}

class UserRegisterStateFormScreen extends StatefulWidget {
  final UserRegisterStateForm state;
  final FieldController name;
  final FieldController surname;
  final FieldController email;
  final FieldController repeatEmail;
  final FieldController password;
  final FieldController repeatPassword;

  const UserRegisterStateFormScreen({
    required this.state,
    required this.name,
    required this.surname,
    required this.email,
    required this.repeatEmail,
    required this.password,
    required this.repeatPassword,
  });

  @override
  _UserRegisterStateFormScreenState createState() =>
      _UserRegisterStateFormScreenState();
}

class _UserRegisterStateFormScreenState
    extends State<UserRegisterStateFormScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<UserRegisterCubit>();
    return ScaffoldCustom(
        isCenter: false,
        isScrollable: false,
        isAppBarForceToShowed: true,
        padding: EdgeInsets.zero,
        backgroundColor: themeColors.greyBackground,
        appBar: CustomAppBar(tt(TTShared.datosDeInicioDeSession)),
        body: ListViewAnimated(
          children: [
            NameFieldV2(
              widget.name,
              additionalValidators: [
                ValidatorStringHasNotNumber(),
              ],
            ),
            LastNameFieldV2(
              widget.surname,
              additionalValidators: [
                ValidatorStringHasNotNumber(),
              ],
            ),
            EmailFieldV2(widget.email),
            EmailRepeatFieldV2(
              fieldController: widget.repeatEmail,
              linkedFieldController: widget.email,
            ),
            CalloutContainer.info(
              title: TTSSO.laContraseniaDebeTenerAlMenos.tt,
              subtitle: tt(
                TTSSO.descripcionRequisitosContraseniaMinMax,
                ["12", "25"],
              ),
            ),
            SeparatorFiori(),
            PasswordFieldV2(
              widget.password,
              secondaryRepeatPasswodController: widget.repeatPassword,
              minLength: 12,
              maxLength: 15,
              additionalValidators: [
                ValidatorResult(
                  (value) => Password(
                    value,
                    minLength: 8,
                    maxLength: 25,
                  ).toResult(),
                ),
              ],
            ),
            PasswordRepeatFieldV2(
              fieldController: widget.repeatPassword,
              linkedFieldController: widget.password,
            ),
            SeparatorFiori(),
            MunicipalityAppDropdown(
              state: widget.state.municipalityState,
              onChanged: cubit.onChangedMunicipality,
            ),
            SeparatorFiori(),
            SmallText(
              tt(TTShared.todosLosCamposSonObligatorios),
              textAlign: TextAlign.center,
              color: themeColors.grey8,
            ),
            SeparatorFiori(),
            GenericButton(
              color: themeColors.secondaryGreen,
              title: tt(TTShared.finalizar),
              onPressed: () async {
                if (!widget.name.isValid) {
                  widget.name.focus();
                  return;
                }
                if (!widget.surname.isValid) {
                  widget.surname.focus();
                  return;
                }

                if (!widget.email.isValid) {
                  widget.email.focus();
                  return;
                }
                if (!widget.repeatEmail.isValid) {
                  widget.repeatEmail.focus();
                  return;
                }
                if (!widget.password.isValid) {
                  widget.password.focus();
                  return;
                }
                if (!widget.repeatPassword.isValid) {
                  widget.repeatPassword.focus();
                  return;
                }
                bool isCompleted = false;
                await DialogService()
                    .showProgressDialog((dialog, context) async {
                  final r = await cubit.onPressedConfirm(
                    email: widget.email.text,
                    repeatEmail: widget.repeatEmail.text,
                    password: widget.password.text,
                    repeatPassword: widget.repeatPassword.text,
                    name: widget.name.text,
                    surname: widget.surname.text,
                  );

                  if (r.isError) {
                    dialog.error(r.error!);
                    return;
                  }

                  dialog
                      .valid(TTShared.registradoEmailDeVerificacionEnviado.tt);
                  isCompleted = true;
                });

                if (isCompleted) Navigator.of(context).pop();
              },
            ),
          ],
        ));
  }
}
