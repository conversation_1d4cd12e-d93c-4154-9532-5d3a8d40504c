import '../../_imports.dart';

class ContainerListPage extends StatefulWidget {
  final ContainerListCubit cubit;

  ContainerListPage(this.cubit);

  @override
  State<ContainerListPage> createState() => _ContainerListState();
}

class _ContainerListState extends State<ContainerListPage> {
  List<StreamSubscription> subscriptions = [];

  final _delayed =
      DelayedRenewableOperationV2(delay: Duration(milliseconds: 300));

  @override
  void initState() {
    _init();
    super.initState();
  }

  void _onChangedList() => widget.cubit.onChangedList();

  Future<void> _init() async {
    subscriptions.add(
      elementManager.onChange.listen((s) {
        _delayed.execute(_onChangedList);
      }),
    );

    subscriptions.add(
      elementManager.onStarted.listen((s) {
        _delayed.execute(_onChangedList);
      }),
    );

    subscriptions.add(
      citizenUseCase.onChange.listen((s) {
        _delayed.execute(_onChangedList);
      }),
    );

    subscriptions.add(
      lockManager.onChange.listen((s) {
        _delayed.execute(_onChangedList);
      }),
    );

    subscriptions.add(
      lockManager.onStarted.listen((s) {
        _delayed.execute(_onChangedList);
      }),
    );

    await elementManager.start();
  }

  @override
  void dispose() {
    subscriptions.forEach((s) => s.cancel());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      elementManager.start();
    });

    return BlocProvider<ContainerListCubit>(
      create: (context) => widget.cubit,
      child: BlocBuilder<ContainerListCubit, ContainerListState>(
        builder: (context, state) {
          return ScaffoldCustom(
            isScrollable: false,
            padding: EdgeInsets.zero,
            appBar: CustomAppBar(
                TTShared.misContenedores.tt + " (${state.elements.length})"),
            endDrawer: const HomeDrawer(page: CurrentPage.containers),
            body: Column(
              children: [
                _testInfo(),
                Expanded(
                  child: state.elements.isEmpty
                      ? Center(
                          child: BodyText(
                            TTShared.noSeHanEncontradoResultados.tt,
                          ),
                        )
                      : ListViewAnimated(
                          shrinkWrap: true,
                          children: state.elements.map2(
                            (e) => ContainerListItem(
                              e,
                              () {
                                NavigatorService().push(ContainerPage(
                                  ContainerCubit(
                                    e,
                                    widget.cubit.onPressedCenterInMap,
                                  ),
                                ));
                              },
                              () {
                                widget.cubit.onPressedCenterInMap(e);
                              },
                            ),
                          ),
                        ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _testInfo() {
    if (!isDebugMode()) return SizedBox.shrink();

    return TestInfo(
      "Lista de contenedores que el usuario puede abrir con sus tarjetas",
      getContent: () => StreamBuilderMultiple([
        elementManager.onChange,
        elementManager.onStarted,
        lockManager.onChange,
        lockManager.onStarted,
        citizenUseCase.onChange,
      ], (_) {
        final locks = lockManager.models;
        final locksCitizen = locks.getByCardAllowed(global.citizen!.cardIds);

        final elementsCitizen = locksCitizen.getElements(elementManager.models);

        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MarkdownWidget("""
En esta pantalla se muestran los contenedores del ciudadano. 

Los contenedores que se consideras del ciudadano, son los que tienen una cerradura. Y la cerradura incluye en sus rangos de valores (***tarjetaDesde*** y ***tarjetaHasta***) el valor del campo idTarjeta (de la tarjeta).

El ciudadano ***${global.citizen?.id} (${global.citizen?.email})*** actualmente tiene las tarjetas:
          """),
            SeparatorFiori(),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: JsonTableWidget(
                global.citizen!.toDto(user.companyId!).tarjetas?.map2(
                          (e) => e.toJson().only(
                            [
                              "id",
                              "empresa",
                              "idTarjeta",
                              "idUsuario",
                              "nsMovisat",
                            ],
                          ),
                        ) ??
                    [],
              ),
            ),
            SeparatorFiori(),
            MarkdownWidget("""
Las cerraduras que coinciden con las tarjetas actuales del ciudadano son:
"""),
            SeparatorFiori(),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: JsonTableWidget(
                locksCitizen.map2(
                  (m) => m.toDto().toJson().collapseNested().only(
                    [
                      "id",
                      "empresa",
                      "idElemento",
                      "nsMovisat",
                      "nsFabricante",
                      "imei",
                      "tarjetaDesde",
                      "tarjetaHasta",
                    ],
                  ),
                ),
              ),
            ),
            SeparatorFiori(),
            MarkdownWidget("""
Los contenedores del ciudadano, serían los que su ***id*** coincida con ***idElement*** de cerradura:

Actualmente estos son los contenedores que puede abrir.
"""),
            SeparatorFiori(),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: JsonTableWidget(
                elementsCitizen.map2(
                  (m) => m.toDto().toMap().only(
                    [
                      "Id",
                      "Nombre",
                      "IdEquipamiento",
                      "Empresa",
                    ],
                  ),
                ),
              ),
            ),
            SeparatorFiori(),
            SeparatorFiori.half(),
            ClickableText(
              "Ver base de datos completa...",
              () => global.openDatabaseViewer(context),
            ),
            SeparatorFiori(),
          ],
        );
      }),
    );
  }
}
