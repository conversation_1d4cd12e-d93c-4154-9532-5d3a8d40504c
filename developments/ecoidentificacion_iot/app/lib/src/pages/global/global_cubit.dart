import 'package:ecoidentificacion_iot_app/ecoidentificacion_iot_app.dart';
import 'package:preference_repository_hive/preference_repository_hive.dart';

abstract class GlobalStateBase extends Equatable {}

class GlobalCubit extends BlocBase<GlobalStateBase> {
  TicketModel? ticket;
  CitizenModel? citizen;
  bool _isCompletedTutorial = false;

  late final PipelineController pipelineController;
  late final translationProvider = TranslationProvider();
  late final onFirstSyncFinish = StreamCustomEmpty();

  late final languages = LanguageType.values;

  GlobalCubit._internal() : super(GlobalStateLoading("BIENVENIDO"));

  void onStartError(String message) => emit(GlobalStateError(message));

  void goToLogin() {
    emit(GlobalStateLogin());
  }

  Future<void> onLogout() async {
    try {
      await injector.get<TicketUseCase>().deleteFromCache();

      // Cuando hace logout, borramos la base de datos de background.
      await backgroundDatabaseClear();
    } catch (e) {
      Log().error("LOGOUT: ${e.toString()}");
    } finally {
      user.logout();
      await UserStateService().notify(user, (error) => Log().error(error));
      token.restart();
      await preferences.autologinUserEmail.set("");
      await preferences.autologinUserPassword.set("");
      await preferences.autologinSSOCompanyId.set(-1);
      await preferences.autologinSSORoleId.set(-1);
      await preferences.hasCheckGeographicFrame.set(false);
      ticket = null;
      citizen = null;
    }

    emit(GlobalStateLogin());
  }

  // Singleton factory.
  static GlobalCubit? _cubit;
  factory GlobalCubit() => _cubit ??= GlobalCubit._internal();

  Future<Either<UserBase, String>> getLoggedUser() async {
    if (!(ticket?.hasAccess() ?? false))
      return Either.right(TTShared.noTienesAccesoAEstaAplicacion.tt);

    final role = SSORole.fromId(ticket?.role?.id ?? -1);
    final id = ticket!.user!.id;

    user.login(
      email: ticket!.user!.email,
      id: id,
      companyId: ticket!.company!.idInNP,
      companyIdSSO: ticket!.company!.idInSSO,
      role: role,
      isInternal: ticket!.user!.isInternal,
      name: ticket!.user!.name,
    );

    Log().info("Usuario logueado: $user");

    /// Después del login, el token se debe restablecer para envíe al servidor su información.
    token.restart();

    await checkCitizen();

    if (citizen == null) {
      final r = await _createCitizen();
      if (r.isError) return Either.right(r.error);
      await checkCitizen();
    }

    if (citizen == null) {
      user.logout();
      token.restart();
      return Either.right(TTShared.haOcurridoUnError.tt);
    }

    await checkTutorial();

    await preferences.isShowedOnlyMyContainers.start();
    if (isDebugMode()) {
      TestInfo.isShowedStream(await preferences.isShowedDebugInfo.get());
      TestInfo.isShowedStream
          .listen((event) => preferences.isShowedDebugInfo.set(event));
    }

    return Either.left(user);
  }

  Future<void> checkCitizen() async {
    citizen = await citizenUseCase.getAdditionalUserData(user.email);
  }

  Future<void> checkTutorial() async {
    _isCompletedTutorial = await preferences.isCompletedTutorial.get();
  }

  void onCompletedLogin() {
    if (!_isCompletedTutorial) {
      emit(GlobalStateTutorial());
    } else {
      backgroundSetData();
      emit(GlobalStateHome());
    }
  }

  Future<Result<String>> _createCitizen() async {
    final m = CitizenModel(
      guid: Guid().get(),
      name: ticket?.user?.nameShort ?? "",
      surname: ticket?.user?.surname ?? "",
      phone: "",
      cadastralNumber: "",
      address: "",
      municipality: "",
      province: "",
      autonomousCommunity: "",
      town: "",
      number: "",
      nif: "",
      observations: "",
      cards: [],
      cleanPointInfo: null,
      addresses: [],
      postalCode: "",
      validationStatus: CitizenValidationStatus.pending.id,
      email: user.email,
      zoneId: 0,
      id: 0,
      modifyDate: now,
      creationDate: now,
      isRemoved: false,
      isSync: false,
      cardIds: [],
      floor: "",
      door: "",
      district: "",
      neighborhood: '',
    );
    return await citizenUseCase.register(m);
  }

  Future<void> openDatabaseViewer(BuildContext context) async {
    List<PreferenceModel> preferences = [];

    await DialogService().showProgressDialog((_, __) async {
      preferences = await PreferenceDao().getAll();
    });

    await NavigatorService().push(
      JsonTablePage(
        [
          JsonTableData(
            "Ciudadano",
            [
              global.citizen!.toDto(user.companyId!).toJson(),
            ],
          ),
          JsonTableData(
            "Tarjetas",
            global.citizen!
                    .toDto(user.companyId!)
                    .tarjetas
                    ?.map2((e) => e.toJson()) ??
                [],
          ),
          JsonTableData(
            "Cerraduras",
            lockManager.models.map2((e) => e.toDto().toJson()),
          ),
          JsonTableData(
            "Contenedores",
            elementManager.models.map2((e) => e.toDto().toMap()),
          ),
          JsonTableData(
            "Preferences",
            preferences.map2((e) => e.toMap()),
          ),
        ],
      ),
    );
  }
}
