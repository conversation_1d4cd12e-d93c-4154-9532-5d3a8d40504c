import 'package:pipeline_date_formatting/pipeline_date_formatting.dart';
import 'package:pipeline_log/pipeline_log.dart';
import 'package:pipeline_new_version/pipeline_new_version.dart';
import 'package:translation_provider_json/translation_provider_json.dart';

import '../../_imports.dart';

class GlobalPage extends StatefulWidget {
  // This 3 parameters is for plugin device_preview.
  final Locale? locale;
  final bool? useInheritedMediaQuery;
  final TransitionBuilder? builder;

  GlobalPage({
    this.locale,
    this.useInheritedMediaQuery,
    this.builder,
  });

  @override
  State<GlobalPage> createState() => _GlobalPageState();
}

class _GlobalPageState extends State<GlobalPage> {
  LanguageType? _language;

  @override
  void initState() {
    PipelineController(
        steps: [
          () => PipelineLog(),
          () => PipelineStep(
                onExecute: () async {
                  await EcoIdentificacionIoTInjector().init();
                },
                onErrorFunction: () async {},
              ),
          // Inicia la base de datos.
          () => PipelineStep(
                // La base de datos Hive a veces no arranca y tampoco genera error.
                timeout: const Duration(seconds: 20),
                onExecute: () async {
                  Log().debug("INICIANDO BASE DE DATOS");
                  await injector.get<DataServiceBase>().startDatabase(
                        () => DatabaseHiveService()
                            .startDatabase(DatabaseHiveProvider()),
                      );

                  Log().debug("INICIANDO BACKGROUND");
                  await backgroundStart();
                },
                onErrorFunction: () async {},
              ),

          () => PipelineDateFormatting(),

          // // Iniciamos Firebase
          // () => PipelineStep(
          //       onExecute: () async {
          //         debugPrint("INICIANDO FIREBASE CORE");
          //         await FirebaseCoreServiceGlobal().start(null);
          //       },
          //       onErrorFunction: () async {},
          //     ),

          // Iniciamos servicio de traducción.
          () => PipelineStep(
                onExecute: () async {
                  Log().debug("ESTABLECIENDO TRADUCCIONES");
                  await TranslationServiceGlobal().start(
                    defaultLanguageType: LanguageType.spanish,
                    languagePref: preferences.language,
                    translationProvider: TranslationProviderJson(),
                  );
                },
                onErrorFunction: () async {},
              ),
          () => PipelineStep(
                onExecute: () async {
                  Log().debug("INICIANDO CONNECTION SERVICE");
                  await injector.get<ConnectionServiceBase>().start();
                },
                onErrorFunction: () async {},
              ),
          () => PipelineNewVersion(
                packageInfoService: injector.get<PackageInfoServiceBase>(),
                onNewVersion: (before, current) async {
                  if (before.isEmpty) return;
                  // Eliminamos todo en las versiones de desarrollo para solucionar los problemas en web de que no arraque.
                  await DatabaseHiveService().repair(
                    isRemovedAll: current.startsWith("0"),
                  );
                },
                onPostNewVersion: (before, current) async {
                  if (before.isEmpty) return;
                  // Siempre que haya una nueva actualización, reiniciamos.
                  // Principalmente se hace para web, para que cargue los ficheros de la nueva versión.
                  // También se hace por si se ha reparado la base de datos, para no olvidarse de reiniciar.
                  await injector.get<RestartServiceBase>().restart();
                },
              ),
        ],
        onCompleted: () async {
          await LoginUseCase(
            isValidTicket: () => GlobalCubit().ticket?.user?.id != null,
            pushLoginPage: () => GlobalCubit().goToLogin(),
            autologinUserEmail: preferences.autologinUserEmail,
            autologinUserPassword: preferences.autologinUserPassword,
            autologinSSOCompanyId: preferences.autologinSSOCompanyId,
            autologinSSORoleId: preferences.autologinSSORoleId,
            ticketUseCase: injector.get<TicketUseCase>(),
            connection: connection,
            applicationId: user.app.id,
            setTicket: (t) => GlobalCubit().ticket = t,
            onCompletedLogin: () => GlobalCubit().onCompletedLogin(),
            getLoggedUser: GlobalCubit().getLoggedUser,
            app: user.app,
            ssoUseCase: ssoUseCase,
          ).tryAutologin("");
        },
        onError: (e, i) async {
          final message = "Error en el pipeline de inicio ($i): $e";
          // Lo imprimimos para que se pueda ver en la consola de la web.
          print(message);
          Log().catastrophe(message);
          Log().info("Borrando la base de datos y reiniciando la aplicación");
          try {
            await DatabaseHiveService().repair(isRemovedAll: true);
            await DatabaseHiveService().close();
          } catch (e) {}
          await injector.get<RestartServiceBase>().restart();
        }).execute();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<GlobalCubit>(
      create: (context) => GlobalCubit(),
      child: GetMaterialApp(
        // This 3 parameters is for plugin device_preview.
        useInheritedMediaQuery: widget.useInheritedMediaQuery ?? false,
        locale: widget.locale,
        builder: widget.builder,
        scrollBehavior: ScrollBehaviourMultiplatform(),
        localizationsDelegates: [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: [
          const Locale('es', ''),
        ],
        theme: ThemeProvider().theme.themeData,
        home: TranslationPage(
          provider: GlobalCubit().translationProvider,
          additionalRebuild: global.onFirstSyncFinish.stream,
          builder: (_) {
            _language = TranslationServiceGlobal().language;

            if (_language != null &&
                TranslationServiceGlobal().language != null &&
                _language != TranslationServiceGlobal().language)
              // When the user changes the languages, we reset the background data.
              backgroundSetData();

            return BlocBuilder<GlobalCubit, GlobalStateBase>(
              builder: (context, state) {
                return ResponsiveGlobalBuilder(
                  child: Builder(
                    builder: (_) {
                      if (state is GlobalStateLoading)
                        return GlobalStateLoadingScreen(state);
                      else if (state is GlobalStateError)
                        return GlobalStateErrorScreen(state);
                      else if (state is GlobalStateLogin)
                        return GlobalStateLoginScreen();
                      else if (state is GlobalStateHome)
                        return GlobalStateHomeScreen();
                      else if (state is GlobalStateTutorial)
                        return GlobalStateTutorialScreen();
                      return const SizedBox();
                    },
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
