import '../../_imports.dart';

class MessageListPage extends StatefulWidget {
  final MessageListCubit cubit;

  MessageListPage(this.cubit);

  @override
  State<MessageListPage> createState() => _MessageListState();
}

class _MessageListState extends State<MessageListPage> {
  List<StreamSubscription> subscriptions = [];

  @override
  void initState() {
    subscriptions.add(
      messageUserManager.onChange.listen((s) {
        widget.cubit.onChangedList(messageUserManager.models);
      }),
    );

    subscriptions.add(
      messageUserManager.onStarted.listen((s) {
        widget.cubit.onChangedList(messageUserManager.models);
      }),
    );

    messageUserManager.start();

    super.initState();
  }

  @override
  void dispose() {
    subscriptions.forEach((s) => s.cancel());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      messageUserManager.start();
    });

    return BlocProvider<MessageListCubit>(
      create: (context) => widget.cubit,
      child: BlocBuilder<MessageListCubit, MessageListState>(
        builder: (context, state) {
          Widget readAllButton = SizedBox();
          if (state.messages.any((m) => m.isValidForNotify(user.id!, user.app)))
            readAllButton = Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: themeContainers.scaffoldPadding.get().get(),
                child: GenericButton(
                    title: TTShared.marcarTodosComoLeidos.tt,
                    color: themeColors.primaryBlueLight,
                    onPressed: () {
                      DialogService().showProgressDialog(
                          (dialog, context) async =>
                              messageUserUseCase.checkAsReaded(state.messages));
                    }),
              ),
            );

          return ScaffoldCustom(
            isScrollable: false,
            padding: EdgeInsets.zero,
            appBar: CustomAppBar(
              TTShared.notificaciones.tt + " (${state.messages.length})",
            ),
            endDrawer: const HomeDrawer(page: CurrentPage.notifications),
            body: state.messages.isEmpty
                ? Center(
                    child: BodyText(
                      TTShared.noSeHanEncontradoResultados.tt,
                    ),
                  )
                : Stack(
                    children: [
                      ListViewAnimated(
                        padding: themeContainers.scaffoldPadding
                            .get()
                            .get()
                            .copyWith(bottom: 70),
                        children: state.messages.map2(
                          (m) => MessageListItem(
                            m,
                            () {
                              if (m.isValidForNotify(user.id!, user.app))
                                messageUserUseCase.checkAsReaded([m]);
                            },
                          ),
                        ),
                      ),
                      readAllButton,
                    ],
                  ),
          );
        },
      ),
    );
  }
}
