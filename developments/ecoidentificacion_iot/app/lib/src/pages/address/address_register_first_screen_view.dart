import '../../_imports.dart';

class AddressRegisterFirstScreenState extends AddressRegisterStateBase {
  // final AddressFilterType filterType;

  AddressRegisterFirstScreenState(
      // this.filterType
      );

  @override
  List<Object> get props => [
        //       filterType,
      ];
}

class AddressRegisterFirstScreenBody extends StatelessWidget {
  final AddressRegisterFirstScreenState state;

  AddressRegisterFirstScreenBody(this.state);

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AddressRegisterCubit>();

    return ListViewAnimated(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
      paddingChild: const EdgeInsets.only(bottom: 20),
      children: [
        Container(
          width: double.maxFinite,
          height: 170,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: themeColors.primaryBlue,
                width: 1,
              )),
          child: FutureBuilder<String>(
            future: ssoUseCase.getImageByMd5Company(
              global.ticket?.company?.imageMd5,
            ),
            builder: (_, snapshot) {
              if (isStringNullOrEmpty(snapshot.data)) return const SizedBox();
              return SizedBox(
                height: globalAppBarHeight,
                child: ImageCacheService().getFromStringBase64(snapshot.data!),
              );
            },
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
          width: double.maxFinite,
          child:  Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                TTEI.darDeAltaDomicilio.tt,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                  height: 1.2,
                  letterSpacing: 0,
                  fontFamily: 'Roboto',
                ),
              ),
             const SizedBox(height: 16),
              Text(
                tt(TTEI.introduceTuXParaDarDeAltaTuDomicilio.tt, [cubit.validationFieldType]),
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 24,
                  fontStyle: FontStyle.italic,
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                  height: 1.2,
                  letterSpacing: 0,
                  fontFamily: 'Roboto',
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class AddressRegisterFirstScreenFooter extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AddressRegisterCubit>();

    return Container(
      width: double.maxFinite,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      child: Column(
        children: [
          AddressRegisterFooterItem(
            title: TTShared.cancelar.tt,
            onPressed: () => NavigatorService().popToMainPage(),
            isSecondary: false,
          ),
          const SizedBox(height: 10),
          AddressRegisterFooterItem(
            title: TTEI.darDeAltaDomicilio.tt,
            onPressed: () => cubit.onChangedPages(AddressRegisterNumberPage.second),
            isSecondary: true,
          ),
        ],
      ),
    );
  }
}
