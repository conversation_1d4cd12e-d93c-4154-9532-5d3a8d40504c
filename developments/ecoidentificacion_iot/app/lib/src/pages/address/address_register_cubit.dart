import '../../_imports.dart';

enum AddressRegisterNumberPage {
  first,
  second,
  third,
}

class AddressRegisterCubit extends BlocBase<AddressRegisterStateBase> {
  List<LockIdentificationModel> _models = [];
  String validationFieldType = TTEI.referenciaCatastral.tt;

  AddressRegisterCubit() : super(AddressRegisterStateLoading());

  Future<void> init() async {
    _models = await lockIdentificationUseCase.getAll();
    final Either<ValidationFieldModel, Failure2<SendErrorType>>
        validationField = await validationFieldUseCase.getByCompany();
    if (validationField.isLeft)
      validationFieldType = validationField.left != null
          ? tt(validationField.left!.label)
          : TTEI.referenciaCatastral.tt;

    onChangedPages(AddressRegisterNumberPage.first);
  }

  void onChangedPages(
    AddressRegisterNumberPage pageNumber,
  ) {
    switch (pageNumber) {
      case AddressRegisterNumberPage.first:
        emit(AddressRegisterFirstScreenState());
        break;

      case AddressRegisterNumberPage.second:
        emit(AddressRegisterSecondScreenState());
        break;

      case AddressRegisterNumberPage.third:
        emit(AddressRegisterThirdScreenState(cadastres: []));
        break;
    }
  }

  void emitSecondScreen(bool? isError) {
    emit(AddressRegisterSecondScreenState(isError: isError));
  }

  void onSelectCadastre(CadastreModel cadastre) {
    final currentState = state;
    if (currentState is AddressRegisterThirdScreenState) {
      if (cadastre.id == currentState.selectedCadaste?.id) {
        emit(currentState.copyWith(selectedCadaste: null));
      } else {
        emit(currentState.copyWith(selectedCadaste: cadastre));
      }
    }
  }

  TextEditingController validationDataController = TextEditingController();

  // bool validationDataIsValid(dynamic data) {
  //   // if (data == null) return null;
  //   // if ((data is String) && data.isNullOrEmpty) return null;
  //   if (validationDataController.text.length != 20) return false;
  //   return true;
  // }

  Future<void> onPressedSearchAddress() async {
    final currentState = state;
    if (currentState is AddressRegisterSecondScreenState) {
      final cadastres = await cadastreUseCase
          .getByCatastralReference(validationDataController.text);

      if (cadastres != null && cadastres.isNotEmpty) {
        emit(AddressRegisterThirdScreenState(
          cadastres: cadastres,
          selectedCadaste: cadastres.length == 1 ? cadastres.first : null,
        ));
      } else
        emit(currentState.copyWith(isError: true));
    }
  }

  Future<void> onEditPopulation(int value) async {
    final currentState = state;
    if (currentState is AddressRegisterThirdScreenState) {
      emit(currentState.copyWith(populationNumber: value));
    }
  }

  Future<void> onPressedAsociteAddress() async {
    final currentState = state;
    if (currentState is AddressRegisterThirdScreenState) {
      if (currentState.selectedCadaste == null) return;

      final Result<String> response = await citizenUseCase.associateAddress(
        cadastralReference: currentState.selectedCadaste!.reference,
        populationNumber: currentState.populationNumber,
        citizenGuid: global.citizen!.guid,
      );

      if (response.isError) {
        DialogService().showDialogEID(
          TTShared.error.tt,
          subtitle: response.error ?? TTShared.haOcurridoUnError.tt,
          icon: Icons.error_outline,
          iconColor: Colors.red,
          primaryButtonText: TTShared.aceptar.tt,
          onPrimaryButtonPressed: () => NavigatorService().pop(),
        );
      } else {
        global.checkCitizen();
        DialogService().showDialogEID(
          '¡Enhorabuena! tu domicilio ha sido dado de alta',
          subtitle:
              'Recibirás un email confirmando que hemos recibido la solicitud',
          icon: Icons.check_circle,
          iconColor: Colors.green,
          primaryButtonText: TTShared.aceptar.tt,
          onPrimaryButtonPressed: () => NavigatorService().popToMainPage(),
        );
      }
    }
  }

  Future<void> onEditValidationData() async {
    final currentState = state;
    if (currentState is AddressRegisterSecondScreenState) {
      if (validationDataController.text.isNotEmpty &&
          !currentState.enabledSearch)
        emit(currentState.copyWith(enabledSearch: true));

      if (validationDataController.text.isEmpty && currentState.enabledSearch)
        emit(currentState.copyWith(enabledSearch: false));
    }
  }
}
