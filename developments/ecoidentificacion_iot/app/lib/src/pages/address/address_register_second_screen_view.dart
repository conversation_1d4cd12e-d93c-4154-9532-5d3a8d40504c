import '../../_imports.dart';

class AddressRegisterSecondScreenState extends AddressRegisterStateBase {
  final bool? isError;
  final bool enabledSearch;

  AddressRegisterSecondScreenState({
    this.isError,
    this.enabledSearch = false,
  });

  AddressRegisterSecondScreenState copyWith({
    bool? isError,
    bool? enabledSearch,
  }) {
    return AddressRegisterSecondScreenState(
      isError: isError ?? this.isError,
      enabledSearch: enabledSearch ?? this.enabledSearch,
    );
  }

  @override
  List<Object?> get props => [
        isError,
        enabledSearch,
      ];

  String errorMessage =
      TTEI.elDatoDeValidacionIntroducidoNoCorrespondeANingunaDireccion.tt;
}

class AddressRegisterSecondScreenBody extends StatelessWidget {
  final AddressRegisterSecondScreenState state;

  AddressRegisterSecondScreenBody(this.state);

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AddressRegisterCubit>();

    return ListViewAnimated(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 30),
      paddingChild: const EdgeInsets.only(bottom: 20),
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
          width: double.maxFinite,
          child:  Column(
            children: [
               Text(
                tt(TTEI.introduceTuX.tt, [cubit.validationFieldType]),
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.w400,
                  color: Color(0xff1E1E1E),
                  height: 1.2,
                  letterSpacing: 0,
                  fontFamily: 'Roboto',
                ),
              ),
            const  SizedBox(height: 16),
              Text(
                tt(TTEI.introduceTuXParaDarDeAltaTuDomicilio.tt, [cubit.validationFieldType]),
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w400,
                  color: Color(0xff1E1E1E),
                  height: 1.2,
                  letterSpacing: 0,
                  fontFamily: 'Roboto',
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 6),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 10),
          width: double.maxFinite,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
               Text(
                TTEI.datoDeValidacion.tt,
                style:const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w400,
                  color: Color(0xff1E1E1E),
                  height: 1.2,
                  letterSpacing: 0,
                  fontFamily: 'Roboto',
                ),
              ),
              const SizedBox(height: 2),
              CardInput(
                border: Border.all(
                  color: const Color(0xff757575),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(8),
                // errorBorder: Border.all(
                //   color: const Color(0xffBC2828),
                //   width: 1,
                // ),
                // validBorder: Border.all(
                //   color: const Color(0xff236616),
                //   width: 1,
                // ),
                controller: cubit.validationDataController,
                fieldType: FieldType.text,
                textFontSize: 20,
                // maxLength: 20,
                // minLength: 3,
                showValidator: false,
                iconRight: null,
                showErrorMessage: false,
                isRequired: true,
                showLabel: false,
                showInitialValidatorIcon: false,
                // isValid: cubit.validationDataIsValid,
                verticalMargin: 0,
                onChange: (p0) => cubit.onEditValidationData(),
              ),
              const SizedBox(height: 26),
              if (state.isError == true)
                AddressRegisterListItem(
                  primaryText: TTShared.error.tt,
                  secondaryText: state.errorMessage,
                  itemState: AddressRegisterListItemState.error,
                  onPressed: () {},
                  onPressConfirm: () {},
                ),
            ],
          ),
        ),
      ],
    );
  }
}

class AddressRegisterSecondScreenFooter extends StatelessWidget {
  final AddressRegisterSecondScreenState state;

  AddressRegisterSecondScreenFooter(this.state);
  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AddressRegisterCubit>();

    return Container(
      width: double.maxFinite,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      child: Column(
        children: [
          AddressRegisterFooterItem(
            title: TTShared.cancelarElProceso.tt,
            onPressed: () => NavigatorService().popToMainPage(),
            isSecondary: false,
          ),
          const SizedBox(height: 10),
          AddressRegisterFooterItem(
            title: TTEI.buscarDomicilio.tt,
            onPressed: () => cubit.onPressedSearchAddress(),
            isSecondary: true,
            enabled: state.enabledSearch,
          ),
        ],
      ),
    );
  }
}
