import '../../_imports.dart';

class UserLoginPage extends StatefulWidget {
  @override
  _UserLoginPageState createState() => _UserLoginPageState();
}

class _UserLoginPageState extends State<UserLoginPage> {
  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      // WHen the login page is loaded, we remove all background data.
      // This is because a error can be produced in the autologin.
      backgroundSetData();
    });

    void onCreateAccount() async {
      await DialogService().showProgressDialog((dialog, context) async {
        final municipalities = await ssoUseCase.getMunicipalitiesApp();

        if (municipalities.isEmpty)
          return dialog.error(TTSSO.noSeHanEncontradoMunicipios.tt);

        dialog.hide();

        // ignore: unawaited_futures
        NavigatorService()
            .push(UserRegisterPage(UserRegisterCubit(municipalities)));
      });
    }

    void onRestorePassword() {
      UserRecoverPasswordBloc(
        connection: injector.get<ConnectionServiceBase>(),
        onClosed: () => NavigatorService().pop(),
      ).openPage();
    }

    // Boton de crear cuenta
    final createAccount = GenericButton(
      title: tt(TTShared.crearCuenta),
      color: themeColors.primaryBlue,
      textColor: themeColors.white,
      onPressed: () => onCreateAccount(),
    );

    // Boton de recuperar contraseña
    final restorePassword = GenericButton(
      title: tt(TTShared.hasOlvidadoTuContrasenia),
      color: themeColors.primaryBlue,
      textColor: themeColors.white,
      onPressed: () => onRestorePassword(),
    );

    return LoginPage(
      LoginProvider(
        languages: global.languages,
        loginUseCase: LoginUseCase(
          isValidTicket: () => global.ticket?.user?.id != null,
          pushLoginPage: () {},
          autologinUserEmail: preferences.autologinUserEmail,
          autologinUserPassword: preferences.autologinUserPassword,
          autologinSSOCompanyId: preferences.autologinSSOCompanyId,
          autologinSSORoleId: preferences.autologinSSORoleId,
          ticketUseCase: injector.get<TicketUseCase>(),
          connection: connection,
          applicationId: user.app.id,
          setTicket: (t) => global.ticket = t,
          onCompletedLogin: () {
            // There is a error witch the progress dialog don't disappear.
            if (NavigatorService().canPop()) NavigatorService().pop();
            global.onCompletedLogin();
          },
          getLoggedUser: global.getLoggedUser,
          app: user.app,
          ssoUseCase: ssoUseCase,
        ),
      ),
      appNameLogo: Column(
        children: [
          AppNameText("Eco", "Identificación IoT"),
          const SizedBox(height: 2),
          Center(
            child: AppVersion(() async {
              final info = await packageInfoService.get();
              return TTShared.version.tt + ' ${info?.version}';
            }),
          ),
        ],
      ),
      footerWidget: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          createAccount,
          SeparatorFiori.half(),
          restorePassword,
          SeparatorFiori.half()
        ],
      ),
      loginWithEmail: true,
    );
  }
}
