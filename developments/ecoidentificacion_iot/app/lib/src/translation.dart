import '_imports.dart';

enum TTEI {
  confirmarDireccion,
  domicilios,
  abreContenedoresConTuApp,
  conLaAplicacionPuedesAbrirTusContenedoresAsignadosYTenerUnHistoricoDeSuUso,
  registraTusDomiciliosEIdentificalosEnElMapaParaPoderVerTusContenedoresAsignados,
  descubreTodasLasVentajasDeLaAPP,
  registraTusDomicilios,
  localizaEnElMapaTusContenedoresMasCercanos,
  encuentraTusContenedoresMasCercanos,
  haHabidoUnErrorAlIntentarAbrirElContenedor,
  aquiPuedesVerTusContenedoresSeleccionaElQueQuieresAbrir,
  seleccionaElContenedorQueQuiereAbrir,
  introduceTuXParaDarDeAltaTuDomicilio,
  referenciaCatastral,
  introduceTuX,
  datoDeValidacion,
  contenedorAbiertoConExito,
  darDeAltaDomicilio,
  noSeHaEncontradoNingunContenedorPermitidoCerca,
  aproximateAUnoDeTusContenedoresParaPoderAbrirlo,
  numeroDeHabitantes,
  buscarDomicilio,
  noSeHanEncontradoCerraduras,
  noTienesTarjetasAsociadas,
  seleccionaElDomicilioQueQuierasDarDeAlta,
  sostenTuTelefonoCercaDelContenedor,
  laDireccionQueSeleccioneSeAsociaraALaTarjetaSiQuieresPedirVariasTarjetasRepiteElProcesoTantasVecesComoSeaNecesario,
  elDatoDeValidacionIntroducidoNoCorrespondeANingunaDireccion,
  porMes,
  porDia,
  porSemana,
  utlimos15dias,
  tuMunicipioEstaVerificandoTusDatos,
  teEnviaremosUnEmailValidadoTusDatos,
  verEnElMapaSoloMisContenedores,
  noSeHaDetectadoQueElDispositivoTengaCompatibilidadConBluetooth,
  noSeHanAceptadoLosPermisosDeBluetooth,
  identificando,
  calendario,
  intensidadDeLaSenial,
  disponibleTodoElDia,
  fueraDeHorario,
  fichaDelContenedor,
  seleccionaUnaUbicacionParaObtenerLaDireccion,
  identificadoCorrectamente,
  seHaAgotadoElTiempoMaximoDeAperturaDeCerradura,
  vuelvaAIdentificarsePorFavor,
  elContenedorYaEstaAbierto,
  noSeHaPodidoConectarConLaCerradura,
  noHayCerradurasDisponibles,
  contacteConSuAyuntamiento,
  usuarioNoPermitido,
  noSePuedeAbrirElContenedor,
  estaFueraDelHorarioEstablecido,
  noSeHanEncontradoLosDatosAsociadosALaCerradura,
  activandoBluetooth,
  identificacion,
  identificaciones,
  cerradura,
  aportacion,
  apertura,
  noSeHanEncontradoIdentificaciones,
  elBluetoothNoEstaActivo,
  noEstasAutorizadoParaAbrirEstaCerradura,
  elIdentificadorNoHaPodidoVerificarse,
  noTienesNingunDomicilioAsignado,
  paraPoderAbrirContenedoresNecesitasUno,
  estasFueraDelMarcoGeograficoDeLaAplicacion;

  String get tt => translate(this);
}
