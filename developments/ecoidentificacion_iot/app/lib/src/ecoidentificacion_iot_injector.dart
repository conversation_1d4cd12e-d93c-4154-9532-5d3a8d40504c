import 'package:connection_provider_gps/connection_provider_gps.dart';
import 'package:connection_provider_internet/connection_provider_internet.dart';
import 'package:connection_provider_location/connection_provider_location.dart';
import 'package:connection_service/connection_service.dart';
import 'package:connection_state_provider_connectivity_plus/connection_state_provider_connectivity_plus.dart';
import 'package:ecoidentificacion_iot_app/ecoidentificacion_iot_app.dart';
import 'package:external_link_service/external_link_service.dart';
import 'package:map_info_service_here/map_info_service_here.dart';
import 'package:package_info_service/package_info_service.dart';
import 'package:preference_repository_hive/preference_repository_hive.dart';
import 'package:restart_service_mobile/restart_service_mobile.dart';
import 'package:server_time_service/server_time_service.dart';
import 'package:sso_local/sso_local.dart';

import '_imports.dart';

class EcoIdentificacionIoTInjector {
  Future<void> init() async {
    //==========================================================================
    // USER
    //==========================================================================
    final user = UserEcoIdentificacionIoT();
    injector.registerSingleton<UserEcoIdentificacionIoT>(user);
    injector.registerSingleton<UserBase>(user);
    injector.registerSingleton<UserNPBase>(user);

    //==========================================================================
    // HELPERS
    //==========================================================================
    injector.registerSingleton<EcoIdentificacionIoTPreferences>(
        EcoIdentificacionIoTPreferences());
    injector.registerSingleton<PositionUtilServiceBase>(PositionUtilService());

    //==========================================================================
    // SERVICES 1
    //==========================================================================
    injector.registerSingleton<ConnectionServiceBase>(
      ConnectionService(
        ConnectionProviderGPS(),
        ConnectionProviderLocation(),
        ConnectionProviderInternet(ConnectionStateProviderConnectivity()),
      ),
    );
    injector.registerSingleton<ExternalLinkServiceBase>(ExternalLinkService());
    injector.registerSingleton<RestartServiceBase>(RestartServiceMobile());
    injector
        .registerSingleton<BluetoothLockServiceBase>(BluetoothLockService());
    injector.registerSingleton<NotificationServiceBase>(
      NotificationService(
        icon: "ic_launcher",
        channelId: "EcoIdentificacionIoT messages",
        channelName: "EcoIdentificacionIoT messages",
        channelDescription: "Messages received by the user",
        onNotificationPressed: addNotificationPeding,
      ),
    );
    //==========================================================================
    // TOKEN
    //==========================================================================

    injector.registerSingleton<TokenBase>(
      Token(
        TokenSourceNP(user),
        preferences.urlServer.get,
        injector.get<ConnectionServiceBase>(),
      ),
    );

    //==========================================================================
    // SERVICES 2
    //==========================================================================
    injector.registerSingleton<PackageInfoServiceBase>(PackageInfoService());
    // injector.registerSingleton<DeviceIdServiceBase>(sp.deviceIdService);
    // injector.registerSingleton<DeviceInfoServiceBase>(sp.deviceInfoService);
    injector.registerSingleton<DataServiceBase>(DataService());
    injector.registerSingleton<SyncServiceBase>(
      SyncService(
        isUserValid: () => user.isLogin() && global.citizen != null,
        // injector.get<UserEcoIdentificacionIoT>().isLogin(),
        connection: injector.get<ConnectionServiceBase>(),
        token: token,
      ),
    );

    // Debido a que en el arranque el servicio de sincronización no está para notificar cambios
    // en los textos que vienen del SSO y reconstruir la UI, creamos un stream intermedio y
    // cuando se instancia el syncService escuchamos su evento y notificamos al nuevo stream.
    syncService.onFirstSyncFinish.listen((event) => global.onFirstSyncFinish());

    injector.registerSingleton<MapInfoServiceBase>(MapInfoServiceHere());
    injector.registerSingleton<ServerTimeServiceBase>(
      ServerTimeService(
        token: token,
        connection: connection,
        diffMicrosecondsPref: preferences.diffMicrosecondsTimeServer,
      ),
    );

    //==========================================================================
    // INJECTORS 1
    //==========================================================================
    await PreferenceInjector(repository: PreferenceRepositoryHive(dataService))
        .init();

    await GeographicFrameInjector(user).init();

    await EquipmentInjector(preferences.lastSyncEquipment).init();

    await SSOInjector(
      user: user,
      connection: injector.get<ConnectionServiceBase>(),
      ticketLocal: TicketLocal(injector.get<DataServiceBase>()),
      ssoLocal: SSOLocal(
        injector.get<DataServiceBase>(),
        injector.get<UserBase>(),
      ),
      userSSOLocal: UserSSOLocal(
        injector.get<DataServiceBase>(),
        injector.get<UserBase>(),
      ),
      textSSOLocal: TextSSOLocal(
        injector.get<DataServiceBase>(),
        injector.get<UserNPBase>(),
      ),
      token: Token(
        TokenSourceSSO(),
        () async => getUrlServerSSO(),
        injector.get<ConnectionServiceBase>(),
      ),
      getTicketCached: () => GlobalCubit().ticket,
      isRemovableRoleId: (_) => false,
      lastSyncUserSSO: preferences.lastSyncUserSSO,
      lastSyncMenuSSO: preferences.lastSyncMenuSSO,
      menuSSOLocal: MenuSSOLocal(dataService, user),
      syncService: syncService,
      companyApp: preferences.companyApp,
    ).init();

    await ResidueInjector(
      user: user,
      connection: connection,
      lastSyncResidue: preferences.lastSyncResidue,
      token: token,
      syncService: syncService,
    ).init();

    await CadastreInjector(
      user: user,
      connection: connection,
      lastSyncCadastre: preferences.lastSyncCadastre,
      token: token,
      syncService: syncService,
    ).init();

    await ElementInjector(
      lastSyncElement: preferences.lastSyncElement,
      paginationElementGuid: preferences.paginationElementGuid,
      paginationElementIndex: preferences.paginationElementIndex,
      paginationElementMaxCount: preferences.paginationElementMaxCount,
      serverTimeService: injector.get<ServerTimeServiceBase>(),
    ).init();

    await LockInjector(
      lastSyncLock: preferences.lastSyncLock,
      lastSyncLockCalendar: preferences.lastSyncLockCalendar,
    ).init();

    await MessageUserInjector(preferences.lastSyncMessageUser).init();

    await CitizenInjector(preferences.lastSyncCitizen).init();

    //==========================================================================
    // INJECTORS 2
    //==========================================================================
    await LockIdentificationInjector(
      lastSyncIdentificacion: preferences.lastSyncHistory,
      getCitizenCardIds: () =>
          global.citizen?.cards.map2((e) => e.idToCrossWithIdentification) ??
          [],
    ).init();

    final validationFieldLocal = ValidationFieldLocal(dataService, user);

    final validationFieldRemote = ValidationFieldRemote(
      token: token,
      user: user,
      local: validationFieldLocal,
    );

    final validationFieldSychronizable =
        Synchronizer<ValidationFieldModel, int>(
      syncService: syncService,
      syncLoopBase: SyncLoopTimerAndCondition(
        const Duration(minutes: 3),
        () => user.isAdmin,
      ),
      priority: -10,
      syncReceive: SyncReceive(
        local: validationFieldLocal,
        lastSyncPref: preferences.lastSyncValidationField,
        remote: validationFieldRemote,
        syncService: syncService,
      ),
    );

    final validationFieldRepository = ValidationFieldRepository(
      connection: connection,
      local: validationFieldLocal,
      remote: validationFieldRemote,
      synchronizable: validationFieldSychronizable,
    );

    //==========================================================================
    // USE CASE
    //==========================================================================
    injector.registerSingleton<ValidationFieldUseCase>(
      ValidationFieldUseCase(
        validationFieldRepository,
        user,
      ),
    );

    //==========================================================================
    // MANAGERS
    //==========================================================================
    injector.registerSingleton<ManagerCache<ValidationFieldModel, int>>(
      ManagerCache<ValidationFieldModel, int>(
        validationFieldUseCase,
      ),
    );

    //==========================================================================
    // DEPENDENCY
    //==========================================================================

    //==========================================================================
    // OTROS
    //==========================================================================
    NotificationMessageUser.start();
  }
}
