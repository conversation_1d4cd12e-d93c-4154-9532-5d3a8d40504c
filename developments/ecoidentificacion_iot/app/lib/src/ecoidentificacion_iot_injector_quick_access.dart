import '_imports.dart';

UserEcoIdentificacionIoT get user => injector.get<UserEcoIdentificacionIoT>();
// EcoIdentificacionIoTEnvironment get environment =>
//     injector.get<EcoIdentificacionIoTEnvironment>();
GlobalCubit get global => GlobalCubit();
DataServiceBase get dataService => injector.get<DataServiceBase>();
BluetoothLockServiceBase get bluetoothLockService =>
    injector.get<BluetoothLockServiceBase>();
TokenBase get token => injector.get<TokenBase>();
EcoIdentificacionIoTPreferences get preferences =>
    injector.get<EcoIdentificacionIoTPreferences>();
SyncServiceBase get syncService => injector.get<SyncServiceBase>();
ServerTimeServiceBase get serverTimeService =>
    injector.get<ServerTimeServiceBase>();
ValidationFieldUseCase get validationFieldUseCase =>
    injector.get<ValidationFieldUseCase>();