import '../ecoidentificacion_iot_app.dart';

enum EINotificationType {
  messageUser,
}

class EcoIdentificacionIoTPreferences {
  final blueLight = Color(0xffE7E9F6);

  final language = LanguagePref();
  final companyIdInSSO = CompanyIdPref();
  final userEmail = LoginPref();
  final userPassword = PasswordPref();

  final diffMicrosecondsTimeServer = PreferenceUser<int>(
    "diffMicrosecondsTimeServer",
    -1,
  );

  late final isShowedOnlyMyContainers = PreferenceNotifierCache<bool>(
    PreferenceUser<bool>(
      "isShowedOnlyMyContainers",
      true,
    ),
  );

  late final isShowedDebugInfo = PreferenceGlobal<bool>(
    "isShowedDebugInfo",
    false,
  );

  final companyApp = PreferenceGlobal<String>(
    "companyApp",
    PreferenceBase.dateTime,
  );

  final lastSyncCitizen = PreferenceUser<String>(
    "lastSyncCitizen",
    PreferenceBase.dateTime,
  );

  final lastSyncMessageUser = _LastSyncMessageUser();

  final lastSyncLock = PreferenceUser<String>(
    "lastSyncLock",
    PreferenceBase.dateTime,
  );
  final lastSyncLockCalendar = PreferenceUser<String>(
    "lastSyncLockCalendar",
    PreferenceBase.dateTime,
  );
  final lastSyncValidationField = PreferenceUser<String>(
    "lastSyncValidationField",
    PreferenceBase.dateTime,
  );

  final lastSyncElement = PreferenceUser<String>(
    "lastSyncElement",
    PreferenceBase.dateTime,
  );
  final paginationElementGuid =
      PreferenceUser<String>("paginationElementGuid", "");
  final paginationElementIndex =
      PreferenceUser<int>("paginationElementIndex", -1);
  final paginationElementMaxCount =
      PreferenceUser<int>("paginationElementMaxCount", -1);
  final isCompletedTutorial =
      PreferenceGlobal<bool>("isCompletedTutorial", false);

  final lastSyncUserSSO = PreferenceUser(
    "lastSyncUserSSO",
    PreferenceBase.dateTime,
  );

  final lastSyncMenuSSO = PreferenceUser(
    "lastSyncMenuSSO",
    PreferenceBase.dateTime,
  );

  final lastSyncResidue = PreferenceUser(
    "lastSyncResidue",
    PreferenceBase.dateTime,
  );

    final lastSyncCadastre = PreferenceUser(
    "lastSyncCadastre",
    PreferenceBase.dateTime,
  );

  final lastSyncHistory = PreferenceUser<String>(
    "lastSyncHistory",
    PreferenceBase.dateTime,
  );
  final lastSyncEquipment = PreferenceUser<String>(
    "lastSyncEquipment",
    PreferenceBase.dateTime,
  );

  final urlServer = PreferenceFake(
    () async => !isStringNullOrEmpty(EnvironmentDebug().urlServer)
        ? EnvironmentDebug().urlServer
        : global.ticket!.urlApi,
    (_) async => null,
  );

  final autologinUserEmail = PreferenceGlobal<String>(
    "autologinUserEmail",
    "",
  );
  final autologinUserPassword = PreferenceGlobal<String>(
    "autologinUserPassword",
    "",
  );

  final autologinSSOCompanyId = PreferenceGlobal<int>(
    "autologinSSOCompanyId",
    -1,
  );

  final autologinSSORoleId = PreferenceGlobal<int>(
    "autologinSSORoleId",
    -1,
  );

  final hasCheckGeographicFrame = PreferenceGlobal<bool>(
    "hasCheckGeographicFrame",
    false,
  );
}

class _LastSyncMessageUser implements PreferenceUser<String> {
  final _pref = PreferenceUser(
    "lastSyncMessageUser",
    PreferenceBase.dateTime,
  );

  @override
  String get name => _pref.name;

  @override
  Future<String> get() => _pref.get();

  @override
  Future<void> set(String value) async {
    await _pref.set(value);
    await lastSyncIsFromBackground.set(false);
    await lastSyncMessageUserBackground.set(value);
  }
}
