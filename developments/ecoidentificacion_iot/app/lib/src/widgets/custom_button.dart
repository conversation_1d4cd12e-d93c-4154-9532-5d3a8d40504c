import '../_imports.dart';
import 'dart:ui';

enum IconPosition { left, right }

class CustomBottom extends StatelessWidget {
  final String label;
  final IconData? icon;
  final double iconSize;
  final FontWeight fontWeight;
  final double fontSize;
  final bool secondary;
  final String fontFamily;
  final double fontHeight;
  final Color foregroundColor;
  final Color backgroundColor;
  final Color disabledForegroundColor;
  final Color disabledBackgroundColor;
  final VoidCallback onTap;
  final bool enabled;
  final double height;
  final double horizontalPadding;
  final double verticalPadding;
  final IconPosition iconPosition;
  final BorderRadius borderRadius;

  const CustomBottom({
    Key? key,
    required this.label,
    required this.onTap,
    this.icon,
    this.iconSize = 20,
    this.fontSize = 20,
    this.fontFamily = 'Roboto',
    this.fontHeight = 1.2,
    this.fontWeight = FontWeight.w400,
    this.secondary = false,
    this.foregroundColor = const Color(0xffE7EDFF),
    this.backgroundColor = const Color(0xFF3358C7),
    this.disabledForegroundColor = const Color(0xFF757575),
    this.disabledBackgroundColor = const Color(0xffB3B3B3),
    this.enabled = true,
    this.height = 53,
    this.verticalPadding = 12,
    this.horizontalPadding = 24,
    this.iconPosition = IconPosition.left,
    this.borderRadius = const BorderRadius.all(Radius.circular(100)),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Color currentForegroundColor = !enabled
        ? disabledForegroundColor
        : secondary
            ? const Color(0xFF1E1E1E)
            : foregroundColor;

    final Color currentBackgroundColor = !enabled
        ? disabledBackgroundColor
        : secondary
            ? const Color(0xffffffFF)
            : backgroundColor;

    return GestureDetector(
      onTap: enabled ? onTap : null,
      child: Container(
        height: height,
        padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: verticalPadding,
        ),
        decoration: BoxDecoration(
          color: currentBackgroundColor,
          borderRadius: borderRadius,
          border: Border.all(
            color: currentForegroundColor,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null && iconPosition == IconPosition.left) ...[
              Icon(icon, size: iconSize, color: currentForegroundColor),
              const SizedBox(width: 8),
            ],
            Text(
              label,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: fontSize,
                color: currentForegroundColor,
                fontFamily: fontFamily,
                height: fontHeight,
                fontWeight: fontWeight,
              ),
            ),
            if (icon != null && iconPosition == IconPosition.right) ...[
              const SizedBox(width: 8),
              Icon(icon, size: iconSize, color: currentForegroundColor),
            ],
          ],
        ),
      ),
    );
  }
}
