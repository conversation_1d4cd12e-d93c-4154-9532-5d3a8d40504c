import '../_imports.dart';

// Sistema de archivos generales para el control de permisos en la aplicación
class Permission implements AppMenuItemPermission {
  @override
  final String title;
  @override
  final IconData icon;
  @override
  final String? description;
  @override
  final Stream<bool>? hasNotifications;
  final MenuAction action;

  Permission({
    required this.title,
    required this.icon,
    this.description,
    this.hasNotifications,
    required this.action,
  });

  @override
  bool operator ==(other) => other == action;

  // Ejecutar la accion de este permiso
  void execute() {
    onPressedMenuAction(action);
  }

  static Permission? getByAction(MenuAction a) {
    return list.firstWhereOrNull((element) => element.action == a);
  }

  bool validate() {
    return Permission.list.contains(this);
  }

  // Lista de permisos propios de la aplicación
  static List<Permission> list = [];
}
