import '../_imports.dart';

class Preferences {
  /// Global keys para mobile
  final reutilizables = GlobalKey();
  final puntosLimpios = GlobalKey();

  //==========================================================================
  // SYNCS
  //==========================================================================
  // Definimos las preferencias del tipo PreferenceUser para evitar errores, porque
  // el servidor filtra las llamadas de sincronización en el servidor según los permisos
  // de lectura que tiene el usuario que realiza la petición y no se obtienen todos los modelos.
  final lastSyncCleanPoint = PreferenceUser(
    "lastSyncCleanPoint",
    PreferenceBase.dateTime,
  );
  final lastSyncCitizen = PreferenceUser(
    "lastSyncCitizen",
    PreferenceBase.dateTimeNP,
  );
  final lastSyncResidue = PreferenceUser(
    "lastSyncResidues",
    PreferenceBase.dateTimeWithRemovedsNP,
  );
  final lastSyncZone = PreferenceUser(
    "lastSyncZone",
    PreferenceBase.dateTime,
  );
  final lastSyncEvent = PreferenceUser(
    "lastSyncEvent",
    PreferenceBase.dateTime,
  );

  final lastSyncMenuSSO = PreferenceUser(
    "lastSyncMenuSSO",
    PreferenceBase.dateTime,
  );
  final lastSyncProduct = PreferenceUser(
    "lastSyncProduct",
    PreferenceBase.dateTime,
  );
  final lastSyncProductCategory = PreferenceUser(
    "lastSyncProductCategory",
    PreferenceBase.dateTime,
  );
  final lastSyncProductRequest = PreferenceUser(
    "lastSyncProductRequest",
    PreferenceBase.dateTime,
  );
  final lastSyncProductWithdrawalRequest = PreferenceUser(
    "lastSyncProductWithdrawalRequest",
    PreferenceBase.dateTime,
  );
  final lastSyncUserData = PreferenceUser(
    "lastSyncUserData",
    PreferenceBase.dateTime,
  );
  final lastSyncUserSSO = PreferenceUser(
    "lastSyncUserSSO",
    PreferenceBase.dateTime,
  );
  final lastSyncLopd = PreferenceUser(
    "lastSyncLopd",
    PreferenceBase.dateTimeNP,
  );

  //==========================================================================
  // OTHERS
  //==========================================================================
  final language = LanguagePref();
  final companyApp = PreferenceGlobal<String>(
    "companyApp",
    "",
  );
  final autologinUserEmail = PreferenceGlobal<String>(
    "autologinUserEmail",
    "",
  );
  final autologinUserPassword = PreferenceGlobal<String>(
    "autologinUserPassword",
    "",
  );

  final autologinSSOCompanyId = PreferenceGlobal<int>(
    "autologinSSOCompanyId",
    -1,
  );

  final autologinSSORoleId = PreferenceGlobal<int>(
    "autologinSSORoleId",
    -1,
  );

  final userId = PreferenceGlobal<int>(
    "userId",
    -1,
  );
  final isTutorialSeen = PreferenceGlobal(
    "isTutorialSeen",
    false,
  );
  final isProductListShowedInGrid = PreferenceUser<bool>(
    "isProductListShowedInGrid",
    true,
  );

  final urlServer = PreferenceFake(
    () async => !isStringNullOrEmpty(EnvironmentDebug().urlServer)
        ? EnvironmentDebug().urlServer
        : global.ticket!.urlApi,
    (_) async => null,
  );
}
