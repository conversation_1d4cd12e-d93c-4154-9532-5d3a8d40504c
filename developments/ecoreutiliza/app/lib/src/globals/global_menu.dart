import '../_imports.dart';

class GlobalMenu {
  late Permission homePageTabActive;
  String get currentTitle => homePageTabActive.title;

  // Lista de menu inferior de la aplicacion
  List<Permission> get homePageTabs => [
        Permission(
          title: tt(TTShared.notificaciones),
          icon: global.iconNotification,
          action: MenuAction.notificaciones,
        ),
        Permission(
          title: tt(TTER.puntosLimpios),
          icon: global.iconCleanPoint,
          action: MenuAction.puntosLimpios,
        ),
        Permission(
          title: tt(TTProduct.reutilizables),
          icon: global.iconStore,
          action: MenuAction.productos,
        ),
        Permission(
          title: tt(TTShared.solicitudes),
          icon: global.iconRequest,
          action: MenuAction.solicitudes,
        ),
        Permission(
          title: tt(TTShared.ajustes),
          icon: global.iconSetting,
          action: MenuAction.ajustes,
        ),
      ];

  late Permission cleanPointPageListTabActive;

  // Lista de menu inferior de la aplicacion
  List<Permission> get cleanPointPageListTabs => [
        Permission(
          title: tt(TTShared.mapa),
          icon: global.iconMap,
          action: MenuAction.map,
        ),
        Permission(
          title: tt(TTShared.listado),
          icon: global.iconList,
          action: MenuAction.listado,
        ),
      ];

  List<Permission> productRequestPageListTabs = [
    Permission(
      title: tt(TTProduct.entrega),
      icon: FontAwesomeIcons.rightToBracket,
      action: MenuAction.solicitudes,
    ),
    Permission(
      title: tt(TTProduct.retirada),
      icon: FontAwesomeIcons.rightFromBracket,
      action: MenuAction.solicitudesRetiro,
    ),
  ];

  Permission? productRequestPageListTabActive;

  getDefaultCurrentMenu() {
    homePageTabActive = homePageTabs[2];
    cleanPointPageListTabActive = cleanPointPageListTabs[0];
    productRequestPageListTabActive = productRequestPageListTabs[0];
  }

  /*
   * Singleton factory
   */
  static GlobalMenu? _globalMenu;
  factory GlobalMenu() {
    _globalMenu ??= GlobalMenu._internal();
    return _globalMenu!;
  }
  GlobalMenu._internal() {
    getDefaultCurrentMenu();
  }

  Widget getCurrentMenuPage() {
    switch (homePageTabActive.action) {
      case MenuAction.productos:
        final provider = global.home!.productListProvider.get()!;
        return ProductListPage(provider);
      case MenuAction.puntosLimpios:
        final provider = global.home!.cleanPointListProvider.get();
        return CleanPointPage(provider);
      case MenuAction.notificaciones:
        return EventListPage();
      case MenuAction.ajustes:
        return UserProfilePage();
      case MenuAction.solicitudes:
        return ProductRequestListPage();

      default:
        Log().error("MenuAction no implementado: ${homePageTabActive.action}");
        return Container();
    }
  }

  void goToLocalMap(CleanPointBuildingModel cleanPoint) async {
    NavigatorService().popToMainPage();

    cleanPointPageListTabActive = cleanPointPageListTabs[0];
    global.home!.openPageCleanPointMap();
    global.home!.rebuild();
    await Future.delayed(const Duration(milliseconds: 1200));

    final position = cleanPoint.getEntrancePosition(zoneManager.modelsMap);
    if (position != null) {
      global.home!.mapBloc.centerMapStream.sink(
        MapCenterData.withStreetZoom(
          LatLng(position.lat, position.long),
        ),
      );
    }
  }

  Widget getCurrentCleanPointMenuPage({
    Function(CleanPointBuildingModel)? onTap = null,
    bool showFloattingActionButton = true,
    bool showSchedule = false,
    CleanPointListProvider? provider,
  }) {
    if (provider == null) provider = global.home!.cleanPointListProvider.get()!;
    global.home!.updateMapItems(
      provider,
      onPressed: onTap,
    );
    if (cleanPointPageListTabActive.action == MenuAction.map) {
      return Scaffold(
        floatingActionButton: showFloattingActionButton
            ? FloatingButtonER(global.home!.getCleanPointMapFloatingButtons())
            : null,
        body: CleanPointMapScreen(
          provider,
        ),
      );
    }
    if (cleanPointPageListTabActive.action == MenuAction.listado) {
      return CleanPointListScreenMobile(
        provider,
        padding: true,
        onTap: onTap,
        showSchedule: showSchedule,
      );
    }
    return Container();
  }
}

void onPressedMenuAction(MenuAction a) async {
  switch (a) {
    default:
      Log().error("MenuAction no implementado: $a");
      break;
  }
}
