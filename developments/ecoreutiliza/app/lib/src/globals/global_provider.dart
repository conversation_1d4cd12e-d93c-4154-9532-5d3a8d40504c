import '../_imports.dart';

class GlobalProvider {
  final iconCleanPoint = FontAwesomeIcons.locationDot;
  final iconStore = FontAwesomeIcons.store;
  final iconMap = FontAwesomeIcons.locationDot;
  final iconList = FontAwesomeIcons.list;
  final iconNotification = FontAwesomeIcons.bell;
  final iconSetting = FontAwesomeIcons.gear;
  final iconRequest = FontAwesomeIcons.arrowRightArrowLeft;

  UserSSOModel? userSSO;

  /// Notificación pendiente de mostrarse cuando cargue o se reconstruya la HomePage.
  NotificationData? _notificationPending;
  bool _notificationInProgress = false;

  final translationProvider = TranslationProvider();

  TicketModel? ticket;
  HomeProvider? home;
  CitizenModel? citizen;
  String? profileImage;

  // Medidas globales
  double initialTopPadding = 52.5;

  late final languages = LanguageType.values;

  bool isValidTicket() => !isNull(ticket?.user?.id);

  void restart() {
    ticket = null;
    home?.dispose();
    home = null;
    citizen = null;
    profileImage = null;
    _notificationPending = null;
    _notificationInProgress = false;
    userSSO = null;
  }

  Future<Either<UserBase, String>> getLoggedUser() async {
    final ticket = global.ticket;
    final role = ticket!.getRole();
    final id = ticket.user!.id;

    // Only citizens can access.
    if (role != SSORole.citizen)
      return Either.right(TTShared.noTienesAccesoAEstaAplicacion.tt);

    user.login(
      email: ticket.user!.email,
      id: id,
      companyId: ticket.company!.idInNP,
      companyIdSSO: ticket.company!.idInSSO,
      role: role,
      isInternal: ticket.user!.isInternal,
    );

    profileImage = await userSSOUseCase.getImageByMd5(ticket.user?.imageMd5);

    // Comprobamos si existe.
    citizen =
        await citizenUseCase.getAdditionalUserData(ticket.user?.email ?? "");

    // Si no existe, lo creamos.
    if (citizen == null ||
        citizen?.cleanPointInfo?.companyId != ticket.company?.idInNP) {
      final guid = citizen?.guid ?? Guid().get();
      await citizenUseCase.register(
        CitizenModel(
            name: citizen?.name ?? ticket.user?.name ?? "",
            surname: citizen?.surname ?? "",
            nif: citizen?.nif ?? "",
            address: citizen?.address ?? citizen?.address ?? "",
            postalCode: citizen?.postalCode ?? "",
            province: citizen?.province ?? "",
            autonomousCommunity: citizen?.autonomousCommunity ?? "",
            cadastralNumber: citizen?.cadastralNumber ?? "",
            cardIds: citizen?.cardIds ?? [],
            door: citizen?.door ?? "",
            floor: citizen?.floor ?? "",
            municipality: citizen?.municipality ?? "",
            number: citizen?.number ?? "",
            town: citizen?.town ?? "",
            validationStatus:
                citizen?.validationStatus ?? CitizenValidationStatus.pending.id,
            zoneId: citizen?.zoneId ?? 0,
            cards: citizen?.cards ?? [],
            cleanPointInfo: CitizenCleanPointInfoModel(
              id: id,
              guid: Guid().get(),
              companyId: ticket.company!.idInNP,
              citizenGuid: guid,
              points: 0,
              favorites: [],
            ),
            phone: citizen?.phone ?? "",
            email: citizen?.email ?? ticket.user?.email ?? "",
            observations: citizen?.observations ?? "",
            id: citizen?.id ?? 0,
            guid: guid,
            modifyDate: now,
            creationDate: citizen?.creationDate ?? now,
            isRemoved: false,
            isSync: false,
            addresses: [],
            district: '',
            neighborhood: ''),
      );

      citizen =
          await citizenUseCase.getAdditionalUserData(ticket.user?.email ?? "");
    }

    // Si no ha sido creado, no se puede acceder al usuario.
    if (citizen == null ||
        citizen!.cleanPointInfo?.companyId != ticket.company?.idInNP ||
        citizen!.isRemoved ||
        citizen!.validationStatusEnum != CitizenValidationStatus.accepted) {
      final status = citizen?.validationStatusEnum;
      citizen = null;
      user.logout();

      if (status == CitizenValidationStatus.pending)
        return Either.right(
          TTCitizen.tuMunicipioEstaVerificandoTusDatos.tt
              .point()
              .jump()
              .jump()
              .concat(TTCitizen.teEnviaremosUnEmailValidadoTusDatos.tt)
              .point(),
        );
      return Either.right(TTShared.noTienesAccesoAEstaAplicacion.tt);
    }

    global.home ??= HomeProvider();
    await global.home?.start();

    user.citizenGuid = citizen!.guid;

    Log().info("Login usuario: $user");

    return Either.left(user);
  }

  void onCompletedLogin() {
    NavigatorService().pushReplacement(HomePage());
  }

  void addNotificationPeding(String event) {
    if (isStringNullOrEmpty(event)) return;
    final n = NotificationData.fromString(event);
    if (n == null) return;
    _notificationPending = n;
    home?.rebuild();
  }

  void showNotificationPending() async {
    if (_notificationPending == null) return;
    if (_notificationInProgress) return;
    _notificationInProgress = true;

    try {
      if (_notificationPending!.id ==
              ERNotificationType.productRequestDelivery.name ||
          _notificationPending!.id ==
              ERNotificationType.productRequestWithdrawal.name) {
        if (_notificationPending!.isOne()) {
          await productManager.start();

          final product = productManager.models.firstWhere2(
            (m) => m.guid == _notificationPending!.modelIds.first,
          );

          if (product == null) {
            // ignore: unawaited_futures
            // DialogService().showCustomDialog(
            //     ProductRequestListDialog(ProductRequestListProvider()), true);
          } else {
            // ignore: unawaited_futures
            DialogService().showCustomDialog(
                ProductDialog(ProductProvider(product)), true);
          }
        } else {
          // ignore: unawaited_futures
          // DialogService().showCustomDialog(
          //     ProductRequestListDialog(ProductRequestListProvider()), true);
        }
      } else if (_notificationPending!.id == ERNotificationType.event.name) {
        await eventManager.start();

        // ignore: unawaited_futures
        DialogService().showCustomDialog(EventListDialog(), true);
      }
    } catch (e) {
      Log().error("[showNotificationPending] $e");
    }

    _notificationPending = null;
    _notificationInProgress = false;
  }

  /*
   * Singleton factory
   */
  static GlobalProvider? _bloc;
  factory GlobalProvider() {
    if (_bloc == null) _bloc = GlobalProvider._internal();
    return _bloc!;
  }
  GlobalProvider._internal();
}
