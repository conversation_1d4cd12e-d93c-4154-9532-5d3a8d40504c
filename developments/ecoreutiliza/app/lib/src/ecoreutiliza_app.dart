import 'package:database_hive_provider/database_hive_provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pipeline_date_formatting/pipeline_date_formatting.dart';
import 'package:pipeline_log/pipeline_log.dart';
import 'package:pipeline_new_version/pipeline_new_version.dart';
import 'package:pipeline_sso_environment_changed/pipeline_sso_environment_changed.dart';
import 'package:translation_provider_json/translation_provider_json.dart';
import 'package:pipeline_ticket_id_saver/pipeline_ticket_id_saver.dart';

import '_imports.dart';

class EcoReutilizaApp extends StatelessWidget {
  final EcoReutilizaEnvironment environment;
  String? ticketId;

  EcoReutilizaApp({
    required this.environment,
    required this.ticketId,
  }) {
    injector.registerSingleton<EcoReutilizaEnvironment>(environment);
  }

  @override
  Widget build(BuildContext context) {
    return RestartWidget(
      child: ThemePage(
        builder: (_) => GetMaterialApp(
          scrollBehavior: ScrollBehaviourMultiplatform(),
          localizationsDelegates: [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: [
            const Locale('es', ''),
          ],
          theme: ThemeProvider().theme.themeData.copyWith(
                colorScheme:
                    ThemeProvider().theme.themeData.colorScheme.copyWith(
                          secondary: themeColors.primaryBlueLight,
                        ),
                textTheme: GoogleFonts.robotoTextTheme(
                  Theme.of(context)
                      .textTheme, // If this is not set, then ThemeData.light().textTheme is used.
                ),
              ),
          home: Builder(
            builder: (___) => StartPage(
              splashScreenContainer:
                  SplashScreenContainer(message: "BIENVENIDO"),
              pipelineController: PipelineController(
                  steps: [
                    () => PipelineLog(),
                    () => PipelineTicketIdSaver(
                          ticketId,
                          (newTicketId) => ticketId = newTicketId,
                        ),
                    () => PipelineStep(
                          onExecute: () async {
                            await EcoReutilizaInjector().init();
                          },
                          onErrorFunction: () async {},
                        ),
                    // Inicia la base de datos.
                    () => PipelineStep(
                        timeout: const Duration(seconds: 20),
                        onExecute: () async {
                          Log().debug("INICIANDO BASE DE DATOS");

                          await injector.get<DataServiceBase>().startDatabase(
                                () => DatabaseHiveService()
                                    .startDatabase(DatabaseHiveProvider()),
                              );
                        },
                        onErrorFunction: () async {}),
                    () => PipelineStep(
                          onExecute: () async {
                            Log().debug("INICIANDO CONNECTION SERVICE");
                            // No detenemos el arranque en el servicio de conexión para que inicie lo más rápido posible.
                            injector.get<ConnectionServiceBase>().start();
                          },
                          onErrorFunction: () async {},
                        ),
                    () => PipelineDateFormatting(),

                    // Iniciamos servicio de traducción.
                    () => PipelineStep(
                          onExecute: () async {
                            Log().debug("ESTABLECIENDO TRADUCCIONES");
                            await TranslationServiceGlobal().start(
                              defaultLanguageType: LanguageType.spanish,
                              languagePref: preferences.language,
                              translationProvider: TranslationProviderJson(),
                            );
                          },
                          onErrorFunction: () async {},
                        ),
                    () => PipelineSsoEnvironmentChanged(
                          removeDatabase: () async {
                            await DatabaseHiveService()
                                .repair(isRemovedAll: true);
                          },
                          restartApp: () async {
                            await environment.restartService.restart();
                          },
                        ),

                    // Se añade para un futuro cuando haya que realizar algún cambio según la versión.
                    () => PipelineNewVersion(
                          packageInfoService: packageInfoService,
                          onNewVersion: (before, current) async {
                            if (before.isEmpty) return;
                            await DatabaseHiveService().repair();
                          },
                          onPostNewVersion: (before, current) async {
                            if (before.isEmpty) return;
                            await restartService.restart();
                          },
                        ),
                  ],
                  onCompleted: () => LoginUseCase(
                        isValidTicket: () => global.isValidTicket(),
                        pushLoginPage: () => NavigatorService()
                            .pushReplacement(UserAccountLoginPage()),
                        autologinUserEmail: preferences.autologinUserEmail,
                        autologinUserPassword:
                            preferences.autologinUserPassword,
                        autologinSSOCompanyId:
                            preferences.autologinSSOCompanyId,
                        autologinSSORoleId: preferences.autologinSSORoleId,
                        getLoggedUser: global.getLoggedUser,
                        ticketUseCase: injector.get<TicketUseCase>(),
                        connection: connection,
                        applicationId: SSOApplication.EcoReutiliza.id,
                        setTicket: (t) => global.ticket = t,
                        onCompletedLogin: global.onCompletedLogin,
                        app: user.app,
                        ssoUseCase: ssoUseCase,
                      ).tryAutologin(ticketId ?? ""),
                  onError: (e, i) async {
                    final message = "Error en el pipeline de inicio ($i): $e";
                    // Lo imprimimos para que se pueda ver en la consola de la web.
                    print(message);
                    Log().catastrophe(message);
                    Log().info(
                        "Borrando la base de datos y reiniciando la aplicación");
                    try {
                      await DatabaseHiveService().repair(isRemovedAll: true);
                      await DatabaseHiveService().close();
                    } catch (e) {}
                    restartService.restart();
                  }),
            ),
          ),
        ),
      ),
    );
  }
}

class RestartWidget extends StatefulWidget {
  RestartWidget({this.child});

  final Widget? child;

  static void restartApp(BuildContext context) {
    context.findAncestorStateOfType<_RestartWidgetState>()!.restartApp();
  }

  @override
  _RestartWidgetState createState() => _RestartWidgetState();
}

class _RestartWidgetState extends State<RestartWidget> {
  Key key = UniqueKey();

  void restartApp() {
    setState(() {
      key = UniqueKey();
    });
  }

  @override
  Widget build(BuildContext context) {
    return KeyedSubtree(
      key: key,
      child: widget.child!,
    );
  }
}
