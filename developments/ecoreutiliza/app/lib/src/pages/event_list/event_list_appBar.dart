import '../../_imports.dart';

class EventListAppBar extends StatelessWidget implements PreferredSizeWidget {
  final EventListProvider? provider;

  EventListAppBar(this.provider);

  @override
  Widget build(BuildContext context) {
    List<Widget> additionalActions = [];

    return AppBar(
      backgroundColor: themeColors.primaryBlue,
      titleSpacing: 5,
      title: Container(
        height: 60,
        width: 190,
        alignment: Alignment.centerLeft,
        child: Padding(
          padding: const EdgeInsets.only(top: 4),
          child: ImageCacheService().getFromAsset(
            "assets/images/ecoreutiliza_white.svg",
            fit: BoxFit.fitHeight,
          ),
        ),
      ),
      actions: [
        GestureDetector(
          child: const Icon(FontAwesomeIcons.check),
          onTap: provider!.onPressedCheckAllEvents,
        ),
        IconButton(
          padding: const EdgeInsets.all(0),
          icon: const Icon(FontAwesomeIcons.solidTrashCan),
          onPressed: provider!.onPressedDeleteAllCheckedEvents,
        )
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(60);
}
