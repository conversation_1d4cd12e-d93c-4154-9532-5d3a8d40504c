import '../../_imports.dart';

class EventItemList extends StatelessWidget {
  final EventModel model;
  final void Function() onPressed;

  const EventItemList(this.model, this.onPressed);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: CardER(
        margin: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        color: model.isSeen ? null : Colors.orange[50],
        child: Container(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: H2Text(
                      model.typeEnum.tt,
                      maxLines: 2,
                      isUpperCase: false,
                    ),
                  ),
                  Container(
                    width: 30,
                    height: 30,
                    alignment: Alignment.center,
                    padding: const EdgeInsets.only(bottom: 3),
                    child: GestureDetector(
                      onTap: () {
                        eventUseCase.remove(model);
                      },
                      child: Icon(
                        FontAwesomeIcons.xmark,
                        color: themeColors.grey3,
                        size: 22,
                      ),
                    ),
                  ),
                ],
              ),
              SeparatorFiori(),
              Wrap(
                runSpacing: 10,
                spacing: 10,
                children: [
                  Container(
                    width: 200,
                    child: TooltipCustom(
                      message: tt(TTShared.fecha),
                      child: LabelIconER(
                        icon: const Icon(FontAwesomeIcons.calendarDays),
                        text: model.creationDate.view(),
                      ),
                    ),
                  ),
                  // Debe imprimir variaciones de puntos negativos.
                  isNullOrZero(model.points)
                      ? const SizedBox()
                      : Container(
                          width: 100,
                          child: TooltipCustom(
                            message: tt(TTER.puntos),
                            child: LabelIconER(
                              icon: const Icon(FontAwesomeIcons.solidStar),
                              text: model.points.toString(),
                            ),
                          ),
                        ),
                  isStringNullOrEmpty(model.productName)
                      ? const SizedBox()
                      : Container(
                          width: 250,
                          child: TooltipCustom(
                            message: tt(TTProduct.reutilizable),
                            child: LabelIconER(
                              icon: const Icon(FontAwesomeIcons.box),
                              text: model.productName,
                            ),
                          ),
                        ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
