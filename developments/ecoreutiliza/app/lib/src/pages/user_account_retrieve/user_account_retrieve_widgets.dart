import 'package:dots_indicator/dots_indicator.dart';

import '../../_imports.dart';

class Dots extends StatelessWidget {
  final int total;
  final int current;
  final void Function(int i) onPressed;

  Dots(this.total, this.current, this.onPressed);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 30,
      child: Stack(
        children: [
          current == 0
              ? const SizedBox()
              : Align(
                  alignment: Alignment.centerLeft,
                  child: IconButton(
                    onPressed:
                        current <= 0 ? null : () => onPressed(current - 1),
                    icon: Icon(
                      FontAwesomeIcons.arrowLeft,
                      size: 12,
                      color: themeColors.primaryBlueLight,
                    ),
                  ),
                ),
          Align(
            alignment: Alignment.center,
            child: DotsIndicator(
              dotsCount: total,
              position: current.toDouble(),
              onTap: (i) => onPressed(i.toInt()),
              decorator: DotsDecorator(
                activeColor: themeColors.black,
                size: const Size.square(9.0),
                activeSize: const Size(18.0, 9.0),
                activeShape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5.0)),
              ),
            ),
          )
        ],
      ),
    );
  }
}
