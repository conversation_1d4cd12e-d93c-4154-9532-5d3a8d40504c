import '../../_imports.dart';

class UserAccountRetrieveStep0 extends StatelessWidget {
  final UserAccountRetrieveProvider provider;

  UserAccountRetrieveStep0(this.provider);
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          TextFormFieldER(
            title: tt(TTShared.email),
            controller: provider.emailField,
            icon: FontAwesomeIcons.solidEnvelope,
          ),
          SeparatorFiori(),
          SeparatorFiori.half(),
          Container(
            width: double.maxFinite,
            alignment: Alignment.centerRight,
            child: Container(
              width: ThemeER.dialogConfirmButtonWidth.get(),
              child: Button<PERSON>(
                title: tt(TTShared.continuar),
                color: themeColors.secondaryGreen,
                colorSplash: themeColors.white,
                colorText: themeColors.white,
                icon: FontAwesomeIcons.circleArrowRight,
                onPressed: provider.onCompletedStep0,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class UserAccountRetrieveStep1 extends StatelessWidget {
  final UserAccountRetrieveProvider provider;

  UserAccountRetrieveStep1(this.provider);
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SubtitleSectionFiori(
            tt(TTShared.introduceElCodigoDeRecuperacionQueHasRecibidoEnTuEmail)
                .point()),
        SeparatorFiori(),
        TextFormFieldER(
          title: tt(TTShared.codigo),
          controller: provider.codeField,
          icon: FontAwesomeIcons.key,
        ),
        SeparatorFiori(),
        SeparatorFiori.half(),
        Container(
          width: double.maxFinite,
          alignment: Alignment.centerRight,
          child: Container(
            width: ThemeER.dialogConfirmButtonWidth.get(),
            child: ButtonER(
              title: tt(TTShared.continuar),
              color: themeColors.secondaryGreen,
              colorSplash: themeColors.white,
              colorText: themeColors.white,
              icon: FontAwesomeIcons.circleArrowRight,
              onPressed: provider.onCompletedStep1,
            ),
          ),
        ),
      ],
    );
  }
}

class UserAccountRetrieveStep2 extends StatelessWidget {
  final UserAccountRetrieveProvider provider;

  UserAccountRetrieveStep2(this.provider);
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SubtitleSectionFiori(tt(TTShared.introduceLaNuevaContrasenia).point()),
        SeparatorFiori(),
        Container(
          alignment: Alignment.center,
          child: PasswordFieldER(controller: provider.passwordController),
        ),
        SeparatorFiori(),
        Container(
          alignment: Alignment.center,
          child: PasswordFieldER(
            controller: provider.passwordRepeatController,
            hint: TTShared.repiteContrasenia.tt,
          ),
        ),
        SeparatorFiori(),
        SeparatorFiori.half(),
        Container(
          width: double.maxFinite,
          alignment: Alignment.centerRight,
          child: Container(
            width: ThemeER.dialogConfirmButtonWidth.get(),
            child: ButtonER(
              title: tt(TTShared.finalizar),
              color: themeColors.primaryBlue,
              colorSplash: themeColors.white,
              colorText: themeColors.white,
              icon: FontAwesomeIcons.circleCheck,
              onPressed: provider.onCompletedStep2,
            ),
          ),
        ),
      ],
    );
  }
}
