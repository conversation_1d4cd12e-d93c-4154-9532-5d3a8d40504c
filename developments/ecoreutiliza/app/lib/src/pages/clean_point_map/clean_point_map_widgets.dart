import '../../_imports.dart';

class LegendIconsDialog extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return DialogER(
      title: tt(TTER.leyendaDeIconos),
      child: MarkerInformation(),
      // isShowedCloseButton: false,
    );
  }
}

class MarkerInformation extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: ColumnER(
        children: [
          _item(
            color: themeColors.validGreen,
            text: tt(TTProduct.seAceptanTodasLasCategorias),
          ),
          const SizedBox(height: 7),
          _item(
            color: themeColors.alertOrange,
            text: tt(TTProduct.noSeAceptanTodasLasCategorias),
          ),
          const SizedBox(height: 7),
          _item(
            color: themeColors.errorRed,
            text: tt(TTProduct.noSeAceptanReutilizables),
          ),
          const SizedBox(height: 7),
          _item(
            padding: const EdgeInsets.only(bottom: 2, right: 3, left: 2),
            color: themeColors.black,
            text: tt(TTER.puntoLimpioFijo),
            icon: FontAwesomeIcons.recycle,
            hasDecoration: false,
          ),
          const SizedBox(height: 7),
          _item(
            padding: const EdgeInsets.only(bottom: 2, right: 3, left: 2),
            color: themeColors.black,
            text: tt(TTER.puntoLimpioMovil),
            icon: FontAwesomeIcons.truckMoving,
            hasDecoration: false,
          ),
        ],
      ),
    );
  }

  Widget _item({
    required Color? color,
    required String text,
    IconData? icon,
    EdgeInsets? padding,
    bool hasDecoration = true,
  }) =>
      Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            height: 30,
            width: 30,
            padding: padding ?? const EdgeInsets.only(bottom: 2, right: 3),
            alignment: Alignment.center,
            child: icon == null
                ? null
                : Icon(
                    icon,
                    color: color ?? themeColors.primaryBlue,
                    size: 30.0 - 13.0,
                  ),
            decoration: !hasDecoration
                ? null
                : BoxDecoration(
                    border: Border.all(
                      color: color ?? themeColors.primaryBlue,
                      width: 2,
                    ),
                    color: themeColors.transparent,
                    shape: BoxShape.circle,
                  ),
          ),
          const SizedBox(width: 10),
          SmallText(text),
        ],
      );
}

class CleanPointMapFilterScreenER extends StatefulWidget {
  final Map<ScheduleDay, bool> daysOpen;

  const CleanPointMapFilterScreenER({Key? key, required this.daysOpen})
      : super(key: key);
  @override
  State<CleanPointMapFilterScreenER> createState() =>
      CleanPointMapFilterScreenStateER();
}

class CleanPointMapFilterScreenStateER
    extends State<CleanPointMapFilterScreenER> {
  @override
  Widget build(BuildContext context) {
    final daysKeys = widget.daysOpen.keys.toList();
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        children: [
          ListView.separated(
            key: Key(widget.daysOpen.toString()),
            separatorBuilder: (context, index) => const SizedBox(height: 8),
            shrinkWrap: true,
            itemCount: widget.daysOpen.length,
            itemBuilder: (context, index) {
              return CheckBoxForm(
                title: (daysKeys[index].translation),
                onChecked: (value) {
                  widget.daysOpen[daysKeys[index]] =
                      !widget.daysOpen[daysKeys[index]]!;
                },
                isStartChecked: widget.daysOpen.values.toList()[index],
              );
            },
          ),
          SeparatorFiori(),
          Container(
            height: 50,
            width: double.maxFinite,
            child: Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: ButtonER.styleGreen(
                    onPressed: () {
                      widget.daysOpen.forEach((key, value) {
                        widget.daysOpen[key] = true;
                      });
                      setState(() {});
                    },
                    title: TTShared.todos.tt,
                    icon: FontAwesomeIcons.circleCheck,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: ButtonER.styleRed(
                    onPressed: () {
                      widget.daysOpen.forEach((key, value) {
                        widget.daysOpen[key] = false;
                      });
                      setState(() {});
                    },
                    title: TTShared.ninguno.tt,
                    icon: FontAwesomeIcons.circleXmark,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
