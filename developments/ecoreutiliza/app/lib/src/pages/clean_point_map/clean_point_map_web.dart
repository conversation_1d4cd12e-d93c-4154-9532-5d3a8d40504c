import '../../_imports.dart';

class CleanPointMapWeb extends StatelessWidget {
  final CleanPointListProvider? provider;
  const CleanPointMapWeb(this.provider);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: themeColors.greyBackground,
      floatingActionButton:
          FloatingButtonER(global.home!.getCleanPointMapFloatingButtons()),
      body: CardER(
        padding: EdgeInsets.zero,
        margin: EdgeInsets.all(ThemeER.bodySeparationProjectWeb),
        child: CleanPointMapScreen(provider),
      ),
    );
  }
}
