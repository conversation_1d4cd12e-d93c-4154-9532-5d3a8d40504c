import '../../_imports.dart';

class ProductListGrid extends StatelessWidget {
  static ResponsiveData<int> _maxItems = ResponsiveData<int>(
    mobileDefault: 1,
    tabletLarge: 3,
    tabletDefault: 2,
    webSmall: 2,
    webNormal: 3,
    webLarge: 3,
    webExtraLarge: 4,
    defaultData: 4,
  );

  static ResponsiveData<double> _spacing = ResponsiveData<double>(
    mobileSmall: 2,
    mobileDefault: 5,
    defaultData: 12,
  );

  final ProductListProvider provider;
  final List<ProductModel> products;

  ProductListGrid(this.provider, this.products);

  @override
  Widget build(BuildContext context) {
    Widget child = GridView.builder(
      // padding: EdgeInsets.only(top: ThemeER.bodySeparation / 2),
      controller: provider.scrollControllerProducts,
      physics: const BouncingScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _maxItems.get() * 2,
        mainAxisSpacing: _spacing.get(),
        crossAxisSpacing: _spacing.get(),
        // width / height: fixed for *all* items
        childAspectRatio: 253 / 220,
      ),
      itemBuilder: (context, i) =>
          ProductItemCard(products[i], () => provider.rebuild()),
      itemCount: products.length,
    );

    return ScrollConfiguration(
      behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
      child: child,
    );
  }
}

class ProductListAppBar extends StatelessWidget implements PreferredSizeWidget {
  final ProductListProvider? provider;

  ProductListAppBar(this.provider);

  @override
  Widget build(BuildContext context) {
    List<Widget> additionalActions = [];

    return AppBarSearchHiden(
      onChanged: (v) => provider!.filter!.onChangedText(v),
      onCleared: () => provider!.filter!.onChangedText(""),
      isClearOnSubmit: false,
      isClosedOnSubmit: false,
      initialValue: provider!.filter!.searchController.text,
      defaultAppBar: (c, button) => AppBar(
        backgroundColor: themeColors.primaryBlue,
        titleSpacing: 5,
        title: Container(
          height: 60,
          width: 190,
          alignment: Alignment.centerLeft,
          child: Padding(
            padding: const EdgeInsets.only(top: 4),
            child: ImageCacheService().getFromAsset(
              "assets/images/ecoreutiliza_white.svg",
              fit: BoxFit.fitHeight,
            ),
          ),
        ),
        actions: [
          button,
          StreamBuilder(
              stream: provider!.rebuild.stream,
              builder: (_, __) => Container(
                    child: ActionButtonAppBar(
                      color: provider!.filter!.isAppliedAnyFilter()
                          ? themeColors.errorRed
                          : themeColors.white,
                      icon: FontAwesomeIcons.filter,
                      onPressed: () {
                        Scaffold.of(context).openEndDrawer();
                      },
                      tooltip: tt(TTShared.filtrar),
                    ),
                  )),
          ...additionalActions,
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(60);
}

class ProductFilterDrawer extends StatefulWidget {
  final ProductListProvider? provider;

  ProductFilterDrawer(this.provider);

  @override
  State<ProductFilterDrawer> createState() => _ProductFilterDrawerState();
}

class _ProductFilterDrawerState extends State<ProductFilterDrawer> {
  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Container(
        child: SafeArea(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            color: themeColors.greyBackground,
            child: ScrollConfiguration(
              behavior:
                  ScrollConfiguration.of(context).copyWith(scrollbars: false),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 40),
                    Container(
                      child: ButtonER(
                        title: tt(TTShared.restablecer),
                        color: themeColors.primaryBlue,
                        colorSplash: themeColors.primaryBlueLight,
                        colorText: themeColors.white,
                        onPressed: () {
                          widget.provider!.filter!.restart();
                          widget.provider!.rebuild();
                          setState(() {});
                        },
                      ),
                    ),
                    // RESERVA: Comentado temporalmente hasta que decidan de implementarlo de nuevo.
                    // Column(
                    //   children: [
                    //     SizedBox(height: 40),
                    //     SwitchButtonER(
                    //       title: tt(TTER.mostrarReservados),
                    //       icon: FontAwesomeIcons.lock,
                    //       isSelected: () =>
                    //           widget.provider!.filter!.isShowedReserved,
                    //       onPressed: () {
                    //         widget.provider!.filter!.isShowedReserved =
                    //             !widget.provider!.filter!.isShowedReserved;
                    //         widget.provider!.rebuild();
                    //         setState(() {});
                    //       },
                    //     ),
                    //   ],
                    // ),
                    SeparatorFiori.half(),
                    Container(
                      width: double.maxFinite,
                      margin: const EdgeInsets.only(left: 5),
                      child: ListTile(
                          onTap: () {
                            widget.provider!.filter!.favorites.isSelected =
                                !widget.provider!.filter!.favorites.isSelected;
                            setState(() {});
                            widget.provider!.rebuild();
                          },
                          leading: IgnorePointer(
                            child: Icon(
                              FontAwesomeIcons.solidHeart,
                              color:
                                  widget.provider!.filter!.favorites.isSelected
                                      ? themeColors.secondaryGreen
                                      : null,
                            ),
                          ),
                          title: Text(tt(TTER.favoritos))),
                    ),
                    ExpansionTileCheck<ProductCategoryModel>(
                      isInitialiteExpanded: false,
                      title: tt(TTProduct.categorias),
                      subtitle: tt(TTER.seleccionados),
                      models: widget.provider!.filter!.categories,
                      builder: (m) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            m.iconWidget,
                            const SizedBox(width: 20),
                            Text(m.type.tt),
                          ],
                        );
                      },
                      onChanged: () {
                        setState(() {});
                        widget.provider!.rebuild();
                      },
                    ),
                    ExpansionTileCheck<CleanPointBuildingModel>(
                      isInitialiteExpanded: false,
                      title: tt(TTER.puntosLimpios),
                      subtitle: tt(TTER.seleccionados),
                      models: widget.provider!.filter!.cleanPoints,
                      builder: (m) {
                        return Text(m.name);
                      },
                      onChanged: () {
                        setState(() {});
                        widget.provider!.rebuild();
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class ProductItemCard extends StatelessWidget {
  static ResponsiveData<double> _iconSize = ResponsiveData<double>(
    mobileDefault: 30,
    tabletDefault: 30,
    defaultData: 30,
  );

  final ProductModel model;
  final void Function() rebuild;

  ProductItemCard(this.model, this.rebuild);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        global.home!.openPageProduct(model);
      },
      child: Container(
        child: Card(
          elevation: ThemeER.elevation,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ThemeER.radius)),
          child: Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    child: ProductImageWidget(
                      model,
                      child: Container(
                        padding: const EdgeInsets.only(bottom: 5, right: 5),
                        alignment: Alignment.bottomRight,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // RESERVA: Comentado temporalmente hasta que decidan de implementarlo de nuevo.
                            // model.isReserved()
                            //     ? RoundedTooltipIconER(
                            //         size: _iconSize.get(),
                            //         icon: model.reservedIcon,
                            //         iconColor: themeColors.errorRed,
                            //         title: StringUtil.colon(
                            //           tt(TTER.reservadoHasta),
                            //           model.reservedDateUntil!.view(),
                            //         ))
                            //     : SizedBox(),
                            // SizedBox(width: 10),
                            StreamBuilder(
                              stream: global.home!.favoritesStream.stream,
                              builder: (BuildContext context,
                                  AsyncSnapshot snapshot) {
                                return RoundedTooltipIconER(
                                  size: _iconSize.get(),
                                  icon: FontAwesomeIcons.solidHeart,
                                  iconColor: model.isFavorite
                                      ? themeColors.alertOrange
                                      : themeColors.grey6,
                                  title: tt(TTER.favorito),
                                  onPressed: () =>
                                      ProductProvider.toggleFavorite(model)
                                          .then((value) => rebuild()),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.only(
                    left: 14,
                    top: 8,
                  ),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    model.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.left,
                    maxLines: 1,
                  ),
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ProductImageWidget extends StatelessWidget {
  final ProductModel p;
  final Widget? child;

  ProductImageWidget(this.p, {this.child});

  @override
  Widget build(BuildContext context) {
    return CardER(
      padding: EdgeInsets.zero,
      margin: EdgeInsets.zero,
      child: Container(
        width: double.maxFinite,
        height: 300,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(ThemeER.radius),
          child: Stack(children: [
            Container(
              height: double.maxFinite,
              width: double.maxFinite,
              child: p.imageWidget,
            ),
            !(p.stateEnum == ProductState.available)
                ? Container(
                    height: double.maxFinite,
                    width: double.maxFinite,
                    color: Colors.grey.withOpacity(0.8),
                    child: Center(
                      child: Text(
                        tt(TTProduct.productoNoSeEncuentraDisponible),
                        style: TextStyle(
                          color: themeColors.primaryBlue,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  )
                : const SizedBox(),
            child!,
          ]),
        ),
      ),
    );
  }
}

class ProductItemListSmall extends StatelessWidget {
  final ProductModel m;

  ProductItemListSmall(this.m);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        global.home!.openPageProduct(m);
      },
      child: CardER(
        child: Container(
          height: 60,
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(ThemeER.radius),
                child: Stack(
                  children: [
                    Container(
                      height: 60,
                      width: 60,
                      child: m.imageWidget,
                    ),
                    !(m.stateEnum == ProductState.available)
                        ? Container(
                            height: 60,
                            width: 60,
                            color: Colors.grey.withOpacity(0.8),
                          )
                        : const SizedBox(),
                  ],
                ),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    H2Text(
                      m.name,
                      isUpperCase: false,
                      maxLines: 1,
                    ),
                    Container(
                      alignment: Alignment.centerLeft,
                      child: LabelIconER(
                        text: m.stateEnum.tt,
                        icon: Icon(
                          m.stateEnum.icon,
                          color: ThemeER.iconColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // RESERVA: Comentado temporalmente hasta que decidan de implementarlo de nuevo.
              // m.isReserved()
              //     ? RoundedTooltipIconER(
              //         size: 40,
              //         icon: FontAwesomeIcons.lock,
              //         iconColor: themeColors.errorRed,
              //         title: StringUtil.colon(
              //           tt(TTER.reservadoHasta),
              //           m.reservedDateUntil!.view(),
              //         ))
              //     : SizedBox()
            ],
          ),
        ),
      ),
    );
  }
}
