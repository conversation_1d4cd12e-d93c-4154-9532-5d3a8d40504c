import '../../_imports.dart';

/// Clase que contiene los filtros de catálogos.
///
/// Los campos se asignarán cuando se abra el filtro.
/// Si un valor no existe, se debe considerar como que está seleccionado.
class ProductListFilter {
  List<StreamSubscription> _subs = [];
  List<Selectable<ProductCategoryModel>> categories = [];
  List<Selectable<CleanPointBuildingModel>> cleanPoints = [];
  Selectable<bool> favorites = Selectable(false, false);
  final searchController = TextEditingController();
  final _changeTextDelayed =
      DelayedRenewableOperationV2(delay: const Duration(milliseconds: 300));
  // RESERVA: Comentado temporalmente hasta que decidan de implementarlo de nuevo.
  // bool isShowedReserved = true;

  final ProductListProvider provider;

  ProductListFilter(this.provider) {
    _subs.add(productCategoryManager.onChange.listen((event) {
      _onOpened();
    }));
    _subs.add(cleanPointBuildingManager.onChange.listen((event) {
      _onOpened();
    }));
    searchController.addListener(() {
      _changeTextDelayed.execute(() => provider.rebuild());
    });
    _onOpened();
  }

  void restart() {
    _onOpened();
    // RESERVA: Comentado temporalmente hasta que decidan de implementarlo de nuevo.
    // isShowedReserved = true;
    searchController.text = "";
    favorites.isSelected = false;
    provider.rebuild();
  }

  /// Indica si se ha modificado algún filtro para excluir los resultados.
  bool isAppliedAnyFilter() {
    if (categories.exists((m) => m.isSelected)) return true;
    if (cleanPoints.exists((m) => m.isSelected)) return true;
    if (favorites.isSelected) return true;

    return false;
  }

  bool isMatch(ProductModel p) {
    if ((isStringFilled(searchController.text) &&
            searchController.text.length > 2) &&
        !p.matchText.contains(searchController.text.toLowerCase()))
      return false;
    final selectedCategoryIds = categories
        .where((filter) => filter.isSelected)
        .map((filter) => filter.value.id)
        .toSet();
    if (selectedCategoryIds.isNotEmpty) {
      final Selectable<ProductCategoryModel>? category =
          categories.firstWhere2((m) => p.categoryId == m.value.id);
      if (isFalse(category?.isSelected)) return false;
    }

    final selectedCleanPointIds = cleanPoints
        .where((filter) => filter.isSelected)
        .map((filter) => filter.value.id)
        .toSet();

    if (selectedCleanPointIds.isNotEmpty) {
      final Selectable<CleanPointBuildingModel>? cleanPoint =
          cleanPoints.firstWhere2((m) => p.cleanPointGuid == m.value.guid);
      if (isFalse(cleanPoint?.isSelected)) return false;
    }
    if (favorites.isSelected && !p.isFavorite) return false;

    // RESERVA: Comentado temporalmente hasta que decidan de implementarlo de nuevo.
    // if (!isShowedReserved && p.isReserved()) return false;
    // if (isShowedReservedOnlyAdmin && !p.isReserved()) return false;
    return true;
  }

  void _onOpened() {
    cleanPoints.clear();
    cleanPointBuildingManager.models.forEach(
      (m) => cleanPoints.addNew(
        Selectable(m, false),
        (m1, m2) => m1.value.guid == m2.value.guid,
      ),
    );
    categories.clear();
    productCategoryManager.models.forEach(
      (m) => categories.addNew(
        Selectable(m, false),
        (m1, m2) => m1.value.id == m2.value.id,
      ),
    );
  }

  void dispose() {
    _subs.forEach((s) => s.cancel());
  }

  List<ProductCategoryModel> getSelectedCategories() =>
      categories.where2((m) => m.isSelected).map((e) => e.value).toList();

  void onSelectedCategory(ProductCategoryModel category) {
    final Selectable<ProductCategoryModel>? exists =
        categories.firstWhere2((m) => m.value.id == category.id);
    if (exists != null) {
      exists.isSelected = !exists.isSelected;
      provider.rebuild();
    }
  }

  /// Se llama desde mobile cuando modifca el texto en el AppBar.
  void onChangedText(String v) {
    searchController.text = v;
  }
}
