import '../../_imports.dart';

class UserAccountLoginPage extends StatefulWidget {
  @override
  _UserAccountLoginPageState createState() => _UserAccountLoginPageState();
}

class _UserAccountLoginPageState extends State<UserAccountLoginPage> {
  final provider = LoginProvider(
    languages: global.languages,
    loginUseCase: LoginUseCase(
      isValidTicket: () => global.isValidTicket(),
      pushLoginPage: () =>
          NavigatorService().pushReplacement(UserAccountLoginPage()),
      autologinUserEmail: preferences.autologinUserEmail,
      autologinUserPassword: preferences.autologinUserPassword,
      autologinSSOCompanyId: preferences.autologinSSOCompanyId,
      autologinSSORoleId: preferences.autologinSSORoleId,
      ticketUseCase: injector.get<TicketUseCase>(),
      connection: connection,
      applicationId: user.app.id,
      setTicket: (t) => global.ticket = t,
      onCompletedLogin: () => global.onCompletedLogin(),
      getLoggedUser: () => global.getLoggedUser(),
      showLoginProgressDialog: (func) async {
        final r = await showDialogLoading<Result<String>>(func());
        if (r?.isError ?? false) {
          await showDialogErrorText(r!.error);
          return;
        }
      },
      app: user.app,
      ssoUseCase: ssoUseCase,
    ),
  );

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    provider.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(builder: (context, sizingInformation) {
      setResponsive(sizingInformation);

      return LoginPage(
        provider,
        onCreateAccount: () async {
          final either =
              await showDialogLoading(MunicipalityGetUseCaseView.execute(
            connection: connection,
            ssoUseCase: ssoUseCase,
            app: user.app,
            isInPolygon: (ll, p) =>
                positionUtilService.isInPolygon(position: ll, points: p),
          ));

          if (either == null) {
            // ignore: unawaited_futures
            showDialogInformationText(TTShared.haOcurridoUnError.tt);
            return;
          }

          if (either.isRight) {
            // ignore: unawaited_futures
            showDialogInformationText(either.right!);
            return;
          }

          // ignore: unawaited_futures
          NavigatorService().push(
            MunicipalitySelectionPage(
              MunicipalitySelectionProvider(either.left!),
            ),
          );
        },
        onRestorePassword: () {
          DialogService().showCustomDialog(UserAccountRetrieveDialog(), true);
        },
        appNameLogo:
            ImageCacheService().getFromAsset("assets/images/ecoreutiliza.svg"),
        loginWithEmail: true,
      );
    });
  }
}
