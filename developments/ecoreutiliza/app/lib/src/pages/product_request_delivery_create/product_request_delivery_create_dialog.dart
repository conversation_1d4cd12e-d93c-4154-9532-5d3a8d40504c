import '../../_imports.dart';

class ProductRequestDeliveryCreateDialog extends StatefulWidget {
  final ProductRequestDeliveryCreateProvider provider;

  ProductRequestDeliveryCreateDialog(this.provider);

  @override
  State<ProductRequestDeliveryCreateDialog> createState() =>
      _ProductRequestDeliveryCreateDialogState();
}

class _ProductRequestDeliveryCreateDialogState
    extends State<ProductRequestDeliveryCreateDialog> {
  @override
  void dispose() {
    widget.provider.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DialogER(
      child:
          ProductRequestDeliveryCreateScreen(widget.provider, EdgeInsets.zero),
      title: tt(TTProduct.solicitudDeEntregaDeReutilizable),
    );
  }
}
