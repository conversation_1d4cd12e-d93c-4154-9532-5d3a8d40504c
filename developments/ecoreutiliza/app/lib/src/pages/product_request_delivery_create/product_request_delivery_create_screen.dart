import '../../_imports.dart';

class ProductRequestDeliveryCreateScreen extends StatefulWidget {
  final ProductRequestDeliveryCreateProvider provider;
  final EdgeInsets padding;
  ProductRequestDeliveryCreateScreen(this.provider, this.padding);
  @override
  State<ProductRequestDeliveryCreateScreen> createState() =>
      _ProductRequestDeliveryCreateScreenState();
}

class _ProductRequestDeliveryCreateScreenState
    extends State<ProductRequestDeliveryCreateScreen> {
  ProductRequestDeliveryCreateProvider get provider => widget.provider;

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (provider.stepIndex == 0) return true;
        // ignore: unawaited_futures
        showDialogConfirmation(
          text: tt(TTShared.quieresCancelarLaOperacionPregunta),
        ).then((value) {
          if (value) NavigatorService().pop();
        });
        return false;
      },
      child: FutureBuilderLoading(
        start: provider.start(),
        builder: (c) => provider.rebuilding(
          (c) => Column(
            children: [
              Builder(
                builder: (_) => widget.provider.getCurrentStep(),
              ),
              SeparatorFiori(),
              SeparatorFiori.half(),
              Dots(widget.provider.totalSteps, provider.stepIndex,
                  provider.stepBack),
            ],
          ),
        ),
      ),
    );
  }
}
