import '../../_imports.dart';

class ProductRequestDeliveryCreateProvider extends ProviderArrowsCarouselER {
  List<Widget> _pages = [];

  ProductCategoryModel? selectedCategory;
  CleanPointBuildingModel? selectedCleanPoint;

  int stepIndex = 0;
  int get totalSteps => _pages.length;

  final nameField = TextEditingController();
  final descriptionField = TextEditingController();

  ProductRequestDeliveryCreateProvider.ProductDeliveryRequestCreateProvider() {
    _pages.addAll([
      ProductRequestDeliveryCreateStepCategory(this),
      ProductCreateDeliveryRequestStepCleanPointMap(this),
      ProductCreateDeliveryRequestStepCleanPointConfirmation(this),
      ProductCreateDeliveryRequestStepName(this),
      ProductCreateDeliveryRequestStepImage(this),
      ProductCreateDeliveryRequestStepConfirmation(this),
    ]);
  }

  Future<void> start() async {
    await productCategoryManager.start();
    await cleanPointBuildingManager.start();
    await zoneManager.start();
  }

  void stepBack(int i) {
    if (i < 0) return;
    if (i == stepIndex) return;
    if (i >= stepIndex) return;
    stepIndex = i;
    rebuild();
  }

  @override
  void internalDispose() {
    nameField.dispose();
    descriptionField.dispose();
  }

  Future<void> save() async {
    final result = await (showDialogLoading(
      productRequestUseCase.save(ProductRequestUseCaseValidator(
        ProductRequestModel(
          companyId: user.companyId!,
          description: descriptionField.text,
          name: nameField.text,
          imagesOrIdsMd5: images.distinct(),
          state: ProductRequestState.pending.id,
          cleanPointGuid: selectedCleanPoint!.guid,
          id: 0,
          guid: Guid().get(),
          modifyDate: DateTime.now(),
          creationDate: DateTime.now(),
          isRemoved: false,
          isSync: false,
          isHiddenByCitizen: false,
          isReadedByCitizen: false,
          isReadedByManager: false,
          managerComment: '',
          citizenGuid: global.citizen!.guid,
          categoryId: selectedCategory!.id,
        ),
        cleanPointBuildingUseCase,
        productRequestUseCase,
        user,
      )),
    ));

    if (result!.isError) {
      // ignore: unawaited_futures
      showDialogErrorText(result.error);
    } else {
      await showDialogInformationText(
          tt(TTProduct.reutilizableEnviadoNoLlevarHastaRecibirValidacion));
      NavigatorService().pop();
    }
  }

  Widget getCurrentStep() {
    return _pages[stepIndex];
  }

  void onCompletedStepCategory() {
    if (selectedCategory == null) return;
    final cps = getCleanPoints();

    if (isListNullOrEmpty(cps)) {
      DialogService()
          .showSnackbar(tt(TTShared.noPuntosLimpiosParaCategoriaSeleccionada));
      return;
    }

    stepIndex = 1;
    rebuild();
  }

  void onCompletedStepCleanPointMap() {
    if (selectedCleanPoint == null) return;
    stepIndex = 2;
    rebuild();
  }

  void onCompletedStepCleanPointConfirmation() {
    stepIndex = 3;
    rebuild();
  }

  void onCompletedStepName() {
    if (isStringNullOrEmpty(nameField.text)) {
      DialogService().showSnackbar(tt(TTShared.noSeHaIndicadoElNombre));
      return;
    }

    if (isStringNullOrEmpty(descriptionField.text)) {
      DialogService().showSnackbar(tt(TTShared.noSeHaIndicadoLaDescripcion));
      return;
    }

    final checkDesc = ProductModel.validateDescription(descriptionField.text);
    if (checkDesc.isError) {
      DialogService().showSnackbar(checkDesc.error!);
      return;
    }

    final checkName = ProductModel.validateDescription(nameField.text);
    if (checkName.isError) {
      DialogService().showSnackbar(checkName.error!);
      return;
    }

    stepIndex = 4;
    rebuild();
  }

  void onCompletedStepImage() {
    if (isListNullOrEmpty(images)) {
      DialogService().showSnackbar(tt(TTShared.noSeHaSeleccionadoImagen));
      return;
    }

    stepIndex = 5;
    rebuild();
  }

  void onCompletedStepConfirm() {
    save();
  }

  List<CleanPointBuildingModel>? getCleanPoints() {
    // Se debe seleccionar antes la categoría para poder filtrar.
    if (selectedCategory == null) return null;

    final result = cleanPointBuildingManager.models.where2((c) => c
        .categoriesAccepted
        .any((cc) => cc.categoryId == selectedCategory!.id));

    if (isListNullOrEmpty(result)) return result;

    if (connection.hasGps && (connection.currentLatLng?.isValid() ?? false))
      return result.sortByProximity(
        (m) => m.getZonePosition(zoneManager.modelsMap),
        //  m.schedules.isEmpty
        //   ? null
        //   : LatLong(m.schedules.first.lat, m.schedules.first.lng),
        connection.currentLatLng!,
        positionUtilService,
      );

    return result;
  }
}
