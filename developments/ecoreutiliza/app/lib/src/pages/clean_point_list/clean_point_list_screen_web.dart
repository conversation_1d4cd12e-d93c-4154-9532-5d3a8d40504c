import '../../_imports.dart';

class CleanPointListScreenWeb extends StatefulWidget {
  final CleanPointListProvider? provider;
  const CleanPointListScreenWeb(this.provider);

  @override
  State<CleanPointListScreenWeb> createState() =>
      _CleanPointListScreenWebState();
}

class _CleanPointListScreenWebState extends State<CleanPointListScreenWeb> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilderLoading(
      loading: Text(TTShared.sincronizacionDeDatosEnCurso.tt),
      start: widget.provider!.start(),
      builder: (_) => StreamBuilder(
        stream: widget.provider!.rebuild.stream,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          final cleanPoints = widget.provider!.getCleanPoints();

          Widget body = const SizedBox();

          if (cleanPoints.isEmpty) {
            if (!syncService.isCompletedOnceSynchronizable<
                Synchronizer<CleanPointBuildingModel, String>>())
              body = Center(child: LoadingContainer());
            else
              body = Center(child: H1Text(TTCleanPoint.puntoLimpio.tt));
          } else {
            body = CleanPointListGrid(widget.provider, cleanPoints!);
          }

          return Scaffold(
            backgroundColor: themeColors.greyBackground,
            // floatingActionButton: widget.provider!.getFloatingButtons(),
            body: Padding(
              padding: EdgeInsets.only(
                top: ThemeER.bodySeparation / 2,
                left: ThemeER.bodySeparation,
                right: ThemeER.bodySeparation,
              ),
              child: Column(
                children: [
                  Container(
                    height: 45,
                    width: double.maxFinite,
                    child: Row(
                      children: [
                        Expanded(
                          child: TextFormFieldER(
                            title: tt(TTShared.buscar),
                            controller:
                                widget.provider!.filter!.searchController,
                            icon: FontAwesomeIcons.magnifyingGlass,
                          ),
                        ),
                        const SizedBox(width: 10),
                        ButtonIconER(
                          colorIcon:
                              widget.provider!.filter!.isAppliedAnyFilter()
                                  ? themeColors.errorRed
                                  : ThemeER.iconColor,
                          color: themeColors.white,
                          icon: FontAwesomeIcons.filter,
                          onPressed: () {
                            Scaffold.of(context).openEndDrawer();
                          },
                          tooltip: tt(TTShared.filtrar),
                          colorSplash: ThemeER.iconColor,
                          sizeIcon: 20,
                        ),
                      ],
                    ),
                  ),
                  SeparatorFiori(),
                  Expanded(child: body)
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
