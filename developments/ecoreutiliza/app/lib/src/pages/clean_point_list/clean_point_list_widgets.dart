import '../../_imports.dart';

class CleanPointListGrid extends StatelessWidget {
  static ResponsiveData<int> _maxItems = ResponsiveData<int>(
    mobileDefault: 1,
    tabletLarge: 3,
    tabletDefault: 2,
    webSmall: 2,
    webNormal: 3,
    webLarge: 3,
    webExtraLarge: 4,
    defaultData: 4,
  );

  static ResponsiveData<double> _spacing = ResponsiveData<double>(
    mobileSmall: 2,
    mobileDefault: 5,
    defaultData: 12,
  );

  final CleanPointListProvider? provider;
  final List<CleanPointBuildingModel> cleanPoints;

  CleanPointListGrid(this.provider, this.cleanPoints);

  @override
  Widget build(BuildContext context) {
    Widget child = GridView.builder(
      // padding: EdgeInsets.only(top: ThemeER.bodySeparation / 2),
      controller: provider!.scrollControllerCleanPoints,
      physics: const BouncingScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _maxItems.get(),
        mainAxisSpacing: _spacing.get(),
        crossAxisSpacing: _spacing.get(),
        // width / height: fixed for *all* items
        childAspectRatio: 253 / 220,
      ),
      itemBuilder: (context, i) => CleanPointItemCard(cleanPoints[i]),
      itemCount: cleanPoints.length,
    );

    return ScrollConfiguration(
      behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
      child: child,
    );
  }
}

class CleanPointListAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  final CleanPointListProvider? provider;

  CleanPointListAppBar(this.provider);

  @override
  Widget build(BuildContext context) {
    List<Widget> additionalActions = [];

    return AppBarSearchHiden(
      onChanged: (v) => provider!.filter!.onChangedText(v),
      onCleared: () => provider!.filter!.onChangedText(""),
      isClearOnSubmit: false,
      isClosedOnSubmit: false,
      initialValue: provider!.filter!.searchController.text,
      defaultAppBar: (c, button) => AppBar(
        backgroundColor: themeColors.primaryBlue,
        titleSpacing: 5,
        title: Container(
          height: 60,
          width: 190,
          alignment: Alignment.centerLeft,
          child: Padding(
            padding: const EdgeInsets.only(top: 4),
            child: ImageCacheService().getFromAsset(
              "assets/images/ecoreutiliza_white.svg",
              fit: BoxFit.fitHeight,
            ),
          ),
        ),
        actions: [
          button,
          StreamBuilder(
              stream: provider!.rebuild.stream,
              builder: (_, __) => Container(
                    child: ActionButtonAppBar(
                      color: provider!.filter!.isAppliedAnyFilter()
                          ? themeColors.errorRed
                          : themeColors.white,
                      icon: FontAwesomeIcons.filter,
                      onPressed: () => Scaffold.of(context).openEndDrawer(),
                      tooltip: tt(TTShared.filtrar),
                    ),
                  )),
          ...additionalActions,
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(60);
}

class CleanPointItemCard extends StatelessWidget {
  static ResponsiveData<double> _iconSize = ResponsiveData<double>(
    mobileDefault: 30,
    tabletDefault: 30,
    defaultData: 30,
  );

  final CleanPointBuildingModel model;

  CleanPointItemCard(this.model);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        global.home!.openPageCleanPoint(model);
      },
      child: Container(
        child: Card(
          elevation: ThemeER.elevation,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ThemeER.radius)),
          child: Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    child: CleanPointImageWidget(
                      model,
                      child: Container(
                        padding: const EdgeInsets.only(bottom: 5, right: 5),
                        alignment: Alignment.bottomRight,
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [],
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.only(
                    left: 14,
                    top: 8,
                  ),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    model.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.left,
                    maxLines: 1,
                  ),
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class CleanPointImageWidget extends StatelessWidget {
  final CleanPointBuildingModel p;
  final Widget? child;

  CleanPointImageWidget(this.p, {this.child});

  @override
  Widget build(BuildContext context) {
    return CardER(
      padding: EdgeInsets.zero,
      margin: EdgeInsets.zero,
      child: Container(
        width: double.maxFinite,
        height: 300,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(ThemeER.radius),
          child: Stack(children: [
            Container(
              height: double.maxFinite,
              width: double.maxFinite,
              child: p.imageWidget,
            ),
            child!,
          ]),
        ),
      ),
    );
  }
}

class CleanPointItemListSmall extends StatelessWidget {
  final CleanPointBuildingModel m;
  final Function(CleanPointBuildingModel)? onTap;
  final bool showSchedule;

  CleanPointItemListSmall(
    this.m, {
    this.onTap,
    this.showSchedule = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget child = CardER(
      child: Container(
        height: showSchedule ? 120 : 80,
        alignment: Alignment.centerLeft,
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(ThemeER.radius),
              child: Container(
                height: double.maxFinite,
                width: 80,
                child: m.imageWidget,
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  H2Text(
                    m.name,
                    maxLines: 2,
                    isShowedEllipsis: true,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: LabelER(
                      title: m.getAddress(zoneManager.modelsMap),
                      icon: FontAwesomeIcons.locationDot,
                      maxLines: 1,
                    ),
                  )
                ],
              ),
            ),
            if (m.hasEcoReutiliza)
              Padding(
                padding: const EdgeInsets.only(right: 8),
                child: Tooltip(
                  message: 'Este punto limpio admite EcoReutiliza',
                  child: Icon(
                    Icons.recycling, // Puedes usar otro ícono si prefieres
                    color: themeColors.secondaryGreen,
                    size: 24,
                  ),
                ),
              )
          ],
        ),
      ),
    );

    if (showSchedule)
      child = Card(
        child: Column(
          children: [
            child,
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: CleanPointSchedules(m),
            ),
          ],
        ),
      );

    return GestureDetector(
      onTap: () {
        if (onTap != null) {
          onTap!(m);
        } else {
          global.home!.openPageCleanPoint(m);
        }
      },
      child: child,
    );
  }
}
