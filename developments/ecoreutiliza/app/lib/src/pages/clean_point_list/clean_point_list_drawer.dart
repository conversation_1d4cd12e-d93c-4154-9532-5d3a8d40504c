import '../../_imports.dart';

class CleanPointFilterDrawer extends StatefulWidget {
  final CleanPointListProvider? provider;

  CleanPointFilterDrawer(this.provider);

  @override
  State<CleanPointFilterDrawer> createState() => _CleanPointFilterDrawerState();
}

class _CleanPointFilterDrawerState extends State<CleanPointFilterDrawer> {
  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Container(
        child: SafeArea(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            color: themeColors.greyBackground,
            child: ScrollConfiguration(
              behavior:
                  ScrollConfiguration.of(context).copyWith(scrollbars: false),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 40),
                    Container(
                      child: But<PERSON><PERSON>(
                        title: tt(TTShared.restablecer),
                        color: themeColors.primaryBlue,
                        colorSplash: themeColors.primaryBlueLight,
                        colorText: themeColors.white,
                        onPressed: () {
                          widget.provider!.filter!.restart();
                          widget.provider!.rebuild();
                          global.home!.cleanPointMarkerAreaManager
                              .resetAllMarkers();
                          global.home!.cleanPointMarkerEntrancesManager
                              .resetAllMarkers();
                          setState(() {});
                        },
                      ),
                    ),

                    SeparatorFiori.half(),

                    /// Checkbox para el filtro de "Incluir Ecoreutiliza"
                    CheckboxListTile(
                      title: Text(tt(TTER.incluirEcoreutiliza)),
                      value: widget
                          .provider!.filter!.includeEcoReutiliza.isSelected,
                      onChanged: (bool? value) {
                        setState(() {
                          widget.provider!.filter!.includeEcoReutiliza
                              .isSelected = value ?? false;
                          widget.provider!
                              .rebuild(); // Actualiza el filtro y la lista
                          global.home!.cleanPointMarkerAreaManager
                              .resetAllMarkers();
                          global.home!.cleanPointMarkerEntrancesManager
                              .resetAllMarkers();
                        });
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                      activeColor: themeColors.primaryBlue,
                    ),

                    Container(
                      width: double.maxFinite,
                      margin: const EdgeInsets.only(left: 5),
                      child: ListTile(
                        onTap: () {
                          widget.provider!.filter!.favorites.isSelected =
                              !widget.provider!.filter!.favorites.isSelected;
                          setState(() {});
                          widget.provider!.rebuild();
                          global.home!.cleanPointMarkerAreaManager
                              .resetAllMarkers();
                          global.home!.cleanPointMarkerEntrancesManager
                              .resetAllMarkers();
                        },
                        leading: IgnorePointer(
                          child: Icon(
                            widget.provider!.filter!.favorites.isSelected
                                ? FontAwesomeIcons.solidHeart
                                : FontAwesomeIcons.heart,
                          ),
                        ),
                        title: Text(tt(TTER.favoritos)),
                      ),
                    ),
                    ExpansionTileCheck<ProductCategoryModel>(
                      isInitialiteExpanded: false,
                      title: tt(TTProduct.categorias),
                      subtitle: tt(TTER.seleccionados),
                      models: widget.provider!.filter!.categories,
                      builder: (m) {
                        return Text(m.name);
                      },
                      onChanged: () {
                        setState(() {});
                        widget.provider!.rebuild();
                        global.home!.cleanPointMarkerAreaManager
                            .resetAllMarkers();
                        global.home!.cleanPointMarkerEntrancesManager
                            .resetAllMarkers();
                      },
                    ),
                    ExpansionTileCheck<ScheduleDay>(
                      isInitialiteExpanded: false,
                      title: tt(TTCleanPoint.diasApertura),
                      subtitle: tt(TTER.seleccionados),
                      models: widget.provider!.filter!.daysOpen,
                      builder: (m) => Text(m.translation),
                      onChanged: () {
                        setState(() {});
                        widget.provider!.rebuild();
                        global.home!.cleanPointMarkerAreaManager
                            .resetAllMarkers();
                        global.home!.cleanPointMarkerEntrancesManager
                            .resetAllMarkers();
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
