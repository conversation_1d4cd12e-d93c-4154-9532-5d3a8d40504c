import '../../_imports.dart';

class CleanPointListProvider extends ProviderBaseV2 {
  bool isShowedGrid = true;

  CleanPointListFilter? get filter => _filter;
  CleanPointListFilter? _filter;
  final scrollControllerCleanPoints = ScrollController();
  bool isAscending = false;
  // Por defecto aparece ordenado por fecha de modificación.
  int currentColumnIndex = 2;

  CleanPointListProvider()
      : super(streams: [
          cleanPointBuildingManager.onStarted,
          cleanPointBuildingManager.onChange,
          SizeInformation.onChangedRefinedSize.stream,
        ]) {
    _filter = CleanPointListFilter(this);
  }

  @override
  void internalDispose() {
    // filter!.dispose();
    rebuild.close();
    scrollControllerCleanPoints.dispose();
  }

  List<CleanPointBuildingModel> getCleanPoints() {
    return cleanPointBuildingManager.models.where((p) {
      return filter!.isMatch(p);
    }).toList()
      ..sortByDateDesc((o) => o.creationDate);
  }

  void resortTable(int columnIndex, bool isCurrentAscending) {
    currentColumnIndex = columnIndex;
    isAscending = !isAscending;
    rebuild();
  }
}
