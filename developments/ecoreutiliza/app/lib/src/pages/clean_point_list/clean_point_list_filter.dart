import '../../_imports.dart';

/// Clase que contiene los filtros de puntos limpios.
class CleanPointListFilter {
  List<StreamSubscription> _subs = [];
  List<Selectable<ProductCategoryModel>> categories = [];
  List<Selectable<ScheduleDay>> daysOpen = [
    Selectable(ScheduleDay.monday, false),
    Selectable(ScheduleDay.tuesday, false),
    Selectable(ScheduleDay.wednesday, false),
    Selectable(ScheduleDay.thursday, false),
    Selectable(ScheduleDay.friday, false),
    Selectable(ScheduleDay.saturday, false),
    Selectable(ScheduleDay.sunday, false)
  ];

  Selectable<bool> favorites = Selectable(false, false);
  Selectable<bool> includeEcoReutiliza =
      Selectable(false, false); // Por defecto en true

  final searchController = TextEditingController();
  final _changeTextDelayed =
      DelayedRenewableOperationV2(delay: const Duration(milliseconds: 300));

  final CleanPointListProvider provider;

  CleanPointListFilter(this.provider) {
    _subs.add(cleanPointBuildingManager.onChange.listen((event) {
      _onOpened();
    }));
    searchController.addListener(() {
      _changeTextDelayed.execute(() => provider.rebuild());
    });
    _onOpened();
  }

  void restart() {
    _onOpened();
    searchController.text = "";
    favorites.isSelected = false;
    includeEcoReutiliza.isSelected = false; // Restablece el valor por defecto
    global.home!.cleanPointMarkerAreaManager.resetAllMarkers();
    global.home!.cleanPointMarkerEntrancesManager.resetAllMarkers();
    provider.rebuild();
  }

  void set(CleanPointListFilter filtro) {
    categories = filtro.categories;
    daysOpen = filtro.daysOpen;
    favorites = filtro.favorites;
    includeEcoReutiliza = filtro.includeEcoReutiliza;
    searchController.text = filtro.searchController.text;
    provider.rebuild();
  }

  bool isAppliedAnyFilter() {
    if (categories.exists((m) => m.isSelected)) return true;
    if (daysOpen.any((d) => d.isSelected)) return true;
    if (favorites.isSelected) return true;
    if (includeEcoReutiliza.isSelected) return true;
    return false;
  }

  bool isMatch(CleanPointBuildingModel p) {
    if ((isStringFilled(searchController.text) &&
            searchController.text.length > 2) &&
        !p.matchText.contains(searchController.text.toLowerCase()))
      return false;

    final selectedCategoryIds = categories
        .where((filter) => filter.isSelected)
        .map((filter) => filter.value.id)
        .toSet();

    if (selectedCategoryIds.isNotEmpty) {
      if (!p.categoriesAccepted.any(
          (category) => selectedCategoryIds.contains(category.categoryId))) {
        return false;
      }
    }

    // if (favorites.isSelected && !p.isFavorite) return false;

    if (includeEcoReutiliza.isSelected && !p.hasEcoReutiliza)
      return false; // Lógica del filtro

    if (daysOpen.any((d) => d.isSelected)) {
      Map<ScheduleDay, bool> daysOpenParsed =
          Map<ScheduleDay, bool>.fromEntries(
        daysOpen.map((d) => MapEntry(d.value, d.isSelected)),
      );
      if (!p.isOpenIn(daysOpenParsed)) return false;
    }

    return true;
  }

  void _onOpened() {
    categories.clear();
    productCategoryManager.models.forEach(
      (m) => categories.addNew(
        Selectable(m, false),
        (m1, m2) => m1.value.id == m2.value.id,
      ),
    );
    daysOpen.clear();
    daysOpen = [
      Selectable(ScheduleDay.monday, false),
      Selectable(ScheduleDay.tuesday, false),
      Selectable(ScheduleDay.wednesday, false),
      Selectable(ScheduleDay.thursday, false),
      Selectable(ScheduleDay.friday, false),
      Selectable(ScheduleDay.saturday, false),
      Selectable(ScheduleDay.sunday, false)
    ];
  }

  void dispose() {
    _subs.forEach((s) => s.cancel());
  }

  /// Se llama desde mobile cuando modifca el texto en el AppBar.
  void onChangedText(String v) {
    searchController.text = v;
    global.home!.cleanPointMarkerAreaManager.resetAllMarkers();
    global.home!.cleanPointMarkerEntrancesManager.resetAllMarkers();
  }

  CleanPointListFilter copy() {
    CleanPointListFilter filter =
        CleanPointListFilter(CleanPointListProvider());

    // Clonar listas de categorías y días abiertos (evitar referencia compartida)
    filter.categories =
        categories.map((c) => Selectable(c.value, c.isSelected)).toList();
    filter.daysOpen =
        daysOpen.map((d) => Selectable(d.value, d.isSelected)).toList();

    // Clonar Selectable individuales
    filter.favorites = Selectable(favorites.value, favorites.isSelected);
    filter.includeEcoReutiliza =
        Selectable(includeEcoReutiliza.value, includeEcoReutiliza.isSelected);

    // Copiar el texto del controlador de búsqueda
    filter.searchController.text = searchController.text;

    return filter;
  }
}
