import '../../_imports.dart';

class CleanPointListPage extends StatelessWidget {
  final CleanPointListProvider? provider;

  CleanPointListPage(this.provider);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: HomeDrawer(),
      endDrawer: CleanPointFilterDrawer(provider),
      appBar: CleanPointListAppBar(provider),
      body: CleanPointListScreenMobile(provider),
    );
  }
}
