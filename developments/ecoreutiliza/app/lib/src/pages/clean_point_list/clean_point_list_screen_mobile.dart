import '../../_imports.dart';

class CleanPointListScreenMobile extends StatefulWidget {
  final CleanPointListProvider? provider;
  final bool padding;
  final Function(CleanPointBuildingModel)? onTap;
  final bool showSchedule;

  CleanPointListScreenMobile(
    this.provider, {
    this.padding = false,
    this.showSchedule = false,
    this.onTap,
  });

  @override
  State<CleanPointListScreenMobile> createState() =>
      _CleanPointListScreenMobileState();
}

class _CleanPointListScreenMobileState
    extends State<CleanPointListScreenMobile> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: StreamBuilder(
          stream: widget.provider!.rebuild.stream,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            final cleanPoints = widget.provider!.getCleanPoints();

            Widget body = const SizedBox();

            if (cleanPoints.isEmpty) {
              if (!syncService.isCompletedOnceSynchronizable<
                  Synchronizer<CleanPointBuildingModel, String>>())
                body = const LoadingBodyER();
              else
                body = Center(child: H1Text(TTCleanPoint.puntoLimpio.tt));
            } else {
              body = ListView.builder(
                  itemCount: cleanPoints!.length,
                  padding: EdgeInsets.only(
                    top: widget.padding ? global.initialTopPadding : 0,
                    left: 5,
                    right: 5,
                  ),
                  itemBuilder: (_, i) => CleanPointItemListSmall(
                        cleanPoints[i],
                        onTap: widget.onTap,
                        showSchedule: widget.showSchedule,
                      ));
            }
            return Scaffold(
              // floatingActionButton: widget.provider!.getFloatingButtons(),
              backgroundColor: themeColors.greyBackground,
              body: body,
            );
          }),
    );
  }
}
