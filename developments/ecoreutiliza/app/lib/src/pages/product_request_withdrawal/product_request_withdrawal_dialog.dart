import '../../_imports.dart';

class ProductRequestWithdrawalDialog extends StatefulWidget {
  final ProductRequestWithdrawalProvider provider;

  ProductRequestWithdrawalDialog(this.provider);

  @override
  State<ProductRequestWithdrawalDialog> createState() =>
      _ProductRequestWithdrawalDialogState();
}

class _ProductRequestWithdrawalDialogState
    extends State<ProductRequestWithdrawalDialog> {
  @override
  void dispose() {
    widget.provider.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DialogER(
      child: ProductRequestWithdrawalScreen(widget.provider, EdgeInsets.zero),
      title: tt(TTProduct.solicitudDeNuevoReutilizable),
    );
  }
}
