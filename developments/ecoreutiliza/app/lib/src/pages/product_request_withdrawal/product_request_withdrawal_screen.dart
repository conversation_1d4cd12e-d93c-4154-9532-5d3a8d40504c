import '../../_imports.dart';

class ProductRequestWithdrawalScreen extends StatefulWidget {
  final ProductRequestWithdrawalProvider provider;
  final EdgeInsets padding;
  ProductRequestWithdrawalScreen(this.provider, this.padding);
  @override
  State<ProductRequestWithdrawalScreen> createState() =>
      _ProductRequestWithdrawalScreenState();
}

class _ProductRequestWithdrawalScreenState
    extends State<ProductRequestWithdrawalScreen> {
  ProductRequestWithdrawalProvider get provider => widget.provider;

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return provider.rebuilding((_) {
      provider.refreshModel();
      final c = provider.model.getCategory(productCategoryManager.modelsMap);
      final cp = provider.model
          .getCleanPointBuilding(cleanPointBuildingManager.modelsMap);
      return SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            FutureBuilder<List<String>>(
              future: provider.getImages(),
              builder: (BuildContext context, AsyncSnapshot snapshot) {
                if (isListNullOrEmpty(snapshot.data)) return const SizedBox();
                return Column(children: [
                  CarouselSlider(
                    options: CarouselOptions(
                      onPageChanged: (i, _) => provider.onChangedIndex(i),
                      autoPlay: false,
                      enlargeCenterPage: true,
                      enableInfiniteScroll: false,
                      initialPage: 0,
                      height: 250,
                      viewportFraction: 0.6,
                    ),
                    // ignore: avoid_dynamic_calls
                    items: snapshot.data
                        .map<Widget>(
                          (v) => FadeInContainer(
                            child: GestureDetector(
                              onTap: () => showDialogImage(v),
                              child: Container(
                                constraints:
                                    const BoxConstraints(maxHeight: 300),
                                alignment: Alignment.center,
                                child: CardER(
                                  padding: EdgeInsets.all(ThemeER.imagePadding),
                                  margin: EdgeInsets.zero,
                                  child: Container(
                                    child: Stack(
                                      children: [
                                        ClipRRect(
                                          borderRadius: BorderRadius.circular(
                                              ThemeER.radius - 3),
                                          child: ImageERAsync(() async {
                                            return v;
                                          }),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  ),
                  CarouselDots(
                    images: snapshot.data,
                    index: provider.carouselIndex,
                  ),
                ]);
              },
            ),
            SeparatorFiori(),
            CardER(
                margin: EdgeInsets.zero,
                child: Container(
                  padding: const EdgeInsets.all(10),
                  width: double.maxFinite,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LabelIconER(
                        text: provider.model.stateEnum.tt,
                        icon: Icon(
                          provider.model.stateEnum.icon,
                          color: provider.model.stateEnum.iconColor ??
                              ThemeER.iconColor,
                        ),
                        tooltip: TTShared.estado.tt,
                      ),
                      Visibility(
                        visible: provider.model.stateEnum ==
                            ProductRequestState.accepted,
                        child: Column(children: [
                          SeparatorFiori.half(),
                          CalloutContainer.valid(
                            subtitle: TTProduct
                                .puedesRecogerElReutilizableDelPuntoLimpio.tt,
                          ),
                        ]),
                      ),
                      Visibility(
                        visible: provider.model.stateEnum ==
                            ProductRequestState.pending,
                        child: Column(children: [
                          SeparatorFiori.half(),
                          CalloutContainer.alert(
                            subtitle: TTProduct
                                .noSePuedeRetirarElReutilizableDelPuntoLimpioHastaRecibirLaNotificacionDeConfirmacion
                                .tt,
                          ),
                        ]),
                      ),
                      Visibility(
                        visible: provider.model.managerComment.isNotEmpty,
                        child: Column(children: [
                          SeparatorFiori.half(),
                          LabelIconER(
                            text: provider.model.managerComment,
                            icon: Icon(
                              FontAwesomeIcons.solidComment,
                              color: ThemeER.iconColor,
                            ),
                            tooltip: TTProduct.comentarioDelGestor.tt,
                          ),
                        ]),
                      ),
                      SeparatorFiori.half(),
                      const Divider(),
                      SeparatorFiori.half(),
                      LabelIconER(
                        text: provider.model.name,
                        icon: Icon(FontAwesomeIcons.box,
                            color: ThemeER.iconColor),
                        tooltip: TTShared.nombre.tt,
                      ),
                      SeparatorFiori.half(),
                      LabelIconER(
                        text: provider.model.description,
                        icon: Icon(FontAwesomeIcons.quoteLeft,
                            color: ThemeER.iconColor),
                        tooltip: TTShared.descripcion.tt,
                      ),
                      if (c != null)
                        Column(children: [
                          SeparatorFiori.half(),
                          LabelIconER(
                            text: c.type.tt,
                            icon: c.iconWidget,
                            tooltip: TTProduct.categoria.tt,
                          ),
                        ]),
                      if (cp != null)
                        Column(children: [
                          SeparatorFiori.half(),
                          TooltipCustom(
                            message: tt(TTER.puntoLimpio),
                            child: LabelIconER(
                              text: cp.name,
                              icon: Icon(FontAwesomeIcons.locationDot,
                                  color: ThemeER.iconColor),
                              tooltip: TTShared.puntoLimpio.tt,
                              end: Row(
                                children: [
                                  ButtonIconER(
                                    tooltip: tt(TTShared.comoLlegar),
                                    color: themeColors.grey1,
                                    colorIcon: themeColors.black,
                                    colorSplash: themeColors.white,
                                    icon: FontAwesomeIcons.route,
                                    onPressed: () => provider
                                        .onPressedCleanPointHowToArrive(cp),
                                    size: 35,
                                  ),
                                  const SizedBox(width: 10),
                                  ButtonIconER(
                                    tooltip: tt(TTShared.informacion),
                                    color: themeColors.grey1,
                                    colorIcon: themeColors.black,
                                    colorSplash: themeColors.white,
                                    icon: FontAwesomeIcons.circleInfo,
                                    onPressed: () =>
                                        provider.onPressedCleanPointInfo(cp),
                                    size: 35,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ]),
                    ],
                  ),
                )),
            ..._buttons(cp),
          ],
        ),
      );
    });
  }

  List<Widget> _buttons(CleanPointBuildingModel? cp) {
    List<Widget> widgets = [];

    widgets.addAll([
      SeparatorFiori.half(),
      if (cp != null) ...[
        ButtonER.styleWhite(
          title: tt(TTShared.verEnMapa),
          icon: FontAwesomeIcons.mapLocation,
          onPressed: () => globalMenu.goToLocalMap(cp),
        ),
        SeparatorFiori.half()
      ],
      ButtonER.styleWhite(
        onPressed: () {
          DialogService().showCustomDialog(
            DialogER(
              child: DialogQRWidget(
                name: provider.model.name,
                data: provider.model.buildQR(),
                imageService: imageService,
              ),
            ),
            true,
          );
        },
        title: tt(TTShared.qr),
        icon: FontAwesomeIcons.qrcode,
      ),
    ]);

    if (!provider.model.isHiddenByCitizen &&
        (provider.model.stateEnum == ProductRequestState.accepted ||
            provider.model.stateEnum == ProductRequestState.rejected)) {
      widgets.addAll([
        SeparatorFiori(),
        ButtonER.styleBlue(
          onPressed: provider.onPressedHide,
          title: tt(TTShared.ocultar),
          icon: FontAwesomeIcons.eyeSlash,
        )
      ]);
    }
    return widgets;
  }
}
