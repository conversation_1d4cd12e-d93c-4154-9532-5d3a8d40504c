import '../../_imports.dart';

class ProductRequestWithdrawalCreateScreen extends StatefulWidget {
  final ProductRequestWithdrawalCreateProvider provider;
  final EdgeInsets padding;
  ProductRequestWithdrawalCreateScreen(this.provider, this.padding);
  @override
  State<ProductRequestWithdrawalCreateScreen> createState() =>
      _ProductRequestWithdrawalCreateScreenState();
}

class _ProductRequestWithdrawalCreateScreenState
    extends State<ProductRequestWithdrawalCreateScreen> {
  ProductRequestWithdrawalCreateProvider get provider => widget.provider;

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (provider.stepIndex == 0) return true;
        // ignore: unawaited_futures
        showDialogConfirmation(
          text: tt(TTShared.quieresCancelarLaOperacionPregunta),
        ).then((value) {
          if (value) NavigatorService().pop();
        });
        return false;
      },
      child: FutureBuilderLoading(
        start: provider.start(),
        builder: (c) => provider.rebuilding(
          (c) => Column(
            children: [
              Builder(
                builder: (_) => widget.provider.getCurrentStep(),
              ),
              SeparatorFiori(),
              SeparatorFiori.half(),
              Dots(widget.provider.totalSteps, provider.stepIndex,
                  provider.stepBack),
            ],
          ),
        ),
      ),
    );
  }
}
