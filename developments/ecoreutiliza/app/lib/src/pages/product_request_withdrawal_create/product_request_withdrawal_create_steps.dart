import '../../_imports.dart';

class ProductRequestWithdrawalCreateStepCategory extends StatelessWidget {
  final ProductRequestWithdrawalCreateProvider provider;

  ProductRequestWithdrawalCreateStepCategory(this.provider);
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SubtitleSectionFiori(tt(TTER.seleccionaUnaCategoria)),
        SeparatorFiori(),
        Container(
          width: double.maxFinite,
          child: DialogERListBody(
            builder: (i) => productCategoryManager.models
                .where((c) => c.name.toLowerCase().contains(i.toLowerCase()))
                .map(
                  (c) => Container(
                    margin: const EdgeInsets.only(bottom: 10),
                    child: GestureDetector(
                      onTap: () {
                        provider.selectedCategory = c;
                        provider.onCompletedStepCategory();
                      },
                      child: ProductCategorySearchItemList(c),
                    ),
                  ),
                )
                .toList(),
          ),
        ),
      ],
    );
  }
}

class ProductCreateRequestWithdrawalStepCleanPointMap extends StatelessWidget {
  final ProductRequestWithdrawalCreateProvider provider;

  ProductCreateRequestWithdrawalStepCleanPointMap(this.provider);
  @override
  Widget build(BuildContext context) {
    final mapBloc = provider.getMapBloc()!;
    return ColumnER(
      children: [
        Row(
          children: [
            Expanded(
                child: SubtitleSectionFiori(tt(TTER.seleccionaUnPuntoLimpio))),
            Container(
              width: 200,
              child: ButtonER.styleWhite(
                onPressed: () {
                  showDialogListCleanPoint(provider.getCleanPoints())
                      .then((value) {
                    if (value != null) {
                      provider.selectedCleanPoint = value;
                      provider.onCompletedStepCleanPointMap();
                    }
                  });
                },
                title: TTShared.listado.tt,
                icon: FontAwesomeIcons.list,
              ),
            ),
          ],
        ),
        SeparatorFiori(),
        Container(
          width: double.maxFinite,
          constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height - 280),
          child: Stack(
            children: [
              CardER(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.zero,
                child: HereMap(bloc: mapBloc),
              ),
              HereMapInterfaceWidget(bloc: mapBloc),
            ],
          ),
        ),
      ],
    );
  }
}

class ProductCreateRequestWithdrawalStepCleanPointConfirmation
    extends StatelessWidget {
  final ProductRequestWithdrawalCreateProvider provider;

  ProductCreateRequestWithdrawalStepCleanPointConfirmation(this.provider);
  @override
  Widget build(BuildContext context) {
    final model = provider.selectedCleanPoint!;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SubtitleSectionFiori(tt(TTCleanPoint.puntoLimpioSeleccionado)),
        SeparatorFiori(),
        TooltipCustom(
          message: tt(TTShared.nombre),
          child: LabelIconER(
            icon: const Icon(FontAwesomeIcons.recycle),
            text: model.name,
            textPaddingTop: 0,
          ),
        ),
        SeparatorFiori.half(),
        TooltipCustom(
          message: tt(TTShared.direccion),
          child: LabelIconER(
            icon: const Icon(FontAwesomeIcons.locationDot),
            text: model.getAddress(zoneManager.modelsMap),
            textPaddingTop: 0,
          ),
        ),
        SeparatorFiori.half(),
        const Divider(),
        CleanPointSchedules(model),
        SeparatorFiori(),
        SeparatorFiori.half(),
        Container(
          width: double.maxFinite,
          alignment: Alignment.centerRight,
          child: Container(
            width: ThemeER.dialogConfirmButtonWidth.get(),
            child: ButtonER(
              title: tt(TTShared.continuar),
              color: themeColors.secondaryGreen,
              colorSplash: themeColors.white,
              colorText: themeColors.white,
              icon: FontAwesomeIcons.circleArrowRight,
              onPressed: () => provider.onCompletedStepCleanPointConfirmation(),
            ),
          ),
        ),
      ],
    );
  }
}

class ProductCreateRequestWithdrawalStepName extends StatelessWidget {
  final ProductRequestWithdrawalCreateProvider provider;

  ProductCreateRequestWithdrawalStepName(this.provider);
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          SubtitleSectionFiori(
              tt(TTProduct.introduceElNombreYDescripcionDelReutilizable)),
          SeparatorFiori(),
          TextFormFieldER(
            title: tt(TTShared.nombre),
            controller: provider.nameField,
            icon: FontAwesomeIcons.ellipsis,
            hintText: tt(TTProduct.reutilizableCreacionNombreEjemplo),
          ),
          SeparatorFiori(),
          TextFormFieldER(
            title: tt(TTShared.descripcion),
            controller: provider.descriptionField,
            icon: FontAwesomeIcons.quoteLeft,
            isTextArea: true,
            hintText: tt(TTProduct.reutilizableCreacionDescripcionEjemplo),
          ),
          SeparatorFiori(),
          SeparatorFiori.half(),
          Container(
            width: double.maxFinite,
            alignment: Alignment.centerRight,
            child: Container(
              width: ThemeER.dialogConfirmButtonWidth.get(),
              child: ButtonER(
                title: tt(TTShared.continuar),
                color: themeColors.secondaryGreen,
                colorSplash: themeColors.white,
                colorText: themeColors.white,
                icon: FontAwesomeIcons.circleArrowRight,
                onPressed: provider.onCompletedStepName,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ProductCreateRequestWithdrawalStepImage extends StatelessWidget {
  final ProductRequestWithdrawalCreateProvider provider;

  ProductCreateRequestWithdrawalStepImage(this.provider);
  @override
  Widget build(BuildContext context) {
    return provider.rebuilding(
      (_) => Column(
        children: [
          SubtitleSectionFiori(tt(TTShared.seleccionaUnaImagen)),
          SeparatorFiori(),
          Container(
            width: double.maxFinite,
            constraints: const BoxConstraints(maxHeight: 600),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CarouselSlider(
                  options: CarouselOptions(
                    onPageChanged: (i, _) => provider.onChangedIndex(i),
                    autoPlay: false,
                    enlargeCenterPage: true,
                    enableInfiniteScroll: false,
                    initialPage: 0,
                    height: 250,
                    viewportFraction: 0.8,
                  ),
                  items: provider.getImageWidget(),
                ),
                ChangeImageOrderArrows(
                  images: provider.images,
                  onChangeOrderImages: provider.onChangeOrderImages,
                  index: provider.carouselIndex,
                ),
                SeparatorFiori(),
                ButtonER.styleWhite(
                  onPressed: provider.images.length == 3
                      ? null
                      : () {
                          WidgetUtil.getImageBase64Multiplatform(imageService)
                              .then((value) =>
                                  provider.onImageChanged(value, -1));
                        },
                  title:
                      "${TTShared.aniadirImagen.tt} (${provider.images.length}/3)",
                  icon: FontAwesomeIcons.camera,
                ),
                SeparatorFiori(),
                Container(
                  width: double.maxFinite,
                  alignment: Alignment.centerRight,
                  child: Container(
                    width: ThemeER.dialogConfirmButtonWidth.get(),
                    child: ButtonER(
                      title: tt(TTShared.continuar),
                      color: themeColors.secondaryGreen,
                      colorSplash: themeColors.white,
                      colorText: themeColors.white,
                      icon: FontAwesomeIcons.circleArrowRight,
                      onPressed: provider.onCompletedStepImage,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ProductCreateRequestWithdrawalStepConfirmation extends StatelessWidget {
  final ProductRequestWithdrawalCreateProvider provider;

  ProductCreateRequestWithdrawalStepConfirmation(this.provider);
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CalloutContainerER.alert(
          tt(TTProduct
              .noLlevesElReutilizableAlPuntoLimpioHastaRecibirLaNotificacionDeConfirmacion),
        ),
        SeparatorFiori(),
        Container(
          height: 300,
          child: CardER(
            child: Image.asset("assets/images/notification.jpg"),
            isFlat: true,
          ),
        ),
        SeparatorFiori(),
        SeparatorFiori.half(),
        Container(
          width: double.maxFinite,
          alignment: Alignment.centerRight,
          child: Container(
            width: ThemeER.dialogConfirmButtonWidth.get(),
            child: ButtonER(
              title: tt(TTShared.finalizar),
              color: themeColors.primaryBlue,
              colorSplash: themeColors.white,
              colorText: themeColors.white,
              icon: FontAwesomeIcons.circleCheck,
              onPressed: provider.onCompletedStepConfirm,
            ),
          ),
        ),
      ],
    );
  }
}
