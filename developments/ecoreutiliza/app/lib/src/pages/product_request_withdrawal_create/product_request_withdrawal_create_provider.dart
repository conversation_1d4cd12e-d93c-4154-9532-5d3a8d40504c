import '../../_imports.dart';

class ProductRequestWithdrawalCreateProvider extends ProviderArrowsCarouselER {
  List<Widget> _pages = [];

  ProductCategoryModel? selectedCategory;
  CleanPointBuildingModel? selectedCleanPoint;
  MapBlocBasic? _mapBloc;

  int stepIndex = 0;
  int get totalSteps => _pages.length;

  final nameField = TextEditingController();
  final descriptionField = TextEditingController();

  ProductRequestWithdrawalCreateProvider() {
    _pages.addAll([
      ProductRequestWithdrawalCreateStepCategory(this),
      ProductCreateRequestWithdrawalStepCleanPointMap(this),
      ProductCreateRequestWithdrawalStepCleanPointConfirmation(this),
      ProductCreateRequestWithdrawalStepName(this),
      ProductCreateRequestWithdrawalStepImage(this),
      ProductCreateRequestWithdrawalStepConfirmation(this),
    ]);
  }

  Future<void> start() async {
    await productCategoryManager.start();
    await cleanPointBuildingManager.start();
    await zoneManager.start();
  }

  void stepBack(int i) {
    if (i < 0) return;
    if (i == stepIndex) return;
    if (i >= stepIndex) return;
    stepIndex = i;
    rebuild();
  }

  @override
  void internalDispose() {
    nameField.dispose();
    descriptionField.dispose();
    _mapBloc?.dispose();
  }

  Future<void> save() async {
    final result = await (showDialogLoading(
      productWithdrawalRequestUseCase
          .save(ProductWithdrawalRequestUseCaseValidator(
        ProductWithdrawalRequestModel(
          companyId: user.companyId!,
          description: descriptionField.text,
          name: nameField.text,
          imagesOrIdsMd5: images.distinct(),
          state: ProductRequestState.pending.id,
          cleanPointGuid: selectedCleanPoint!.guid,
          id: 0,
          guid: Guid().get(),
          modifyDate: DateTime.now(),
          creationDate: DateTime.now(),
          isRemoved: false,
          isSync: false,
          isHiddenByCitizen: false,
          isReadedByCitizen: false,
          isReadedByManager: false,
          managerComment: '',
          citizenGuid: global.citizen!.guid,
          categoryId: selectedCategory!.id,
        ),
        cleanPointBuildingUseCase,
        productWithdrawalRequestUseCase,
        user,
      )),
    ));

    if (result!.isError) {
      // ignore: unawaited_futures
      showDialogErrorText(result.error);
    } else {
      await showDialogInformationText(
          tt(TTProduct.reutilizableEnviadoNoLlevarHastaRecibirValidacion));
      NavigatorService().pop();
    }
  }

  Widget getCurrentStep() {
    return _pages[stepIndex];
  }

  void onCompletedStepCategory() {
    if (selectedCategory == null) return;
    final cps = getCleanPoints();

    if (isListNullOrEmpty(cps)) {
      DialogService()
          .showSnackbar(tt(TTShared.noPuntosLimpiosParaCategoriaSeleccionada));
      return;
    }

    stepIndex = 1;
    rebuild();
  }

  void onCompletedStepCleanPointMap() {
    if (selectedCleanPoint == null) return;
    stepIndex = 2;
    rebuild();
  }

  void onCompletedStepCleanPointConfirmation() {
    stepIndex = 3;
    rebuild();
  }

  void onCompletedStepName() {
    if (isStringNullOrEmpty(nameField.text)) {
      DialogService().showSnackbar(tt(TTShared.noSeHaIndicadoElNombre));
      return;
    }

    if (isStringNullOrEmpty(descriptionField.text)) {
      DialogService().showSnackbar(tt(TTShared.noSeHaIndicadoLaDescripcion));
      return;
    }

    final checkDesc = ProductModel.validateDescription(descriptionField.text);
    if (checkDesc.isError) {
      DialogService().showSnackbar(checkDesc.error!);
      return;
    }

    final checkName = ProductModel.validateDescription(nameField.text);
    if (checkName.isError) {
      DialogService().showSnackbar(checkName.error!);
      return;
    }

    stepIndex = 4;
    rebuild();
  }

  void onCompletedStepImage() {
    if (isListNullOrEmpty(images)) {
      DialogService().showSnackbar(tt(TTShared.noSeHaSeleccionadoImagen));
      return;
    }

    stepIndex = 5;
    rebuild();
  }

  void onCompletedStepConfirm() {
    save();
  }

  MapBlocBase? getMapBloc() {
    if (_mapBloc == null) {
      // Al pasar al paso de puntos limpios, ya garantizamos que haya al menos 1 que admita la categoría.
      final cps = getCleanPoints()!;

      final _areaPolygonPlugin = AreaPolygonPlugin(
        positionUtilService,
        (p) => _onAreaOrPolygonPressed(p?.id),
      );
      final _areaCirclePlugin = AreaCirclePlugin(
        positionUtilService,
        (p) => _onAreaOrPolygonPressed(p?.id),
      );

      // Usamos el primero que tenga zona con lat de entrada
      LatLng? startLatLng;

      for (final m in cps) {
        final marker = m.getMarkerArea(zoneManager.models);
        if (marker == null) continue;

        if (marker is MarkerCircleCustom) {
          startLatLng ??= marker.latLng;
          _areaCirclePlugin.add(marker);
        } else if (marker is MarkerPolygonCustom) {
          startLatLng ??= marker.latLng;
          _areaPolygonPlugin.add(marker);
        }
      }

      _mapBloc = MapBlocBasic(
        connection,
        mapCenterData: startLatLng == null
            ? null
            : MapCenterData.withElementZoom(startLatLng),
        additionalPlugins: [
          _areaPolygonPlugin,
          _areaCirclePlugin,
        ],
        getBoundLimit: _getMapBoundLimit,
        languageType: TranslationServiceGlobal().language,
      );
    }

    //Si no hay puntos limpios no se intentan colocar en el mapa
    // if (!isListNullOrEmpty(cps.first.schedules)) {
    //   Future.delayed(Duration(milliseconds: 200), () {
    //     final schedule = cps.first.schedules.getFirstWithPosition();
    //     if (schedule == null) return;
    //     _mapBloc!.centerMapStream.sink(
    //       MapCenterData.withCurrentZoom(
    //         LatLng(schedule!.lat, schedule.lng),
    //       ),
    //     );
    //   });

    //   _mapBloc!.markerPlugin.clear();

    //   final List<CleanPointBuildingModel> pointsWithSchedule =
    //       cps.where2((s) => !isListNullOrEmpty(s.schedules));

    //   pointsWithSchedule.forEach2(
    //     (e) => _mapBloc!.markerPlugin.addAll(
    //       e.getMarker(
    //         () {
    //           selectedCleanPoint = e;
    //           onCompletedStepCleanPointMap();
    //         },
    //       ),
    //     ),
    //   );
    // }

    return _mapBloc;
  }

  void _onAreaOrPolygonPressed(String? idView) {
    if (isStringNullOrEmpty(idView)) return;

    final cleanPoint = cleanPointBuildingManager.models.firstWhere2(
      (e) => e.idView == idView,
    );
    if (cleanPoint == null) return;
    selectedCleanPoint = cleanPoint;
    onCompletedStepCleanPointMap();
  }

  Future<MapBoundLimit?> _getMapBoundLimit() async {
    final u = await getMapBoundLimit(connection, syncService);
    if (u == null) return null;
    return MapBoundLimit(
      northEast: u.object1,
      southWest: u.object2,
      positionUtilService: positionUtilService,
    );
  }

  List<CleanPointBuildingModel>? getCleanPoints() {
    // Se debe seleccionar antes la categoría para poder filtrar.
    if (selectedCategory == null) return null;

    final result = cleanPointBuildingManager.models.where2((c) => c
        .categoriesAccepted
        .any((cc) => cc.categoryId == selectedCategory!.id));

    if (isListNullOrEmpty(result)) return result;

    if (connection.hasGps && (connection.currentLatLng?.isValid() ?? false))
      return result.sortByProximity(
        (m) => m.getZonePosition(zoneManager.modelsMap),
        //  m.schedules.isEmpty
        //   ? null
        //   : LatLong(m.schedules.first.lat, m.schedules.first.lng),
        connection.currentLatLng!,
        positionUtilService,
      );

    return result;
  }
}
