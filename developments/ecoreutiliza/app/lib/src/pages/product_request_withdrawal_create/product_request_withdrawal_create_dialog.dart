import '../../_imports.dart';

class ProductRequestWithdrawalCreateDialog extends StatefulWidget {
  final ProductRequestWithdrawalCreateProvider provider;

  ProductRequestWithdrawalCreateDialog(this.provider);

  @override
  State<ProductRequestWithdrawalCreateDialog> createState() =>
      _ProductRequestWithdrawalCreateDialogState();
}

class _ProductRequestWithdrawalCreateDialogState
    extends State<ProductRequestWithdrawalCreateDialog> {
  @override
  void dispose() {
    widget.provider.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DialogER(
      child: ProductRequestWithdrawalCreateScreen(
          widget.provider, EdgeInsets.zero),
      title: tt(TTProduct.solicitudDeEntregaDeReutilizable),
    );
  }
}
