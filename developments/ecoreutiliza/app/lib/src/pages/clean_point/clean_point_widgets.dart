import 'package:flutter/cupertino.dart';

import '../../_imports.dart';

class CleanPointData extends StatelessWidget {
  final CleanPointBuildingModel model;
  late CleanPointProvider provider;

  CleanPointData(this.model, this.provider);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        FutureBuilder(
          future: provider.startImages(),
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            if (snapshot.connectionState != ConnectionState.done)
              return Column(
                children: [
                  LoadingContainer(),
                  SeparatorFiori(),
                ],
              );

            return Column(
              children: [
                CarouselSlider(
                    options: CarouselOptions(
                      onPageChanged: (i, _) => provider.onChangedIndex(i),
                      autoPlay: false,
                      enlargeCenterPage: true,
                      enableInfiniteScroll: false,
                      initialPage: 0,
                      height: 250,
                      viewportFraction: 0.8,
                    ),
                    items: (provider.model.hasImage)
                        ? provider.getImageWidget(isEditable: false)
                        : [
                            Icon(
                              Icons.image_not_supported,
                              size: 100,
                              color: themeColors.grey1,
                            ),
                          ]),
                provider.rebuilding((_) {
                  return CarouselDots(
                    images: provider.images,
                    index: provider.carouselIndex,
                  );
                }),
                SeparatorFiori(),
              ],
            );
          },
        ),
        CardER(
          margin: EdgeInsets.zero,
          child: Container(
            padding: const EdgeInsets.all(10),
            width: double.maxFinite,
            child: BodyText(
              model.name,
              isBold: true,
              textAlign: TextAlign.left,
              maxLines: 1,
            ),
          ),
        ),
        SeparatorFiori.half(),
        CardER(
          margin: EdgeInsets.zero,
          child: Container(
            padding: const EdgeInsets.all(10),
            width: double.maxFinite,
            child: Row(
              children: [
                Icon(
                  Icons.location_on,
                  color: themeColors.primaryBlue,
                ),
                const SizedBox(width: 10),
                Flexible(
                  child: BodyText(
                    model.getAddress(zoneManager.modelsMap),
                    maxLines: 3,
                    textAlign: TextAlign.left,
                  ),
                ),
              ],
            ),
          ),
        ),
        SeparatorFiori.half(),
        CardER(margin: EdgeInsets.zero, child: CleanPointSchedules(model)),
        SeparatorFiori.half(),
      ],
    );
  }
}

class CleanPointSchedules extends StatelessWidget {
  final CleanPointBuildingModel model;

  CleanPointSchedules(this.model);

  @override
  Widget build(BuildContext context) {
    return isListNullOrEmpty(model.schedules)
        ? const SizedBox()
        : ExpandableER(
            title: tt(TTShared.horario),
            children: model.schedules.map2((e) {
              List<Widget> texts = [];

              for (final t in e.getTimesList()) {
                if (isStringFilled(t))
                  texts.add(
                    BodyText(
                      t.replaceFirst("-", " - "),
                      maxLines: 1,
                    ),
                  );
              }
              return Container(
                margin: const EdgeInsets.only(bottom: 10),
                height: (25 * texts.length).toDouble(),
                constraints: const BoxConstraints(minHeight: 35),
                child: Row(
                  children: [
                    Container(
                      width: 30,
                      child: ScheduleDayLetter(e),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: e.isClosed
                          ? BodyText(tt(TTShared.cerrado))
                          : Container(
                              width: double.maxFinite,
                              height: double.maxFinite,
                              child: Column(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceAround,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.max,
                                children: texts,
                              ),
                            ),
                    ),
                  ],
                ),
              );
            }),
          );
  }
}

class ScheduleDayLetter extends StatelessWidget {
  final CleanPointScheduleModel schedule;

  ScheduleDayLetter(this.schedule);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: schedule.scheduleDayEnum!.isToday
            ? schedule.isClosed
                ? themeColors.errorRed
                : themeColors.validGreen
            : themeColors.primaryBlueLight,
      ),
      child: Center(
        child: BodyText(
          schedule.scheduleDayEnum!.translation[0],
          color: themeColors.white,
          isBold: true,
        ),
      ),
    );
  }
}

class CleanPointCategoriesAcceptedList extends StatelessWidget {
  final CleanPointBuildingModel model;

  CleanPointCategoriesAcceptedList(this.model);

  @override
  Widget build(BuildContext context) {
    return ExpandableER(
      title: tt(TTProduct.categoriasAceptadas),
      children: [
        Container(
          margin: const EdgeInsets.only(bottom: 10),
          constraints: const BoxConstraints(minHeight: 35),
          child: Wrap(
            spacing: 10,
            runSpacing: 14,
            runAlignment: WrapAlignment.start,
            alignment: WrapAlignment.start,
            children: productCategoryManager.models
                .where((e) =>
                    model.categoriesAccepted.any((c) => c.categoryId == e.id))
                .map(
                  (e) => CardER(
                    color: themeColors.white,
                    margin: EdgeInsets.zero,
                    padding: const EdgeInsets.only(right: 20),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          height: 50,
                          width: 50,
                          child: e.iconWidget,
                        ),
                        Text(
                          e.type.tt,
                          style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.5),
                        ),
                      ],
                    ),
                  ),
                )
                .toList(),
          ),
        ),
      ],
    );
  }
}

class CleanPointResiduesAcceptedList extends StatelessWidget {
  final CleanPointBuildingModel model;

  CleanPointResiduesAcceptedList(this.model);

  List<ResidueModel> getResidues() {
    List<ResidueModel> residuesAccepted = [];
    for (final e in model.residuesAccepted) {
      var residuo = e.getResidue(residueManager.modelsMap, user);
      if (residuo != null) {
        residuesAccepted.add(residuo);
      }
    }
    return residuesAccepted;
  }

  @override
  Widget build(BuildContext context) {
    return ExpandableER(
      title: tt(TTCleanPoint.residuosAceptados),
      children: [
        Container(
          margin: const EdgeInsets.only(bottom: 10),
          constraints: const BoxConstraints(minHeight: 35),
          child: Wrap(
            spacing: 10,
            runSpacing: 14,
            runAlignment: WrapAlignment.start,
            alignment: WrapAlignment.start,
            children: model.residuesAccepted
                .getResidues()
                .map(
                  (e) => CardER(
                    color: themeColors.white,
                    margin: EdgeInsets.zero,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 10, vertical: 2.5),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          e.name,
                          style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.5),
                        ),
                        Text(
                          e.description,
                          style: const TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w300,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
                .toList(),
          ),
        ),
      ],
    );
  }
}

class CleanPointEntrancesAndExistsList extends StatelessWidget {
  final CleanPointBuildingModel model;

  CleanPointEntrancesAndExistsList(this.model);

  @override
  Widget build(BuildContext context) {
    return ExpandableER(
      title: tt(TTCleanPoint.entradasYSalidas),
      children: [
        for (final e in model.getEntrancesAndExists(zoneManager.modelsMap))
          ...[
            CardER(
              margin: const EdgeInsets.only(bottom: 5),
              color: themeColors.grey0_1,
              child: Container(
                padding: const EdgeInsets.all(10),
                width: double.maxFinite,
                child: Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: themeColors.primaryBlue,
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: BodyText(
                        e.address,
                        maxLines: 3,
                        textAlign: TextAlign.left,
                      ),
                    ),
                    const SizedBox(width: 10),
                    Icon(
                      e.typeEnum == EntranceType.entrance
                          ? Icons.login
                          : e.typeEnum == EntranceType.exit
                              ? Icons.logout
                              : Icons.compare_arrows,
                      color: themeColors.primaryBlue,
                    ),
                  ],
                ),
              ),
            ),
          ].toList(),
      ],
    );
  }
}

class ScheduleWidget extends StatelessWidget {
  final LatLong position;
  final void Function(ScheduleTime? time) onPressedSelectTimeOpen;
  final void Function(ScheduleTime? time) onPressedSelectTimeClose;
  final void Function() onPressedAddHours;
  final void Function(ScheduleTime? time) onPressedRemoveHours;
  final void Function() onPressedSelectLocation;
  final void Function() onPressedSwitchClosed;
  final void Function() onPressedCopyConfiguration;
  final List<ScheduleTime?>? times;
  final ScheduleDay? day;
  final bool isClosed;

  ScheduleWidget({
    required this.position,
    required this.onPressedSelectLocation,
    required this.onPressedRemoveHours,
    required this.onPressedAddHours,
    required this.times,
    required this.day,
    required this.onPressedSelectTimeOpen,
    required this.onPressedSelectTimeClose,
    required this.onPressedSwitchClosed,
    required this.onPressedCopyConfiguration,
    required this.isClosed,
  });

  @override
  Widget build(BuildContext context) {
    List<Widget> timesWidget = [];
    int index = 0;

    if (isListFilled(times!)) {
      timesWidget = times!.map2((e) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (index++ > 0)
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 10),
                    child: TooltipCustom(
                      message: TTShared.eliminar.tt,
                      child: ButtonER(
                        title: responsive.isMobile ? "" : TTShared.eliminar.tt,
                        onPressed: () => onPressedRemoveHours(e),
                        color: themeColors.errorRed,
                        colorIcon: themeColors.white,
                        colorSplash: themeColors.white,
                        colorText: themeColors.white,
                        icon: FontAwesomeIcons.trash,
                      ),
                    ),
                  ),
                ),
              Flexible(
                child: TooltipCustom(
                  message: TTER.apertura.tt,
                  child: ButtonER(
                    title: responsive.isMobile
                        ? ""
                        : isNotNull(e!.open)
                            ? e.open!.viewTime()
                            : TTER.apertura.tt,
                    onPressed: () => onPressedSelectTimeOpen(e),
                    color: isNotNull(e!.open)
                        ? themeColors.validGreen
                        : themeColors.grey1,
                    colorSplash: themeColors.white,
                    colorIcon: isNotNull(e.open)
                        ? themeColors.white
                        : themeColors.black,
                    colorText: isNotNull(e.open)
                        ? themeColors.white
                        : themeColors.black,
                    icon: FontAwesomeIcons.doorOpen,
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Flexible(
                child: TooltipCustom(
                  message: TTER.cierre.tt,
                  child: ButtonER(
                    title: responsive.isMobile
                        ? ""
                        : isNotNull(e.close)
                            ? e.close!.viewTime()
                            : TTER.cierre.tt,
                    onPressed: () => onPressedSelectTimeClose(e),
                    color: isNotNull(e.close)
                        ? themeColors.validGreen
                        : themeColors.grey1,
                    colorSplash: themeColors.white,
                    colorIcon: isNotNull(e.close)
                        ? themeColors.white
                        : themeColors.black,
                    colorText: isNotNull(e.close)
                        ? themeColors.white
                        : themeColors.black,
                    icon: FontAwesomeIcons.doorClosed,
                  ),
                ),
              ),
            ],
          ),
        );
      });
    }

    return ColumnER(
      children: [
        Row(
          children: [
            H3Text(
              day!.translation,
              maxLines: 1,
              textAlign: TextAlign.left,
            ),
            const Spacer(),
            GestureDetector(
              onTap: () => onPressedSwitchClosed(),
              child: TooltipCustom(
                message: tt(TTER.cerradoPregunta),
                child: Container(
                    height: ThemeER.formItemHeight,
                    child: Row(
                      children: [
                        Text(
                          tt(TTER.cerradoPregunta),
                          maxLines: 1,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(width: 10),
                        IgnorePointer(
                          child: CupertinoSwitch(
                            activeColor: themeColors.errorRed,
                            value: isClosed,
                            onChanged: (value) {},
                          ),
                        ),
                      ],
                    )),
              ),
            ),
          ],
        ),
        SizedBox(height: isClosed ? 0 : 10),
        isClosed
            ? const SizedBox()
            : Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Flexible(
                      child: ButtonER(
                        title: TTShared.ubicacion.tt,
                        onPressed: onPressedSelectLocation,
                        color: position.isValid()
                            ? themeColors.validGreen
                            : themeColors.grey1,
                        colorIcon: position.isValid()
                            ? themeColors.white
                            : themeColors.black,
                        colorSplash: themeColors.white,
                        colorText: position.isValid()
                            ? themeColors.white
                            : themeColors.black,
                        icon: FontAwesomeIcons.locationDot,
                      ),
                    ),
                    const SizedBox(width: 10),
                    Flexible(
                      child: ButtonER.styleWhite(
                        title: TTER.aniadirHoras.tt,
                        onPressed: onPressedAddHours,
                        icon: FontAwesomeIcons.circlePlus,
                      ),
                    ),
                  ],
                ),
              ),
        isClosed
            ? const SizedBox()
            : Column(
                children: timesWidget,
              ),
        day == ScheduleDay.monday
            ? Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Column(children: [
                  const SizedBox(height: 10),
                  ButtonER.styleBlue(
                    title: TTER.copiarValoresParaOtrosDias.tt,
                    onPressed: onPressedCopyConfiguration,
                    icon: FontAwesomeIcons.copy,
                  ),
                ]),
              )
            : const SizedBox(),
        const Divider(),
      ],
    );
  }
}

extension CleanPointResiduesAcceptedListExt
    on List<CleanPointResidueAcceptedModel> {
  List<ResidueModel> getResidues() {
    List<ResidueModel> residuesAccepted = [];
    for (final e in this) {
      var residuo = e.getResidue(residueManager.modelsMap, user);
      if (residuo != null) {
        residuesAccepted.add(residuo);
      }
    }
    return residuesAccepted;
  }
}
