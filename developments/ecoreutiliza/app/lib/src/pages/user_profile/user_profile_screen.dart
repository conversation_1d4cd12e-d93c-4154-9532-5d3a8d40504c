import '../../_imports.dart';

class UserProfileScreen extends StatefulWidget {
  final UserProfileProvider provider;

  const UserProfileScreen({
    super.key,
    required this.provider,
  });

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToEnd() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _scrollController.animateTo(_scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
    });
  }

  @override
  Widget build(BuildContext context) {
    var provider = widget.provider;
    return FutureBuilderLoading(
      start: provider.start(),
      loading: const LoadingBodyER(),
      builder: (_) => provider.rebuilding((_) {
        List<ProductModel>? pr;

        // RESERVA: Comentado temporalmente hasta que decidan de implementarlo de nuevo.
        // pr = productManager.models.where2(
        //     (m) => m.isReserved() && m.reservedUserId == provider.model!.id);

        return SingleChildScrollView(
          controller: _scrollController,
          child: Container(
            alignment: Alignment.topCenter,
            constraints: const BoxConstraints(
              maxWidth: globalMaxWidthContainer,
            ),
            margin: const EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                ProfileWidget(provider.model),

                ///Ajustes
                Margin.v(),
                TextTitle(
                  title: tt(TTShared.datosDeUsuario),
                  child: Margin.v(),
                ),
                CardER(
                  margin: EdgeInsets.zero,
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      LabelER(
                        title: tt(TTER.puntos) +
                            ": " +
                            global.citizen!.cleanPointInfo!.points.toString(),
                        icon: FontAwesomeIcons.solidStar,
                        iconColor: themeColors.alertOrange,
                      ),
                      Margin.v(),
                      LabelER(
                        title: tt(TTER.favoritos) +
                            ": " +
                            global.citizen!.cleanPointInfo!.favorites.length
                                .toString(),
                        icon: FontAwesomeIcons.solidHeart,
                        iconColor: themeColors.errorRed,
                      ),
                      Margin.v(),
                      LabelER(
                        title: tt(TTShared.dni) + ": " + global.citizen!.nif,
                        icon: FontAwesomeIcons.idCard,
                      ),
                      Margin.v(),
                      LabelER(
                        title: tt(TTShared.direccion) +
                            ": " +
                            global.citizen!
                                .getAddressComplete(LopdFactoryProfile()),
                        icon: FontAwesomeIcons.mapLocationDot,
                      ),
                      Margin.v(),
                      LabelER(
                        title: tt(TTShared.telefono) +
                            ": " +
                            global.citizen!.phone,
                        icon: FontAwesomeIcons.phone,
                      ),
                    ],
                  ),
                ),

                // RESERVA: Comentado temporalmente hasta que decidan de implementarlo de nuevo.
                // isListNullOrEmpty(pr)
                //     ? SizedBox()
                //     : Column(
                //         mainAxisAlignment: MainAxisAlignment.start,
                //         crossAxisAlignment: CrossAxisAlignment.start,
                //         children: [
                //           SeparatorFiori(),
                //           H2Text(
                //             tt(TTProduct.reutilizablesReservados),
                //             maxLines: 1,
                //             isUpperCase: false,
                //             textAlign: TextAlign.left,
                //           ),
                //           Margin.v(),
                //           ...pr.map(
                //             (e) => ProductSearchItemList(
                //               e,
                //               () {
                //                 DialogService().showCustomDialog(
                //                     ProductDialog(ProductProvider(e)), true);
                //               },
                //               SmallText(
                //                 "${TTER.reservadoHasta.tt}: ${e.reservedDateUntil!.view().substring(0, 10)}",
                //               ),
                //             ),
                //           ),
                //         ],
                //       ),
                ///Ajustes
                Margin.v(),
                TextTitle(
                  title: tt(TTShared.ajustes),
                  child: Margin.v(),
                ),

                ButtonER.styleWhite(
                  icon: Icons.info,
                  title: tt(TTShared.informacionDeLaAPP),
                  onPressed: () => global.home!.openPageInformation(),
                ),
                Margin.v(),
                ButtonER.styleWhite(
                  icon: FontAwesomeIcons.circleQuestion,
                  title: tt(TTShared.ayuda),
                  onPressed: () => global.home!.openHelpOptions(),
                ),
                Margin.v(),
                ButtonER.styleWhite(
                  icon: FontAwesomeIcons.heart,
                  title: tt(TTER.verMisProductosFavoritos),
                  onPressed: () => global.home!.openPageStore(true),
                ),
                Margin.v(),
                ButtonER.styleRed(
                  icon: Icons.logout,
                  title: tt(TTShared.cerrarSesion),
                  onPressed: () => LogoutUseCase().execute(),
                ),
                Margin.v(),

                if (provider.canRemove())
                  ButtonER.styleRed(
                    icon: FontAwesomeIcons.trash,
                    title: tt(TTShared.eliminarCuenta),
                    onPressed: provider.onPressedRemove,
                  ),
              ],
            ),
          ),
        );
      }),
    );
  }
}
