import '../../_imports.dart';

class ProfileWidget extends StatelessWidget {
  final UserSSOModel? model;

  ProfileWidget(this.model);

  @override
  Widget build(BuildContext context) {
    final avatar = AvatarImageContent(
      image: global.profileImage ?? '',
      padding: 0,
      radius: 60,
    );

    final name = LabelER(
      title: global.citizen!.name,
      icon: FontAwesomeIcons.userLarge,
    );

    final email = LabelER(
      title: global.citizen!.email,
      icon: FontAwesomeIcons.solidEnvelope,
    );

    return CardER(
      margin: EdgeInsets.zero,
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          avatar,
          Margin.v(marginm),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              name,
              email,
            ],
          ),
        ],
      ),
    );
  }
}

class UserProfileAppBar extends StatelessWidget implements PreferredSizeWidget {
  final UserProfileProvider provider;

  UserProfileAppBar(this.provider);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: themeColors.primaryBlue,
      titleSpacing: 5,
      title: Container(
        height: 60,
        width: 190,
        alignment: Alignment.centerLeft,
        child: Padding(
          padding: const EdgeInsets.only(top: 4),
          child: ImageCacheService().getFromAsset(
            "assets/images/ecoreutiliza_white.svg",
            fit: BoxFit.fitHeight,
          ),
        ),
      ),
      actions: [
        if (global.citizen != null)
          GestureDetector(
            child: Icon(
              FontAwesomeIcons.qrcode,
              color: themeColors.white,
            ),
            onTap: () => DialogService().showCustomDialog(
                DialogER(
                  child: DialogQRWidget(
                    name: TTER.miQR.tt,
                    data: global.citizen!.buildQR(),
                    imageService: imageService,
                  ),
                ),
                true),
          ),
        if (provider.canEdit())
          IconButton(
            onPressed: provider.onPressedEdit,
            icon: const Icon(FontAwesomeIcons.penToSquare),
          ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(60);
}
