import '../../_imports.dart';

class UserProfilePage extends StatelessWidget {
  final provider = UserProfileProvider();

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
      stream: provider.rebuild.stream,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        return ScaffoldCustom(
          drawer: HomeDrawer(),
          appBar: UserProfileAppBar(provider),
          isScrollable: false,
          padding: EdgeInsets.zero,
          isCenter: true,
          body: UserProfileScreen(provider: provider),
        );
      },
    );
  }
}
