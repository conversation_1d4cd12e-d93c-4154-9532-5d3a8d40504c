import '../../_imports.dart';

class ProductRequestDeliveryDialog extends StatefulWidget {
  final ProductRequestDeliveryProvider provider;

  ProductRequestDeliveryDialog(this.provider);

  @override
  State<ProductRequestDeliveryDialog> createState() =>
      _ProductRequestDeliveryDialogState();
}

class _ProductRequestDeliveryDialogState
    extends State<ProductRequestDeliveryDialog> {
  @override
  void dispose() {
    widget.provider.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DialogER(
      child: ProductRequestDeliveryScreen(widget.provider, EdgeInsets.zero),
      title: tt(TTProduct.solicitudDeNuevoReutilizable),
    );
  }
}
