import '../../_imports.dart';

class ProductRequestDeliveryProvider extends ProviderBaseV2 {
  int carouselIndex = 0;
  List<String> imgs = [];

  ProductRequestModel model;

  ProductRequestDeliveryProvider(this.model)
      : super(
          streams: [
            productRequestManager.onChange
                .map((event) => event.after.guid == model.guid),
          ],
        );

  Future<void> start() async {}

  @override
  void internalDispose() {}

  void onPressedCleanPointInfo(CleanPointBuildingModel cp) {
    DialogService()
        .showCustomDialog(CleanPointDialog(CleanPointProvider(cp)), true);
  }

  Future<List<String>> getImages() async {
    if (isListFilled(imgs)) return imgs;

    if (isListNullOrEmpty(model.imagesOrIdsMd5)) return imgs;

    for (var i in model.imagesOrIdsMd5) {
      final img = await productUseCase.getImage(i);
      if (isStringFilled(img)) imgs.add(img);
    }

    return imgs;
  }

  void refreshModel() {
    model = productRequestManager.getByPk(model.pk) ?? model;
  }

  void onChangedIndex(int i) {
    carouselIndex = i;
    rebuild();
  }

  void onPressedHide() {
    productRequestUseCase.save(
      ProductRequestUseCaseValidator(
        model.copyWith(isHiddenByCitizen: true),
        cleanPointBuildingUseCase,
        productRequestUseCase,
        user,
      ),
    );
  }

  onPressedCleanPointHowToArrive(CleanPointBuildingModel cp) {
    // Copiado de CleanPointProvider
    // TODO: Implementar lógica similar cuando haya PL móviles
    // if (model.isMovable()) {
    //   DialogService().showOptionDialog(
    //     model.schedules.getOpen().map2(
    //           (e) => OptionButton(
    //             icon: FontAwesomeIcons.locationDot,
    //             onPressed: () {
    //               externalLinkService.map(
    //                 e.lat,
    //                 e.lng,
    //                 travelMode: TravelMode.driving,
    //               );
    //             },
    //             text: e.scheduleDayEnum!.translation,
    //           ),
    //         ),
    //   );
    //   return;
    // }
    // final schedule = model.schedules.getFirstWithPosition();
    //  if (schedule == null) return;
    final position = cp.getEntrancePosition(zoneManager.modelsMap);
    if (position == null) return;
    externalLinkService.map(
      position.lat,
      position.long,
      travelMode: TravelMode.driving,
    );
  }
}
