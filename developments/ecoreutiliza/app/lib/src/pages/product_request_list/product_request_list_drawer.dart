import '../../_imports.dart';

class ProductRequestListFilterDrawer extends StatefulWidget {
  final ProductRequestListProvider? provider;

  ProductRequestListFilterDrawer(this.provider);

  @override
  State<ProductRequestListFilterDrawer> createState() =>
      _ProductRequestListFilterDrawerState();
}

class _ProductRequestListFilterDrawerState
    extends State<ProductRequestListFilterDrawer> {
  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Container(
        child: SafeArea(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            color: themeColors.greyBackground,
            child: ScrollConfiguration(
              behavior:
                  ScrollConfiguration.of(context).copyWith(scrollbars: false),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 40),
                    Container(
                      child: ButtonER(
                        title: tt(TTShared.restablecer),
                        color: themeColors.primaryBlue,
                        colorSplash: themeColors.primaryBlueLight,
                        colorText: themeColors.white,
                        onPressed: () {
                          widget.provider!.filter!.restart();
                          widget.provider!.rebuild();
                          global.home!.cleanPointMarkerAreaManager
                              .resetAllMarkers();
                          global.home!.cleanPointMarkerEntrancesManager
                              .resetAllMarkers();
                          setState(() {});
                        },
                      ),
                    ),
                    SeparatorFiori.half(),
                    ExpansionTileCheck<ProductRequestState>(
                      isInitialiteExpanded: false,
                      title: tt(TTER.estados),
                      subtitle: tt(TTER.seleccionados),
                      models: widget.provider!.filter!.states,
                      builder: (m) {
                        return Text(m.name);
                      },
                      onChanged: () {
                        setState(() {});
                        widget.provider!.rebuild();
                      },
                    ),
                    ExpansionTileCheck<CleanPointBuildingModel>(
                      isInitialiteExpanded: false,
                      title: tt(TTER.puntosLimpios),
                      subtitle: tt(TTER.seleccionados),
                      models: widget.provider!.filter!.cleanPoints,
                      builder: (m) {
                        return Text(m.name);
                      },
                      onChanged: () {
                        setState(() {});
                        widget.provider!.rebuild();
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
