import '../../_imports.dart';

class ProductRequestListPage extends StatelessWidget {
  ProductRequestListPage();

  @override
  Widget build(BuildContext context) {
    final ProductRequestListProvider? provider =
        global.home!.productRequestListProvider.get();

    return FloatingTabPage(
      provider: provider,
      getDrawer: () => HomeDrawer(),
      getEndDrawer: () => ProductRequestListFilterDrawer(provider),
      getAppBar: () => ProductRequestListAppBar(provider),
      getTabMenu: () => globalMenu.productRequestPageListTabs,
      getPage: () => getProductRequestTab(),
      stream: global.home!.rebuild.stream,
      getContent: (e) => GestureDetector(
        child: Container(
          padding: const EdgeInsets.all(10),
          decoration: e == globalMenu.productRequestPageListTabActive
              ? BoxDecoration(
                  color: themeColors.white,
                  borderRadius: BorderRadius.circular(5),
                )
              : const BoxDecoration(),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                e.title,
                style: TextStyle(
                  color: e == globalMenu.productRequestPageListTabActive
                      ? themeColors.primaryBlue
                      : themeColors.grey5,
                  fontSize: globalIconSizeXS,
                ),
              ),
              const SizedBox(width: 5),
              Icon(
                e.icon,
                size: 16,
                color: e == globalMenu.productRequestPageListTabActive
                    ? themeColors.primaryBlue
                    : themeColors.grey5,
              ),
            ],
          ),
        ),
        onTap: () {
          globalMenu.productRequestPageListTabActive = e as Permission;
          global.home!.rebuild();
        },
      ),
    );
  }

  Widget getProductRequestTab() {
    final provider = global.home!.productRequestListProvider.get()!;

    if (globalMenu.productRequestPageListTabActive?.action == MenuAction.solicitudes) {
      return ProductRequestDeliveryListScreenMobile(provider);
    }

    if (globalMenu.productRequestPageListTabActive?.action == MenuAction.solicitudesRetiro) {
      return ProductRequestWithdrawalListScreenMobile(provider);
    }

    return Container();
  }
}
