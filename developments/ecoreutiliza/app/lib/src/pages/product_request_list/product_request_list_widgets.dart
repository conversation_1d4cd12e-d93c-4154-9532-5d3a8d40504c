import '../../_imports.dart';

class ProductRequestListAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  final ProductRequestListProvider? provider;

  ProductRequestListAppBar(this.provider);

  @override
  Widget build(BuildContext context) {
    List<Widget> additionalActions = [];

    return AppBarSearchHiden(
      onChanged: (v) => provider!.filter!.onChangedText(v),
      onCleared: () => provider!.filter!.onChangedText(""),
      isClearOnSubmit: false,
      isClosedOnSubmit: false,
      initialValue: provider!.filter!.searchController.text,
      defaultAppBar: (c, button) => AppBar(
        backgroundColor: themeColors.primaryBlue,
        titleSpacing: 5,
        title: Container(
          height: 60,
          width: 190,
          alignment: Alignment.centerLeft,
          child: Padding(
            padding: const EdgeInsets.only(top: 4),
            child: ImageCacheService().getFromAsset(
              "assets/images/ecoreutiliza_white.svg",
              fit: BoxFit.fitHeight,
            ),
          ),
        ),
        actions: [
          button,
          StreamBuilder(
              stream: provider!.rebuild.stream,
              builder: (_, __) => Container(
                    child: ActionButtonAppBar(
                      color: provider!.filter!.isAppliedAnyFilter()
                          ? themeColors.errorRed
                          : themeColors.white,
                      icon: FontAwesomeIcons.filter,
                      onPressed: () {
                        Scaffold.of(context).openEndDrawer();
                      },
                      tooltip: tt(TTShared.filtrar),
                    ),
                  )),
          ...additionalActions,
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(60);
}
