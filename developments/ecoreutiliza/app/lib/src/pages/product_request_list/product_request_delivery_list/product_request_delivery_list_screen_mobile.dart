import '../../../_imports.dart';

class ProductRequestDeliveryListScreenMobile extends StatefulWidget {
  final ProductRequestListProvider provider;

  ProductRequestDeliveryListScreenMobile(this.provider);

  @override
  State<ProductRequestDeliveryListScreenMobile> createState() =>
      _ProductRequestDeliveryListScreenMobileState();
}

class _ProductRequestDeliveryListScreenMobileState
    extends State<ProductRequestDeliveryListScreenMobile> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: StreamBuilder(
          stream: widget.provider.rebuild.stream,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            final productRequests = widget.provider.getDeliveryRequests();

            Widget body = const SizedBox();

            if (productRequests.isEmpty) {
              if (!syncService.isCompletedOnceSynchronizable<
                  Synchronizer<ProductModel, String>>())
                body = const LoadingBodyER();
              else
                body = Center(
                    child: H1Text(
                        TTProduct.noSeHanEncontradoSolicitudesDeEntrega.tt));
            } else {
              body = ListView.builder(
                itemCount: productRequests.length,
                padding: EdgeInsets.only(
                  top: global.initialTopPadding,
                  left: 5,
                  right: 5,
                ),
                itemBuilder: (_, i) => ProductRequestDeliveryPendingItemList(
                  productRequests[i],
                  () {
                    widget.provider!.productDeliveryRequestListProvider
                        .checkAsReaded(productRequests[i]);
                    DialogService().showCustomDialog(
                      ProductRequestDeliveryDialog(
                          ProductRequestDeliveryProvider(productRequests[i])),
                      true,
                    );
                  },
                  widget.provider!,
                ),
              );
            }

            return Scaffold(
              floatingActionButton: widget
                  .provider!.productDeliveryRequestListProvider
                  .getFloatingButton(),
              backgroundColor: themeColors.greyBackground,
              body: body,
            );
          }),
    );
  }
}
