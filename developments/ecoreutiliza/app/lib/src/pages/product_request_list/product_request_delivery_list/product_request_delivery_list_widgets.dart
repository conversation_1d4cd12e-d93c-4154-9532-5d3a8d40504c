import '../../../_imports.dart';

class ProductRequestDeliveryPendingItemList extends StatelessWidget {
  final ProductRequestModel m;
  final void Function() onPressed;
  final ProductRequestListProvider provider;

  ProductRequestDeliveryPendingItemList(this.m, this.onPressed, this.provider);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: CardER(
        color: m.isNeccesaryNotify(user.role) ? Colors.orange[50] : null,
        child: Container(
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(ThemeER.radius),
                child: Container(
                    height: 80,
                    width: 80,
                    child: m.imagesOrIdsMd5.isEmpty
                        ? FittedBox(
                            child: Padding(
                                padding: const EdgeInsets.all(5),
                                child: Icon(
                                    FontAwesomeIcons.solidCircleQuestion,
                                    color: ThemeER.iconColor)))
                        : ImageERAsync(() async {
                            return productRequestUseCase
                                .getImage(m.imagesOrIdsMd5.first);
                          })),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Column(
                  children: [
                    _state(),
                    SeparatorFiori.half(),
                    _productName(),
                  ],
                ),
              ),
              _requestDate()
            ],
          ),
        ),
      ),
    );
  }

  Widget _productName() => _row(
        m.name,
        FontAwesomeIcons.box,
        TTProduct.reutilizable.tt,
        null,
      );

  Widget _state() => _row(
        m.stateEnum.tt,
        m.stateEnum.icon,
        TTShared.estado.tt,
        m.stateEnum.iconColor,
      );

  Widget _requestDate() => Column(
        children: [
          Text(
            m.creationDate.day.toString().padLeft(2, '0') +
                "/" +
                m.creationDate.month.toString().padLeft(2, '0') +
                "/" +
                m.creationDate.year.toString().padLeft(4, '0'),
            style: TextStyle(
              color: themeColors.grey6,
              fontSize: 10,
            ),
          ),
          Text(
            m.creationDate.hour.toString().padLeft(2, '0') +
                ":" +
                m.creationDate.minute.toString().padLeft(2, '0'),
            style: TextStyle(
              color: themeColors.grey6,
              fontSize: 10,
            ),
          )
        ],
      );

  Widget _row(String title, IconData icon, String tooltip, Color? iconColor) {
    return TooltipCustom(
      message: tooltip,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: iconColor ?? themeColors.grey6,
            size: 20,
          ),
          const SizedBox(width: 10),
          Flexible(
            child: BodyText(
              title,
              maxLines: 3,
              textAlign: TextAlign.left,
            ),
          ),
        ],
      ),
    );
  }
}
