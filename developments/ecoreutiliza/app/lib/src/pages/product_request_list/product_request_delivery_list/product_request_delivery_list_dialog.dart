import '../../../_imports.dart';

class ProductRequestDeliveryListDialog extends StatelessWidget {
  final ProductRequestListProvider provider;

  ProductRequestDeliveryListDialog(this.provider);

  @override
  Widget build(BuildContext context) {
    return DialogER(
      title: TTProduct.solicitudesDeEntrega.tt,
      action: ElevatedButton(
        onPressed: () {
          DialogService().showCustomDialog(
            ProductRequestDeliveryCreateDialog(
                ProductRequestDeliveryCreateProvider
                    .ProductDeliveryRequestCreateProvider()),
            true,
          );
        },
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeER.radius),
          ),
          backgroundColor: themeColors.secondaryGreen,
          elevation: ThemeER.elevation,
          padding: ThemeER.formItemPadding,
        ),
        child: Text(TTProduct.nuevaSolicitudDeEntrega.tt),
      ),
      child: provider.rebuilding(
        (_) => SingleChildScrollView(
          child: ProductRequestDeliveryListScreen(provider),
        ),
      ),
    );
  }
}
