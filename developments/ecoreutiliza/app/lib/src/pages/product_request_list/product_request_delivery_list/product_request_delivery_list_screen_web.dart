import '../../../_imports.dart';

class ProductRequestDeliveryListScreen extends StatelessWidget {
  final ProductRequestListProvider provider;

  ProductRequestDeliveryListScreen(this.provider);

  @override
  Widget build(BuildContext context) {
    return provider.rebuilding((_) {
      final requests = provider.getDeliveryRequests();

      requests.sortByDateDesc((o) => o.modifyDate);

      if (isListNullOrEmpty(requests))
        return Padding(
          padding: const EdgeInsets.only(bottom: 32, top: 16),
          child: H3Text(tt(TTProduct.noHaySolicitudesPendientes)),
        );

      return Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...requests.map2(
              (e) => ProductRequestDeliveryPendingItemList(
                e,
                () {
                  provider.productDeliveryRequestListProvider.checkAsReaded(e);
                  DialogService().showCustomDialog(
                    ProductRequestDeliveryDialog(
                        ProductRequestDeliveryProvider(e)),
                    true,
                  );
                },
                provider,
              ),
            )
          ]);
    });
  }
}
