import '../../../_imports.dart';

class ProductRequestDeliveryListProvider extends ProviderBaseV2 {
  ProductRequestDeliveryListProvider()
      : super(streams: [
          productRequestManager.onChange,
        ]);

  @override
  void internalDispose() {}

  Future<void> checkAsReaded(ProductRequestModel r) async {
    if (r.stateEnum == ProductRequestState.accepted ||
        r.stateEnum == ProductRequestState.rejected) {
      await productRequestUseCase.save(
        ProductRequestUseCaseValidator(
          r.copyWith(isReadedByCitizen: true),
          cleanPointBuildingUseCase,
          productRequestUseCase,
          user,
        ),
      );
    }
  }

  Future<void> checkAsHidde(ProductRequestModel r) async {
    if (r.stateEnum == ProductRequestState.accepted ||
        r.stateEnum == ProductRequestState.rejected) {
      await productRequestUseCase.save(
        ProductRequestUseCaseValidator(
          r.copyWith(
            isReadedByCitizen: true,
            isHiddenByCitizen: true,
          ),
          cleanPointBuildingUseCase,
          productRequestUseCase,
          user,
        ),
      );
    }
  }

  Widget getFloatingButton() {
    return FloatingActionButton.small(
      backgroundColor: themeColors.primaryBlue,
      child: const Icon(Icons.add),
      onPressed: () {
        DialogService().showCustomDialog(
          ProductRequestDeliveryCreateDialog(
              ProductRequestDeliveryCreateProvider
                  .ProductDeliveryRequestCreateProvider()),
          true,
        );
      },
    );
  }
}
