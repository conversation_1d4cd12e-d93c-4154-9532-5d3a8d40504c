import '../../_imports.dart';

class ProductRequestListProvider extends ProviderBaseV2 {
  ProductRequestListFilter? get filter => _filter;
  ProductRequestListFilter? _filter;

  ProductRequestDeliveryListProvider get productDeliveryRequestListProvider =>
      _productDeliveryRequestListProvider;
  ProductRequestDeliveryListProvider _productDeliveryRequestListProvider =
      ProductRequestDeliveryListProvider();

  ProductRequestWithdrawalListProvider
      get productWithdrawalRequestListProvider =>
          _productWithdrawalRequestListProvider;
  ProductRequestWithdrawalListProvider _productWithdrawalRequestListProvider =
      ProductRequestWithdrawalListProvider();

  ProductRequestListProvider()
      : super(streams: [
          productRequestManager.onChange,
          productRequestManager.onStarted,
          productWithdrawalRequestManager.onChange,
          productWithdrawalRequestManager.onStarted,
        ]) {
    _filter = ProductRequestListFilter(this);
    _productDeliveryRequestListProvider = ProductRequestDeliveryListProvider();
    _productWithdrawalRequestListProvider =
        ProductRequestWithdrawalListProvider();
  }

  @override
  void internalDispose() {}

  List<ProductRequestModel> getDeliveryRequests() {
    return productRequestManager.models.where((m) {
      if (m.isHiddenByCitizen) return false;

      return filter!.isMatch(ProductRequestFilterModel(
        name: m.name,
        description: m.description,
        state: m.state,
        cleanPointGuid: m.cleanPointGuid,
      ));
    }).toList()
      ..sortByDateDesc((o) => o.creationDate);
  }

  List<ProductWithdrawalRequestModel> getWithdrawalRequests() =>
      productWithdrawalRequestManager.models.where((m) {
        if (m.isHiddenByCitizen) return false;

        return filter!.isMatch(ProductRequestFilterModel(
          name: m.name,
          description: m.description,
          state: m.state,
          cleanPointGuid: m.cleanPointGuid,
        ));
      }).toList()
        ..sortByDateDesc((o) => o.creationDate);
}
