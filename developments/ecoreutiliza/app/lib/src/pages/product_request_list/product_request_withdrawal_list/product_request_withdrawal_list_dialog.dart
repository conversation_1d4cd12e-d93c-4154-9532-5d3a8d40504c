import '../../../_imports.dart';

class ProductRequestWithdrawalListDialog extends StatelessWidget {
  final ProductRequestListProvider provider;

  ProductRequestWithdrawalListDialog(this.provider);

  @override
  Widget build(BuildContext context) {
    return DialogER(
      title: TTProduct.solicitudesDeRetiro.tt,
      action: ElevatedButton(
        onPressed: () {
          DialogService().showCustomDialog(
            ProductRequestWithdrawalCreateDialog(
                ProductRequestWithdrawalCreateProvider()),
            true,
          );
        },
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeER.radius),
          ),
          backgroundColor: themeColors.secondaryGreen,
          elevation: ThemeER.elevation,
          padding: ThemeER.formItemPadding,
        ),
        child: Text(TTProduct.nuevaSolicitudDeRetiro.tt),
      ),
      child: provider.rebuilding(
        (_) => SingleChildScrollView(
          child: ProductRequestWithdrawalListScreen(provider),
        ),
      ),
    );
  }
}
