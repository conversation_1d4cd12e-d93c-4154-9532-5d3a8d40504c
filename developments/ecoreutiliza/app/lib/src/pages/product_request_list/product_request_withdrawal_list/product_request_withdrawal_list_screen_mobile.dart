import '../../../_imports.dart';

class ProductRequestWithdrawalListScreenMobile extends StatefulWidget {
  final ProductRequestListProvider? provider;

  ProductRequestWithdrawalListScreenMobile(this.provider);

  @override
  State<ProductRequestWithdrawalListScreenMobile> createState() =>
      _ProductRequestWithdrawalListScreenMobileState();
}

class _ProductRequestWithdrawalListScreenMobileState
    extends State<ProductRequestWithdrawalListScreenMobile> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: StreamBuilder(
          stream: widget.provider!.rebuild.stream,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            final productWithdrawalRequests =
                widget.provider!.getWithdrawalRequests();

            Widget body = const SizedBox();

            if (productWithdrawalRequests.isEmpty) {
              if (!syncService.isCompletedOnceSynchronizable<
                  Synchronizer<ProductModel, String>>())
                body = const LoadingBodyER();
              else
                body = Center(
                    child: H1Text(
                        TTProduct.noSeHanEncontradoSolicitudesDeRetiro.tt));
            } else {
              body = ListView.builder(
                itemCount: productWithdrawalRequests.length,
                padding: EdgeInsets.only(
                  top: global.initialTopPadding,
                  left: 5,
                  right: 5,
                ),
                itemBuilder: (_, i) => ProductRequestWithdrawalPendingItemList(
                  productWithdrawalRequests[i],
                  () {
                    widget.provider!.productWithdrawalRequestListProvider
                        .checkAsReaded(productWithdrawalRequests[i]);
                    DialogService().showCustomDialog(
                      ProductRequestWithdrawalDialog(
                          ProductRequestWithdrawalProvider(
                              productWithdrawalRequests[i])),
                      true,
                    );
                  },
                  widget.provider!,
                ),
              );
            }
            return Scaffold(
              // floatingActionButton: widget.provider!.getFloatingButtons(),
              backgroundColor: themeColors.greyBackground,
              body: body,
            );
          }),
    );
  }
}
