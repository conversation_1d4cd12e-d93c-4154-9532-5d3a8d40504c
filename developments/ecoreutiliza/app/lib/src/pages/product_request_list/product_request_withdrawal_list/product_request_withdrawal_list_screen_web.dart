import '../../../_imports.dart';

class ProductRequestWithdrawalListScreen extends StatelessWidget {
  final ProductRequestListProvider provider;

  ProductRequestWithdrawalListScreen(this.provider);

  @override
  Widget build(BuildContext context) {
    return provider.rebuilding((_) {
      final requests = provider.getWithdrawalRequests();

      requests.sortByDateDesc((o) => o.modifyDate);

      if (isListNullOrEmpty(requests))
        return Padding(
          padding: const EdgeInsets.only(bottom: 32, top: 16),
          child: H3Text(tt(TTProduct.noHaySolicitudesPendientes)),
        );

      return Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...requests.map2(
              (e) => ProductRequestWithdrawalPendingItemList(
                e,
                () {
                  provider.productWithdrawalRequestListProvider
                      .checkAsReaded(e);
                  DialogService().showCustomDialog(
                    ProductRequestWithdrawalDialog(
                        ProductRequestWithdrawalProvider(e)),
                    true,
                  );
                },
                provider,
              ),
            )
          ]);
    });
  }
}
