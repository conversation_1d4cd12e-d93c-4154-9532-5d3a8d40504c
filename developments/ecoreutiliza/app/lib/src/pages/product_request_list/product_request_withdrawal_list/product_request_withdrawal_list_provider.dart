import '../../../_imports.dart';

class ProductRequestWithdrawalListProvider extends ProviderBaseV2 {
  ProductRequestWithdrawalListProvider()
      : super(streams: [
          productWithdrawalRequestManager.onChange,
        ]);

  @override
  void internalDispose() {}

  Future<void> checkAsReaded(ProductWithdrawalRequestModel r) async {
    if (r.stateEnum == ProductRequestState.accepted ||
        r.stateEnum == ProductRequestState.rejected) {
      await productWithdrawalRequestUseCase.save(
        ProductWithdrawalRequestUseCaseValidator(
          r.copyWith(isReadedByCitizen: true),
          cleanPointBuildingUseCase,
          productWithdrawalRequestUseCase,
          user,
        ),
      );
    }
  }

  Future<void> checkAsHidde(ProductWithdrawalRequestModel r) async {
    if (r.stateEnum == ProductRequestState.accepted ||
        r.stateEnum == ProductRequestState.rejected) {
      await productWithdrawalRequestUseCase.save(
        ProductWithdrawalRequestUseCaseValidator(
          r.copyWith(
            isReadedByCitizen: true,
            isHiddenByCitizen: true,
          ),
          cleanPointBuildingUseCase,
          productWithdrawalRequestUseCase,
          user,
        ),
      );
    }
  }
}
