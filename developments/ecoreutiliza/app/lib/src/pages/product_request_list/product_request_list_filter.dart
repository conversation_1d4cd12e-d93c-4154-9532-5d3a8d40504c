import '../../_imports.dart';

/// Clase que contiene los filtros de puntos limpios.
class ProductRequestListFilter {
  List<StreamSubscription> _subs = [];
  List<Selectable<CleanPointBuildingModel>> cleanPoints = [];
  List<Selectable<ProductRequestState>> states = [
    Selectable(ProductRequestState.pending, false),
    Selectable(ProductRequestState.accepted, false),
    Selectable(ProductRequestState.rejected, false),
    Selectable(ProductRequestState.canceled, false),
    Selectable(ProductRequestState.expired, false),
    Selectable(ProductRequestState.ended, false)
  ];

  final searchController = TextEditingController();
  final _changeTextDelayed =
      DelayedRenewableOperationV2(delay: const Duration(milliseconds: 300));

  final ProductRequestListProvider provider;

  ProductRequestListFilter(this.provider) {
    _subs.add(cleanPointBuildingManager.onChange.listen((event) {
      _onOpened();
    }));
    searchController.addListener(() {
      _changeTextDelayed.execute(() => provider.rebuild());
    });
    _onOpened();
  }

  void restart() {
    _onOpened();
    searchController.text = "";
    global.home!.cleanPointMarkerAreaManager.resetAllMarkers();
    global.home!.cleanPointMarkerEntrancesManager.resetAllMarkers();
    provider.rebuild();
  }

  bool isAppliedAnyFilter() {
    if (cleanPoints.exists((m) => m.isSelected)) return true;
    if (states.exists((m) => m.isSelected)) return true;
    if (isStringFilled(searchController.text)) return true;
    return false;
  }

  bool isMatch(ProductRequestFilterModel p) {
    if ((isStringFilled(searchController.text) &&
            searchController.text.length > 2) &&
        !p.name.toLowerCase().contains(searchController.text.toLowerCase()) &&
        !p.description
            .toLowerCase()
            .contains(searchController.text.toLowerCase())) return false;

    final selectedCleanPointIds = cleanPoints
        .where((filter) => filter.isSelected)
        .map((filter) => filter.value.id)
        .toSet();

    if (selectedCleanPointIds.isNotEmpty) {
      final Selectable<CleanPointBuildingModel>? cleanPoint =
          cleanPoints.firstWhere2((m) => p.cleanPointGuid == m.value.guid);
      if (cleanPoint == null) return false;
      if (isFalse(cleanPoint.isSelected)) return false;
    }

    if (states.any((d) => d.isSelected)) {
      final selectedStates = states
          .where((state) => state.isSelected)
          .map((state) => state.value.id)
          .toList();
      if (!selectedStates.contains(p.state)) return false;
    }

    return true;
  }

  void _onOpened() {
    cleanPoints.clear();
    cleanPointBuildingManager.models.forEach(
      (m) => cleanPoints.addNew(
        Selectable(m, false),
        (m1, m2) => m1.value.guid == m2.value.guid,
      ),
    );
    states.clear();
    states.addAll([
      Selectable(ProductRequestState.pending, false),
      Selectable(ProductRequestState.accepted, false),
      Selectable(ProductRequestState.rejected, false),
      Selectable(ProductRequestState.canceled, false),
      Selectable(ProductRequestState.expired, false),
      Selectable(ProductRequestState.ended, false)
    ]);
  }

  void dispose() {
    _subs.forEach((s) => s.cancel());
  }

  /// Se llama desde mobile cuando modifca el texto en el AppBar.
  void onChangedText(String v) {
    searchController.text = v;
    global.home!.cleanPointMarkerAreaManager.resetAllMarkers();
    global.home!.cleanPointMarkerEntrancesManager.resetAllMarkers();
  }
}

class ProductRequestFilterModel {
  final String name;
  final String description;
  final String cleanPointGuid;
  final int state;

  ProductRequestFilterModel({
    required this.name,
    required this.description,
    required this.cleanPointGuid,
    required this.state,
  });
}
