import '../../_imports.dart';

class InfoPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final url = configurationManager.models
        .firstWhere2((m) => m.key == ConfigurationKey.urlProject);
    return ScaffoldCustom(
      isScrollable: false,
      padding: EdgeInsets.zero,
      isAppBarForceToShowed: true,
      appBar: AppBarER(tt(TTShared.informacion)),
      body: WebViewScreen(url?.value ?? ''),
    );
  }
}
