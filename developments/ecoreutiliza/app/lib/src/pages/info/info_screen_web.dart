import '../../_imports.dart';

class InfoScreenWeb extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final url = configurationManager.models
        .firstWhere2((m) => m.key == ConfigurationKey.urlProject);

    return Scaffold(
      backgroundColor: themeColors.greyBackground,
      body: CardER(
          padding: EdgeInsets.zero,
          margin: EdgeInsets.all(ThemeER.bodySeparationProjectWeb),
          child: WebViewScreen(url?.value ?? '')),
    );
  }
}
