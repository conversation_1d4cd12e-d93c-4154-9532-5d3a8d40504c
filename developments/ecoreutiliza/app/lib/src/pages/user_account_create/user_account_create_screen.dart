import '../../_imports.dart';

class UserAccountCreateScreen extends StatelessWidget {
  final UserAccountCreateProvider provider;

  UserAccountCreateScreen(this.provider);

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // ignore: unawaited_futures
        showDialogConfirmation(
          text: tt(TTER.quieresCancelarLaOperacionPregunta),
        ).then((value) {
          if (value) NavigatorService().pop();
        });
        return false;
      },
      child: provider.rebuilding(
        (_) => Container(
          width: double.maxFinite,
          child: Column(
            children: [
              SeparatorFiori.half(),
              TextFormFieldERValitation(
                title: tt(TTShared.nombre),
                controller: provider.nameField,
                icon: FontAwesomeIcons.solidUser,
                validators: [
                  ValidatorStringMinLength(2),
                  ValidatorStringMaxLength(30),
                  ValidatorStringHasNotNumber(),
                ],
              ),
              SeparatorFiori.half(),
              TextFormFieldERValitation(
                title: tt(TTShared.apellidos),
                controller: provider.lastNameField,
                icon: FontAwesomeIcons.userGroup,
                validators: [
                  ValidatorStringMinLength(2),
                  ValidatorStringMaxLength(50),
                  ValidatorStringHasNotNumber(),
                ],
              ),
              SeparatorFiori(),
              TextFormFieldERValitation(
                title: tt(TTShared.email),
                controller: provider.emailField,
                icon: FontAwesomeIcons.at,
                validators: [ValidatorEmail()],
              ),
              SeparatorFiori.half(),
              TextFormFieldERValitation(
                title: tt(TTShared.repiteElEmail),
                controller: provider.emailRepeatField,
                icon: FontAwesomeIcons.at,
                validators: [
                  ValidatorResult<String>((value) {
                    if (provider.emailField.text != value)
                      return Result.error(tt(TTShared.losEmailsNoCoinciden));
                    return Result.valid();
                  }),
                ],
              ),
              SeparatorFiori(),
              CalloutContainerER.info(
                TTSSO.laContraseniaDebeTenerAlMenos.tt +
                    ":\n" +
                    tt(
                      TTSSO.descripcionRequisitosContraseniaMinMax,
                      ["8", "25"],
                    ),
              ),
              SeparatorFiori(),
              TextFormFieldERValitation(
                title: tt(TTShared.contrasenia),
                controller: provider.passwordField,
                icon: FontAwesomeIcons.lock,
                validators: [
                  ValidatorResult(
                    (value) => Password(
                      value,
                      minLength: 8,
                      maxLength: 25,
                      hasName: false,
                    ).toResult(),
                  ),
                ],
              ),
              SeparatorFiori.half(),
              TextFormFieldERValitation(
                title: tt(TTShared.repiteContrasenia),
                controller: provider.passwordRepeatField,
                icon: FontAwesomeIcons.lock,
                validators: [
                  ValidatorResult<String>((value) {
                    if (provider.passwordField.text != value)
                      return Result.error(tt(TTShared.contraseniasNoCoinciden));
                    return Result.valid();
                  }),
                ],
              ),
              SeparatorFiori(),
              SmallText(
                tt(TTShared.todosLosCamposSonObligatorios),
                textAlign: TextAlign.center,
                color: themeColors.grey8,
              ),
              SeparatorFiori(),
              SeparatorFiori.half(),
              Container(
                width: double.maxFinite,
                alignment: Alignment.centerRight,
                child: Container(
                  width: ThemeER.dialogConfirmButtonWidth.get(),
                  child: ButtonER(
                    title: tt(TTShared.registrarme),
                    color: themeColors.secondaryGreen,
                    colorSplash: themeColors.white,
                    colorText: themeColors.white,
                    icon: FontAwesomeIcons.solidCircleCheck,
                    onPressed: () => provider.save(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
