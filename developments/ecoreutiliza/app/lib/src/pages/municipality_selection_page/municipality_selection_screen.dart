import '../../_imports.dart';

class MunicipalitySelectionScreen extends StatelessWidget {
  final MunicipalitySelectionProvider provider;

  MunicipalitySelectionScreen(this.provider);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
      stream: provider.rebuild.stream,
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        return Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            SeparatorFiori.half(),
            TestInfo.forced(
              tt(TTShared.deDondeSaleEstaInformacion),
              getContent: () => MarkdownWidget("""
El listado de municipios viene de la base de datos del SSO, tabla ***'configuracion'***.

Para añadir o modificar municipios, se ha de hacer a mano editando la tabla de la base de datos.

Actualmente no existe en la web del SSO, ningún formulario para realizar estas operaciones de forma fácil.

---

En versiones TEST se mostrará todo sin restricciones GPS. Pero en versiones de producción o que no sean de TEST, sólo se mostrarán los municiipos donde esté situada la posición GPS del dispositivo.

"""),
            ),
            TextFormFieldER(
              title: tt(TTShared.buscar),
              controller: provider.searchController,
              icon: FontAwesomeIcons.magnifyingGlass,
            ),
            Expanded(
              child: StreamBuilder(
                stream: provider.rebuildSearch.stream,
                builder: (BuildContext context, AsyncSnapshot snapshot) {
                  final List<MunicipalityModel> muns =
                      provider.municipalities.where2(
                    (s) => s.name.toLowerCase().removeAccents().contains(
                          provider.searchController.text
                              .toLowerCase()
                              .removeAccents(),
                        ),
                  );
                  if (muns.isEmpty)
                    return Center(
                      child: H2Text(
                        tt(TTShared.noSeHanEncontradoResultados),
                        textAlign: TextAlign.center,
                      ),
                    );
                  return Scrollbar(
                    thumbVisibility: true,
                    trackVisibility: true,
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Container(
                        padding: const EdgeInsets.only(
                            right: 10, top: 20, bottom: 0),
                        width: double.maxFinite,
                        child: Wrap(
                          spacing: 20,
                          runSpacing: 20,
                          alignment: WrapAlignment.spaceAround,
                          children: muns.map2(
                            (e) => MunicipalityItemList(e, provider),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            Container(
              width: double.maxFinite,
              alignment: Alignment.centerRight,
              child: Container(
                width: ThemeER.dialogConfirmButtonWidth.get(),
                child: ButtonER(
                    title: tt(TTShared.confirmar),
                    color: themeColors.secondaryGreen,
                    colorSplash: themeColors.white,
                    colorText: themeColors.white,
                    icon: FontAwesomeIcons.solidCircleCheck,
                    onPressed: provider.onConfirmPressed),
              ),
            ),
          ],
        );
      },
    );
  }
}
