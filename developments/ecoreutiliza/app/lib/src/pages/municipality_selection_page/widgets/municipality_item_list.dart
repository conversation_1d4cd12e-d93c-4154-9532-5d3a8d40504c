import '../../../_imports.dart';

class MunicipalityItemList extends StatelessWidget {
  final MunicipalityModel model;
  final MunicipalitySelectionProvider provider;

  MunicipalityItemList(
    this.model,
    this.provider,
  );

  @override
  Widget build(BuildContext context) {
    final double width = 250;

    bool hasWhiteLogo = model.id == 8 || model.id == 10;

    return Container(
      // height: 280,
      width: MediaQuery.of(context).size.width <= width * 2
          ? double.maxFinite
          : width,
      alignment: Alignment.center,
      child: Transform.scale(
          scale: _getSize(),
          child: GestureDetector(
            onTap: () => provider.onServerPressed(model),
            child: Card(
              elevation: ThemeER.elevation,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(ThemeER.radius),
                side: BorderSide(
                  color: _getBorderColor(),
                  width: _getBorderWidth(),
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(ThemeER.radius),
                child: Container(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        height: 150,
                        padding: ThemeER.cardPadding.get(),
                        color: hasWhiteLogo ? themeColors.grey4 : null,
                        child: isStringNullOrEmpty(model.image)
                            ? Image.asset("assets/images/icon.png")
                            : ImageCacheService()
                                .getFromStringBase64(model.image),
                      ),
                      Container(
                        height: 1,
                        child:
                            hasWhiteLogo ? const SizedBox() : const Divider(),
                      ),
                      Container(
                        height: 80,
                        alignment: Alignment.center,
                        child: H3Text(
                          model.name,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      _getSubtitle(),
                    ],
                  ),
                ),
              ),
            ),
          )),
    );
  }

  Widget _getSubtitle() {
    if (model.notAvailableAppIds.contains(user.app.id))
      return Padding(
        padding: ThemeER.cardPadding.get().copyWith(top: 0),
        child: Text(tt(TTShared.noDisponible),
            style: TextStyle(color: themeColors.errorRed, fontSize: 18)),
      );

    // Solo se obtendrá modelos con esta propiedad a FALSE cuando se esté en modo testeo (debug o compilación de testeo)
    if (!model.isVisibleForCitizen)
      return Padding(
        padding: ThemeER.cardPadding.get().copyWith(top: 0),
        child: Text("Testing",
            style: TextStyle(color: themeColors.alertOrange, fontSize: 18)),
      );

    return const SizedBox.shrink();
  }

  Color _getBorderColor() {
    if (provider.selected?.id == model.id) return themeColors.validGreen;
    return Colors.white;
  }

  double _getBorderWidth() {
    if (provider.selected?.id == model.id) return 2;
    return 0;
  }

  double _getSize() {
    if (provider.selected?.id == model.id) return 1.05;
    return 1;
  }
}
