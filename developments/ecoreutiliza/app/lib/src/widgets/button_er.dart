import '../_imports.dart';

class ButtonER extends StatelessWidget {
  final double height;
  final Color? color;
  final Color? colorSplash;
  final Color? colorText;
  final IconData? icon;
  final String title;
  final Color? colorIcon;
  final void Function()? onPressed;
  final bool isVertical;
  final Alignment alignment;
  final EdgeInsets padding;

  ButtonER({
    this.height = ThemeER.formItemHeight,
    this.padding = ThemeER.formItemPadding,
    required this.title,
    required this.color,
    required this.colorSplash,
    required this.colorText,
    this.icon,
    this.colorIcon,
    this.isVertical = false,
    this.alignment = Alignment.centerRight,
    required this.onPressed,
  });
  factory ButtonER.modify(void Function() onPressed) => ButtonER.styleBlue(
        onPressed: onPressed,
        title: tt(TTShared.modificar),
        icon: FontAwesomeIcons.penToSquare,
      );

  factory ButtonER.remove(void Function() onPressed) => ButtonER.styleRed(
        onPressed: onPressed,
        title: tt(TTShared.eliminar),
        icon: FontAwesomeIcons.trash,
      );

  ButtonER.styleWhite({
    required this.onPressed,
    required this.title,
    required this.icon,
  })  : height = ThemeER.formItemHeight,
        padding = ThemeER.formItemPadding,
        color = themeColors.white,
        colorSplash = themeColors.grey4,
        colorText = themeColors.black,
        isVertical = false,
        alignment = Alignment.centerRight,
        colorIcon = ThemeER.iconColor;

  ButtonER.styleGreen({
    required this.onPressed,
    required this.title,
    required this.icon,
  })  : height = ThemeER.formItemHeight,
        padding = ThemeER.formItemPadding,
        color = themeColors.secondaryGreen,
        colorSplash = themeColors.white,
        isVertical = false,
        alignment = Alignment.centerRight,
        colorText = themeColors.white,
        colorIcon = themeColors.white;

  ButtonER.styleBlue({
    required this.onPressed,
    required this.title,
    required this.icon,
  })  : height = ThemeER.formItemHeight,
        padding = ThemeER.formItemPadding,
        color = themeColors.primaryBlue,
        colorSplash = themeColors.white,
        alignment = Alignment.centerRight,
        colorText = themeColors.white,
        isVertical = false,
        colorIcon = themeColors.white;

  ButtonER.styleRed({
    required this.onPressed,
    required this.title,
    required this.icon,
  })  : height = ThemeER.formItemHeight,
        padding = ThemeER.formItemPadding,
        color = themeColors.errorRed,
        colorSplash = themeColors.white,
        colorText = themeColors.white,
        isVertical = false,
        alignment = Alignment.centerRight,
        colorIcon = themeColors.white;

  @override
  Widget build(BuildContext context) {
    final child1 = icon == null
        ? const SizedBox()
        : Icon(
            icon,
            color: colorIcon ?? colorText,
          );
    final child2 = Text(
      title,
      maxLines: 1,
      style: ThemeER.accessButtonST.copyWith(color: colorText),
      textAlign: TextAlign.center,
    );
    bool isLargeTitle = child2.data!.length > 10;
    return Container(
      alignment: alignment,
      height: height,
      child: Container(
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            surfaceTintColor: themeColors.transparent,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(ThemeER.radius)),
            backgroundColor: color ?? themeColors.white,
            elevation: ThemeER.elevation,
            padding: padding,
          ),
          child: !isStringFilled(title)
              ? Center(child: child1)
              : isVertical
                  ? Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        child1,
                        child2,
                      ],
                    )
                  : Row(
                      children: [
                        child1,
                        SizedBox(
                          width: isLargeTitle ? 8 : 0,
                        ),
                        Expanded(
                          child: Center(
                            child: Text(
                              child2.data!,
                              style: child2.style!.copyWith(
                                  fontSize: isLargeTitle
                                      ? 16
                                      : child2.style!.fontSize),
                            ),
                          ),
                        ),
                      ],
                    ),
          onPressed: onPressed,
        ),
      ),
    );
  }
}

class ButtonERSubtitle extends StatelessWidget {
  final Color? color;
  final Color? colorSplash;
  final Color? colorText;
  final IconData? icon;
  final String text;
  final String subtitle;
  final void Function() onPressed;

  const ButtonERSubtitle({
    Key? key,
    required this.text,
    required this.color,
    required this.colorSplash,
    required this.colorText,
    required this.subtitle,
    this.icon,
    required this.onPressed,
  }) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: const BoxConstraints(minHeight: 80),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ThemeER.radius)),
          backgroundColor: color,
          elevation: ThemeER.elevation,
          foregroundColor: colorSplash,
          padding: ThemeER.formItemPadding,
        ),
        child: Row(
          children: [
            icon == null
                ? const SizedBox()
                : Container(
                    margin: const EdgeInsets.only(right: 20),
                    child: Icon(
                      icon,
                      color: colorText,
                    ),
                  ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    text,
                    maxLines: 1,
                    style: ThemeER.accessButtonST.copyWith(
                      color: colorText,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 5),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: themeColors.grey7,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        onPressed: () => onPressed(),
      ),
    );
  }
}
