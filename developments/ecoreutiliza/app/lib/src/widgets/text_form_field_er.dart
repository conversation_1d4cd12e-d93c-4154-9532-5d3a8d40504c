import '../_imports.dart';

class TextFormFieldER extends StatelessWidget {
  final String title;
  final TextEditingController controller;
  final IconData icon;
  final bool isTextArea;
  final TextInputType textInputType;
  final bool isEditable;
  final bool isObscureText;
  final String hintText;

  const TextFormFieldER({
    Key? key,
    required this.title,
    required this.controller,
    required this.icon,
    this.isTextArea = false,
    this.isEditable = true,
    this.textInputType = TextInputType.text,
    this.isObscureText = false,
    this.hintText = '',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Card(
        margin: EdgeInsets.zero,
        elevation: ThemeER.elevation,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeER.radius)),
        child: Container(
          height: isTextArea
              ? ThemeER.formItemHeight * 5
              : hintText.isEmpty
                  ? ThemeER.formItemHeight
                  : ThemeER.formItemHeight * 1.2,
          padding: ThemeER.formItemPadding.copyWith(top: 0, bottom: 0),
          child: TextFormField(
            key: Key(title),
            obscureText: isObscureText,
            enabled: isEditable,
            controller: controller,
            maxLines: isTextArea ? null : 1,
            keyboardType: isTextArea ? TextInputType.multiline : textInputType,
            decoration: InputDecoration(
              border: InputBorder.none,
              contentPadding: const EdgeInsets.only(top: 2.5, bottom: 10),
              hintText: hintText.isEmpty ? null : hintText,
              labelStyle: TextStyle(color: themeColors.grey6, fontSize: 14),
              labelText: title.isEmpty ? null : title,
              hintMaxLines: 5,
              icon: Icon(icon, color: themeColors.grey6),
            ),
          ),
        ),
      ),
    );
  }
}

class TextFormFieldERValitation extends StatefulWidget {
  final String title;
  final TextEditingController controller;
  final IconData icon;
  final bool isTextArea;
  final TextInputType textInputType;
  final bool isEditable;
  final bool isObscureText;
  final String hintText;
  final List<ValidatorBase<String>> validators;

  const TextFormFieldERValitation({
    Key? key,
    required this.title,
    required this.controller,
    required this.icon,
    this.isTextArea = false,
    this.isEditable = true,
    this.textInputType = TextInputType.text,
    this.isObscureText = false,
    this.hintText = '',
    this.validators = const [],
  }) : super(key: key);

  @override
  State<TextFormFieldERValitation> createState() =>
      _TextFormFieldERValitationState();
}

class _TextFormFieldERValitationState extends State<TextFormFieldERValitation> {
  String error = "";

  void _validate(String value) {
    // Comprobamos todos los validadores
    if (!isListNullOrEmpty(widget.validators)) {
      for (var validator in widget.validators) {
        final result = validator.evaluate(value);
        if (result.isValid) continue;
        error = result.error ?? "";
        setState(() {});
        return;
      }
    }

    // If previously there wasn't a error, we don't rebuild the widget.
    if (error.isEmpty) return;

    error = "";
    setState(() {});
  }

  void initState() {
    super.initState();
    widget.controller.addListener(() {
      _validate(widget.controller.text);
    });
  }

  void dispose() {
    widget.controller.removeListener(() {
      _validate(widget.controller.text);
    });
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      Card(
        margin: EdgeInsets.zero,
        elevation: ThemeER.elevation,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeER.radius)),
        child: Container(
          height: widget.isTextArea
              ? ThemeER.formItemHeight * 5
              : ThemeER.formItemHeight,
          padding: ThemeER.formItemPadding.copyWith(top: 0, bottom: 0),
          child: TextFormField(
            key: Key(widget.title),
            obscureText: widget.isObscureText,
            enabled: widget.isEditable,
            controller: widget.controller,
            maxLines: widget.isTextArea ? null : 1,
            keyboardType: widget.isTextArea
                ? TextInputType.multiline
                : widget.textInputType,
            decoration: InputDecoration(
              border: InputBorder.none,
              hintText:
                  widget.hintText.isEmpty ? widget.title : widget.hintText,
              labelText: widget.hintText.isEmpty ? null : widget.title,
              icon: Icon(widget.icon, color: themeColors.grey6),
            ),
          ),
        ),
      ),
      FadeInOutContainer(
        getIsShowed: () => widget.validators.isNotEmpty && error.isFilled,
        child: CalloutContainerER.error(error),
        isRemovedWhenNotShowed: true,
        margin: const EdgeInsets.only(top: 10),
        delayIn: const Duration(milliseconds: 300),
      ),
    ]);
  }
}

class LabelER extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color? iconColor;
  final int? maxLines;

  const LabelER({
    Key? key,
    required this.title,
    required this.icon,
    this.iconColor,
    this.maxLines,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 15,
            color: iconColor ?? ThemeER.iconColor,
          ),
          const SizedBox(width: 10),
          Flexible(
            child: Text(
              title,
              maxLines: maxLines ?? 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
