import '../_imports.dart';

class TableParentFlexible extends StatelessWidget {
  final Widget child;
  final double? maxHeight;
  final double? maxWidth;
  final Color? color;

  TableParentFlexible({
    required this.child,
    this.maxHeight,
    this.maxWidth,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (_, constraints) {
      return SingleChildScrollView(
        scrollDirection: Axis.vertical,
        physics: const BouncingScrollPhysics(),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          physics: const BouncingScrollPhysics(),
          child: ConstrainedBox(
            constraints: BoxConstraints(minWidth: constraints.maxWidth),
            child: child,
          ),
        ),
      );
    });
  }
}
