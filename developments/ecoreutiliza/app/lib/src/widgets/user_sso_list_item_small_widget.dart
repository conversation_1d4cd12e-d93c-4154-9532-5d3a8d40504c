import '../_imports.dart';

class UserSSOListItemSmallWidget extends StatelessWidget {
  static final avatarSize = ResponsiveData<double>(
    mobileNormal: 50,
    defaultData: 50,
  );
  final UserSSOModel userSSO;
  final void Function() onPressed;
  final void Function()? onLongPressed;

  UserSSOListItemSmallWidget({
    required this.userSSO,
    required this.onPressed,
    this.onLongPressed,
  });

  @override
  Widget build(BuildContext context) {
    return FullWidthButtonItemListV2(
      onPressed: onPressed,
      onLongPressed: onLongPressed,
      child: Row(
        children: <Widget>[
          Container(
            width: avatarSize.get(),
            child: isStringNullOrEmpty(userSSO.imageMd5)
                ? const FittedBox(
                    child: Padding(
                      padding: EdgeInsets.all(5),
                      child: Icon(FontAwesomeIcons.circleUser),
                    ),
                  )
                : ImageMd5ReadWidget(
                    md5: userSSO.imageMd5,
                    getImage: userSSOUseCase.getImageByMd5,
                  ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  child: H1Text(
                    userSSO.name,
                    textAlign: TextAlign.left,
                    maxLines: 1,
                    color: themeColors.secondaryGreenDark,
                  ),
                ),
                Container(
                  alignment: Alignment.centerLeft,
                  child: H3Text(
                    userSSO.roleName ?? "",
                    textAlign: TextAlign.left,
                    maxLines: 1,
                    color: themeColors.secondaryGreenDark,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
