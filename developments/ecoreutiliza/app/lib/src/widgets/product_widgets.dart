import '../_imports.dart';

class CleanPointSearchItemList extends StatelessWidget {
  final CleanPointBuildingModel? m;
  final void Function()? onPressed;

  CleanPointSearchItemList([this.m, this.onPressed]);

  @override
  Widget build(BuildContext context) {
    final address = m?.getAddress(zoneManager.modelsMap) ?? "";
    return GestureDetector(
      onTap: onPressed,
      child: CardER(
        child: Container(
          height: 95,
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(ThemeER.radius),
                child: Container(
                  height: double.maxFinite,
                  width: 80,
                  child: m == null
                      ? FittedBox(
                          child: Padding(
                              padding: const EdgeInsets.all(5),
                              child: Icon(FontAwesomeIcons.solidCircleQuestion,
                                  color: ThemeER.iconColor)))
                      : m!.imageWidget,
                ),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: m == null
                    ? H2Text(
                        tt(TTShared.noSeHaEncontrado),
                        isUpperCase: false,
                        maxLines: 1,
                      )
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          H2Text(
                            m!.name,
                            maxLines: 2,
                          ),
                          if (address.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(right: 8),
                              child: LabelER(
                                  title: address,
                                  icon: FontAwesomeIcons.locationDot),
                            )
                        ],
                      ),
              ),
              onPressed == null
                  ? const SizedBox()
                  : Icon(
                      FontAwesomeIcons.caretRight,
                      color: themeColors.grey4,
                    ),
              SizedBox(width: responsive.isMobile ? 0 : 10),
            ],
          ),
        ),
      ),
    );
  }
}

class UserSearchItemList extends StatelessWidget {
  final UserSSOModel? m;
  final void Function()? onPressed;

  UserSearchItemList([this.m, this.onPressed]);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: CardER(
        margin: const EdgeInsets.only(bottom: 8),
        child: Container(
          width: double.infinity,
          height: 80,
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(ThemeER.radius),
                child: Container(
                  height: double.maxFinite,
                  width: 80,
                  child: m == null
                      ? FittedBox(
                          child: Padding(
                            padding: const EdgeInsets.all(5),
                            child: Icon(FontAwesomeIcons.solidCircleQuestion,
                                color: ThemeER.iconColor),
                          ),
                        )
                      : m!.imageWidget,
                ),
              ),
              const SizedBox(width: 12),
              m == null
                  ? Center(
                      child: H2Text(
                        tt(TTShared.noSeHaEncontrado),
                        isUpperCase: false,
                        maxLines: 1,
                      ),
                    )
                  : Flexible(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          H2Text(
                            m!.name,
                            isUpperCase: false,
                            maxLines: 1,
                          ),
                          _field(m!.email, FontAwesomeIcons.solidEnvelope),
                        ],
                      ),
                    ),
              onPressed == null ? const SizedBox() : const Spacer(),
              onPressed == null
                  ? const SizedBox()
                  : Icon(
                      FontAwesomeIcons.caretRight,
                      color: themeColors.grey4,
                    ),
              responsive.isMobile
                  ? const SizedBox()
                  : const SizedBox(width: 10),
            ],
          ),
        ),
      ),
    );
  }

  Widget _field(String text, IconData icon) => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(icon, size: 12),
          const SizedBox(width: 10),
          BodyText(text, maxLines: 2),
        ],
      );
}

class ProductCategorySearchItemList extends StatelessWidget {
  final ProductCategoryModel m;

  const ProductCategorySearchItemList(this.m);

  @override
  Widget build(BuildContext context) {
    return CardER(
      child: Container(
        height: 50,
        alignment: Alignment.centerLeft,
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.only(left: 20),
              child: m.iconWidget,
            ),
            const SizedBox(width: 20),
            H2Text(m.type.tt, isUpperCase: false),
            const Spacer(),
            Icon(
              FontAwesomeIcons.caretRight,
              color: themeColors.grey4,
            ),
            const SizedBox(width: 10),
          ],
        ),
      ),
    );
  }
}
