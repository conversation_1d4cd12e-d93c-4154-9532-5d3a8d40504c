import '../_imports.dart';

class EventItemDrawer extends StatelessWidget {
  final void Function() onPressed;

  EventItemDrawer(this.onPressed) {
    eventManager.start();
  }

  @override
  Widget build(BuildContext context) {
    return ItemDrawer(
      text: tt(TTShared.notificaciones),
      icon: FontAwesomeIcons.solidBell,
      function: (_) => onPressed(),
      onChange: eventManager.onChange.map((event) => null),
      getNotificationCount: () async =>
          eventManager.models.where2((m) => !m.isSeen).length,
      color: themeColors.white,
      colorIcon: themeColors.white,
      colorShadow: themeColors.transparent,
      spacingTextIcon: ThemeER.spaceingDrawerIconText,
    );
  }
}

class ProductRequestItemDrawer extends StatelessWidget {
  final void Function() onPressed;

  ProductRequestItemDrawer(this.onPressed) {
    eventManager.start();
  }

  @override
  Widget build(BuildContext context) {
    return ItemDrawer(
      text: tt(TTProduct.solicitudesDeEntrega),
      icon: FontAwesomeIcons.rightToBracket,
      function: (_) => onPressed(),
      onChange: productRequestManager.onChange.map((event) => null),
      getNotificationCount: () async => productRequestManager.models
          .where2((m) => m.isNeccesaryNotify(user.role))
          .length,
      color: themeColors.white,
      colorIcon: themeColors.white,
      colorShadow: themeColors.transparent,
      spacingTextIcon: ThemeER.spaceingDrawerIconText,
    );
  }
}

class ProductWithdrawalRequestItemDrawer extends StatelessWidget {
  final void Function() onPressed;

  ProductWithdrawalRequestItemDrawer(this.onPressed) {
    eventManager.start();
  }

  @override
  Widget build(BuildContext context) {
    return ItemDrawer(
      text: tt(TTProduct.solicitudesDeRetiro),
      icon: FontAwesomeIcons.rightFromBracket,
      function: (_) => onPressed(),
      onChange: productWithdrawalRequestManager.onChange.map((event) => null),
      getNotificationCount: () async => productWithdrawalRequestManager.models
          .where2((m) => m.isNeccesaryNotify(user.role))
          .length,
      color: themeColors.white,
      colorIcon: themeColors.white,
      colorShadow: themeColors.transparent,
      spacingTextIcon: ThemeER.spaceingDrawerIconText,
    );
  }
}
