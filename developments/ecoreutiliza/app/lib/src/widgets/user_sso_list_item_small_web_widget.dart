import '../_imports.dart';

class UserSSOListItemSmallWebWidget extends StatelessWidget {
  final UserSSOModel userSSO;
  final void Function() onPressed;

  UserSSOListItemSmallWebWidget({
    required this.userSSO,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          const SizedBox(width: 10),
          Container(
            width: 20,
            padding: const EdgeInsets.all(3),
            child: userSSO.imageWidget,
          ),
          const SizedBox(width: 10),
          BodyText(
            "${userSSO.id} - ${userSSO.name}",
            textAlign: TextAlign.left,
            maxLines: 1,
            color: themeColors.secondaryGreenDark,
          ),
        ],
      ),
    );
  }
}
