import '../_imports.dart';

class CardER extends StatelessWidget {
  final Widget child;
  final EdgeInsets? margin;
  final EdgeInsets? padding;
  final Color? color;
  final bool isFlat;

  CardER({
    required this.child,
    this.margin,
    this.padding,
    this.color,
    this.isFlat = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: margin ?? const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
      color: color ?? themeColors.white,
      elevation: isFlat ? 0 : ThemeER.elevation,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ThemeER.radius)),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(ThemeER.radius),
        child: Container(
          color: color ?? themeColors.white,
          padding: padding ?? ThemeER.cardPadding.get(),
          child: child,
        ),
      ),
    );
  }
}
