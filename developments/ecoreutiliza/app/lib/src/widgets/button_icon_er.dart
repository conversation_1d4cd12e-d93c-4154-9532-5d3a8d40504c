import '../_imports.dart';

class ButtonIconER extends StatelessWidget {
  final double size;
  final double? sizeIcon;
  final Color? color;
  final Color? colorIcon;
  final Color? colorSplash;
  final IconData icon;
  final String tooltip;
  final void Function() onPressed;
  final EdgeInsets iconPadding;

  ButtonIconER({
    Key? key,
    this.size = ThemeER.formItemHeight,
    this.sizeIcon,
    required this.tooltip,
    required this.color,
    required this.colorIcon,
    required this.colorSplash,
    required this.icon,
    required this.onPressed,
    this.iconPadding = EdgeInsets.zero,
  }) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Container(
      height: size,
      width: size,
      child: TooltipCustom(
        message: tooltip,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(ThemeER.radius)),
            backgroundColor: color,
            elevation: ThemeER.elevation,
            padding: EdgeInsets.zero,
            foregroundColor: colorSplash,
          ),
          child: Container(
            alignment: Alignment.center,
            padding: iconPadding,
            child: FittedBox(
              child: Icon(
                icon,
                color: colorIcon,
                size: sizeIcon ?? (size - 20),
              ),
            ),
          ),
          onPressed: () => onPressed(),
        ),
      ),
    );
  }
}
