import '../_imports.dart';

class DropdownER extends StatelessWidget {
  final String title;
  final IconData icon;
  final String? selected;
  final void Function() onPressed;
  final double width;

  const DropdownER({
    Key? key,
    required this.title,
    required this.icon,
    required this.onPressed,
    required this.selected,
    this.width = 300,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String showedOption = isStringFilled(selected) ? selected! : title;
    return Container(
      width: width,
      height: ThemeER.formItemHeight,
      child: GestureDetector(
        onTap: () => onPressed(),
        child: TooltipCustom(
          message: title,
          child: CardER(
            margin: EdgeInsets.zero,
            child: Row(
              children: [
                const SizedBox(width: 10),
                Icon(
                  icon,
                  color: themeColors.grey6,
                ),
                const SizedBox(width: 15),
                Text(
                  showedOption,
                  maxLines: 1,
                  textAlign: TextAlign.center,
                ),
                const Spacer(),
                Icon(
                  FontAwesomeIcons.caretDown,
                  color: themeColors.grey4,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// class DropdownER extends StatelessWidget {
//   final String title;
//   final IconData icon;
//   final List<String> Function() getSelectdOptions;
//   final int Function() getTotal;
//   final void Function() onPressed;
//   final double width;

//   const DropdownER({
//     Key key,
//     required this.title,
//     required this.icon,
//     required this.getSelectdOptions,
//     required this.getTotal,
//     required this.onPressed,
//     this.width = 300,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     final total = getTotal() ?? 0;
//     // if (isNullLesserOrEqualZero(total)) return SizedBox();
//     String showedOption = title;
//     final selecteds = getSelectdOptions();
//     bool isMultiple = false;
//     if (isListFilled(selecteds)) {
//       if (selecteds.length == 1)
//         showedOption = selecteds.first;
//       else {
//         isMultiple = true;
//         showedOption = "${selecteds.length} / $total";
//       }
//     }
//     return Container(
//       width: width,
//       height: ThemeER.formItemHeight,
//       child: GestureDetector(
//         onTap: () => onPressed(),
//         child: TooltipCustom(
//           message: title,
//           child: CardER(
//             margin: EdgeInsets.zero,
//             child: Row(
//               children: [
//                 SizedBox(width: 10),
//                 Icon(
//                   icon,
//                   color: themeColors.grey6,
//                 ),
//                 SizedBox(width: 15),
//                 Text(
//                   showedOption,
//                   maxLines: 1,
//                   textAlign: TextAlign.center,
//                 ),
//                 Spacer(),
//                 Icon(
//                   FontAwesomeIcons.caretDown,
//                   color: themeColors.grey4,
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }
