import 'package:dots_indicator/dots_indicator.dart';

import '../_imports.dart';

abstract class ProviderArrowsCarouselER extends ProviderBaseV2 {
  List<String> images = [];
  List<Widget> widgets = [];
  int carouselIndex = 0;
  // CarouselController carouselController;
  void onChangeOrderImages({bool? right}) {
    final item = images[carouselIndex];
    images.removeAt(carouselIndex);
    final modifier = right!
        ? carouselIndex == images.length
            ? -images.length
            : 1
        : carouselIndex == 0
            ? images.length
            : -1;
    images.insert(carouselIndex + modifier, item);
    widgets.clear();
    rebuild();
  }

  void onImageChanged(String? img, int index) {
    if (isStringNullOrEmpty(img)) {
      if (index >= 0 && index < images.length) images.removeAt(index);
      if (carouselIndex > 0) carouselIndex--;
    } else {
      images.add(img!);
    }
    widgets.clear();
    rebuild();
  }

  List<Widget> getImageWidget({bool isEditable = true}) {
    if (isListFilled(widgets)) return widgets;
    if (isListFilled(images)) {
      for (var i = 0; i < images.length; i++) {
        Widget img = FadeInContainer(
          child: ImageER(
            image: images[i],
            isEditable: isEditable,
            onChangedImage: (img) => onImageChanged(img, i),
          ),
        );

        if (!isEditable)
          img = InkWell(
            onTap: () => showDialogImage(images[i]),
            child: img,
          );

        widgets.add(img);
      }
    }

    return widgets;
  }

  void onChangedIndex(int i) {
    carouselIndex = i;
    rebuild();
  }
}

/// El provider que define la lógica de este widget debe implementar la interfaz ProviderArrowsCarousel que a su vez hereda de ProviderBaseV2.
///
/// Así poseemos una lista de imágenes en Base64, un campo índice de manera que sepamos en qué posición del carrusel se encuentra el usuario (se debe actualizar en el onPageChanged de CarouselOptions), un controlador de carrusel para la animación y funciones para realizar la ordenación, el cambio de imagen y la creación del widget de imagen.
class ChangeImageOrderArrows extends StatelessWidget {
  final List<String?> images;
  final int index;
  final void Function({bool right})? onChangeOrderImages;

  ChangeImageOrderArrows({
    required this.images,
    required this.index,
    this.onChangeOrderImages,
  });

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: images.length > 1,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 50),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            GestureDetector(
              child: const Icon(FontAwesomeIcons.arrowRotateLeft),
              onTap: () => onChangeOrderImages!(right: false),
            ),
            Expanded(
              child: CarouselDots(
                images: images,
                index: index,
              ),
            ),
            GestureDetector(
              child: const Icon(FontAwesomeIcons.arrowRotateRight),
              onTap: () => onChangeOrderImages!(right: true),
            )
          ],
        ),
      ),
    );
  }
}

class CarouselDots extends StatelessWidget {
  final List<String?>? images;
  final int index;

  CarouselDots({
    required this.images,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    if (isNullLesserOrEqualZero(images?.length)) return const SizedBox();
    return Center(
      child: DotsIndicator(
        dotsCount: images!.length,
        position: index.toDouble(),
        onTap: (i) {},
        decorator: DotsDecorator(
          activeColor: themeColors.black,
          size: const Size.square(9.0),
          activeSize: const Size(18.0, 9.0),
          activeShape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.0)),
        ),
      ),
    );
  }
}
