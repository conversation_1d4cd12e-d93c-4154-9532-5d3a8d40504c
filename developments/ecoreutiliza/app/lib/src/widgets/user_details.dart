import '../_imports.dart';

class UserDetails extends StatelessWidget {
  final UserSSOModel user;
  final void Function() onPressed;
  final bool isEditable;

  const UserDetails({
    required this.user,
    required this.onPressed,
    required this.isEditable,
  });

  @override
  Widget build(BuildContext context) {
    return CardButtonItemList(
      height: 60,
      color: themeColors.white,
      onPressed: isEditable ? onPressed : null,
      child: Row(
        children: [
          user.imageMd5.isNullOrEmpty
              ? const SizedBox()
              : Container(
                  width: 60,
                  child: ImageMd5ReadWidget(
                    md5: user.imageMd5,
                    getImage: userSSOUseCase.getImageByMd5,
                  ),
                ),
          SizedBox(width: !isEditable ? 10 : 0),
          Expanded(
            child: Container(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: <Widget>[
                  H1Text(user.name, maxLines: 1),
                  Flexible(
                    child: H4Text(user.email, maxLines: 1),
                  ),
                ],
              ),
            ),
          ),
          isEditable
              ? Container(
                  width: 40,
                  child: Icon(
                    FontAwesomeIcons.caretDown,
                    color: themeColors.grey6,
                  ))
              : const SizedBox()
        ],
      ),
    );
  }
}
