import 'package:flutter/cupertino.dart'; //

import '../_imports.dart';

class SwitchButtonER extends StatelessWidget {
  final String title;
  final IconData? icon;
  final bool Function() isSelected;
  final void Function() onPressed;

  const SwitchButtonER({
    Key? key,
    required this.title,
    this.icon,
    required this.isSelected,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onPressed(),
      child: TooltipCustom(
        message: title,
        child: Container(
            height: ThemeER.formItemHeight,
            child: Row(
              children: [
                SizedBox(width: icon == null ? 0 : 10),
                icon == null
                    ? const SizedBox()
                    : Icon(
                        icon,
                        color: themeColors.grey6,
                      ),
                const SizedBox(width: 15),
                Text(
                  title,
                  maxLines: 1,
                  textAlign: TextAlign.center,
                ),
                const Spacer(),
                IgnorePointer(
                  child: CupertinoSwitch(
                    activeColor: themeColors.secondaryGreen,
                    value: isSelected(),
                    onChanged: (value) {},
                  ),
                ),
              ],
            )),
      ),
    );
  }
}
