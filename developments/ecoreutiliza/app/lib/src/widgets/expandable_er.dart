import '../_imports.dart';

class ExpandableER extends StatelessWidget {
  final String title;
  final List<Widget> children;

  const ExpandableER({
    super.key,
    required this.title,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
      collapsedShape: const RoundedRectangleBorder(),
      childrenPadding: EdgeInsets.zero,
      tilePadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 10),
      shape: ShapeBorder.lerp(
          RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ThemeER.radius)),
          RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ThemeER.radius)),
          1),
      title: H2Text(title),
      controlAffinity: ListTileControlAffinity.trailing,
      maintainState: true,
      initiallyExpanded: false,
      iconColor: themeColors.secondaryGreen,
      textColor: themeColors.secondaryGreen,
      children: children,
    );
  }
}
