import '../_imports.dart';

class CleanPointCard extends StatelessWidget {
  static final _buttonPadding = ResponsiveData<double>(
    mobileDefault: 0,
    defaultData: 10,
  );
  final bool isFirst;
  final bool isLast;
  final void Function() onPressed;
  final void Function() onPressedCenterInMap;

  const CleanPointCard(
      {Key? key,
      this.isFirst = false,
      this.isLast = false,
      required this.onPressed,
      required this.onPressedCenterInMap})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 200,
        margin: const EdgeInsets.all(10)
            .copyWith(left: isFirst ? 0 : 10, right: isLast ? 0 : 10),
        child: Card(
          elevation: ThemeER.elevation,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ThemeER.radius)),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(ThemeER.radius),
            child: Container(
              height: double.maxFinite,
              width: double.maxFinite,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  fit: BoxFit.cover,
                  image: NetworkImage(
                    "https://cflvdg.avoz.es/sc/ModFsOsl3ntCGWxfJJ_BUPPbDsQ=/1280x/2018/09/17/00121537211258638784356/Foto/punto.jpg",
                  ),
                ),
              ),
              child: Stack(
                children: [
                  Container(
                    alignment: Alignment.topCenter,
                    child: Container(
                      alignment: Alignment.center,
                      width: double.maxFinite,
                      padding: const EdgeInsets.all(2),
                      height: 30,
                      color: themeColors.black.withOpacity(0.5),
                      child: Text(
                        "Punto límpio 1",
                        style: TextStyle(
                          color: themeColors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                      ),
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.only(
                        bottom: _buttonPadding.get(),
                        right: _buttonPadding.get()),
                    alignment: Alignment.bottomRight,
                    child: GestureDetector(
                      onTap: onPressedCenterInMap,
                      child: RoundedTooltipIconER(
                        size: 30,
                        icon: FontAwesomeIcons.locationDot,
                        iconColor: themeColors.primaryBlue,
                        title: tt(TTShared.centrarMapa),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
