import '../_imports.dart';

class LabelIconER extends StatelessWidget {
  final Widget icon;
  final String? title;
  final String? text;
  final String? tooltip;
  final Widget end;
  final double textPaddingTop;

  const LabelIconER({
    Key? key,
    required this.icon,
    required this.text,
    this.title,
    this.tooltip,
    this.textPaddingTop = 5,
    this.end = const SizedBox(),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final r = Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        const SizedBox(width: 10),
        Container(
          height: 20,
          child: FittedBox(
            child: icon,
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(top: textPaddingTop),
            child: Text(
              title?.colon(text!) ?? text!,
              style: const TextStyle(
                fontSize: 16,
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ),
        const SizedBox(width: 10),
        end,
      ],
    );

    if (isStringNullOrEmpty(tooltip)) return r;

    return TooltipCustom(message: tooltip!, child: r);
  }
}
