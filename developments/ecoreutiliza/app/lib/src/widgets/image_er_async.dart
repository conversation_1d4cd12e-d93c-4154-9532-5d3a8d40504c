import '../_imports.dart';

class ImageERAsync extends StatefulWidget {
  final Future<String> Function() getImage;
  final double? opacity;
  ImageERAsync(this.getImage, {this.opacity});

  @override
  State<ImageERAsync> createState() => _ImageERAsyncState();
}

class _ImageERAsyncState extends State<ImageERAsync> {
  String? image;

  Future<void> _getImage() async {
    if (!mounted) return;
    String newImage = await widget.getImage();
    if (isStringNullOrEmpty(newImage)) return;
    if (image == newImage) return;
    image = newImage;
    if (!mounted) return;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    if (!connection.hasInternet && isStringNullOrEmpty(image)) {
      return Center(
        child: IconButton(
          onPressed: () {
            setState(() {});
          },
          icon: Icon(
            FontAwesomeIcons.rotate,
            color: themeColors.grey4,
          ),
        ),
      );
    }

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _getImage();
    });

    if (isStringNullOrEmpty(image)) {
      return Center(
        child: Icon(
          FontAwesomeIcons.circleXmark,
          color: themeColors.grey4,
          size: 40,
        ),
      );
    }

    return Image(
      opacity: AlwaysStoppedAnimation(widget.opacity ?? 1.0),
      image: ImageCacheService().getAssetStringBase64(image)!,
      fit: BoxFit.cover,
    );
  }
}
