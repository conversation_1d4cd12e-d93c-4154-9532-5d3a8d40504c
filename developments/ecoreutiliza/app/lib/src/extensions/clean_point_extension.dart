import '../_imports.dart';

extension CleanPointBuildingModelExt on CleanPointBuildingModel {
  String get matchText => "${name.toLowerCase()}";

  Widget get imageWidget {
    if (isListNullOrEmpty(imagesOrIdsMd5) ||
        isStringNullOrEmpty(imagesOrIdsMd5.first)) {
      return Center(
        child: Icon(
          FontAwesomeIcons.circleXmark,
          color: themeColors.grey4,
          size: 40,
        ),
      );
    }

    return ImageERAsync(
        () => cleanPointBuildingUseCase.getImage(imagesOrIdsMd5.first));
  }

  MarkerCustom getMarkerBuilding(void Function() onPressed) {
    final position = getEntrancePosition(zoneManager.modelsMap);
    return getMarker(
      hasEcoReutiliza: hasEcoReutiliza,
      totalCategoriesOrResidues: productCategoryManager.models.length,
      totalAcceptedCategoriesOrResidues: categoriesAccepted.length,
      // Por ahora no se muestran marcadores de PL habiendo zonas
      isVisible: () => false,
      latitude: position?.lat ?? 0,
      longitude: position?.long ?? 0,
      onPressed: onPressed,
    );
  }
}

extension CleanPointProductCategoryAcceptedModelExt
    on CleanPointProductCategoryAcceptedModel {
  ProductCategoryModel? get category =>
      productCategoryManager.getByPk(categoryId);
}

extension ManagerProductCategoryExt on ManagerCache<ProductCategoryModel, int> {
  Map<int, bool> getCategorySelectionMap() {
    Map<int, bool> selectedCategories = {};
    if (isListFilled(models))
      for (final c in models) {
        selectedCategories[c.id] = true;
      }

    return selectedCategories;
  }
}
