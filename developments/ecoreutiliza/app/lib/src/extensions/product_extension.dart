import '../_imports.dart';

extension ProductModelExt on ProductModel {

  // RESERVA: Comentado temporalmente hasta que decidan de implementarlo de nuevo.
  // IconData get reservedIcon => isReservedAndMine(user.id)
  //     ? FontAwesomeIcons.userLock
  //     : FontAwesomeIcons.lock;

  bool get isFavorite => global.home?.isFavorite(this) ?? false;

  String get matchText => "${name.toLowerCase()} ${description.toLowerCase()}";

  ImageERAsync get imageWidget => ImageERAsync(() async {
        if (isListNullOrEmpty(imagesOrIdsMd5)) return "";
        return productUseCase.getImage(imagesOrIdsMd5.first);
      });
}

extension ProductStateExt2 on ProductState? {
  IconData get icon {
    if (this == null) return FontAwesomeIcons.question;
    switch (this!) {
      case ProductState.available:
        return FontAwesomeIcons.solidEye;
      case ProductState.acquired:
        return FontAwesomeIcons.checkDouble;
    }
  }
}

extension ProductListExt2 on List<ProductModel> {}
