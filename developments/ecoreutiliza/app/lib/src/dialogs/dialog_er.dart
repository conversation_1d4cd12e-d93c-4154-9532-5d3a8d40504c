import '../_imports.dart';

class CloseDialogButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomLeft,
      child: Container(
        margin: EdgeInsets.only(bottom: 10, left: 10),
        child: Material(
          color: themeColors.transparent,
          child: FloatingActionButton(
            onPressed: () {
              NavigatorService().pop();
            },
            backgroundColor: themeColors.errorRed,
            child: Icon(
              FontAwesomeIcons.xmark,
              color: themeColors.white,
              size: 22,
            ),
          ),
        ),
      ),
    );
  }
}

class DialogER extends StatelessWidget {
  final Widget child;
  final String? title;
  final bool isScrolling;
  final Color? colorBackground;
  // final bool isShowedCloseButton;
  final Widget action;

  const DialogER({
    Key? key,
    required this.child,
    this.title,
    this.isScrolling = true,
    this.colorBackground,
    this.action = const SizedBox(),
    // this.isShowedCloseButton = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // bool isShowedClosed = isShowedCloseButton && !responsive.isWeb;
    final body = Container(
      padding: ThemeER.dialogPadding.get(),
      margin: /*!isShowedClosed ? null : */ EdgeInsets.only(bottom: 70),
      decoration: BoxDecoration(
        color: colorBackground ?? themeColors.greyBackground,
        borderRadius: BorderRadius.all(Radius.circular(ThemeER.radius)),
      ),
      constraints: BoxConstraints(maxWidth: 700),
      child: isStringNullOrEmpty(title)
          ? child
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    dialogERTitle(title!),
                    action,
                  ],
                ),
                SeparatorFiori(),
                child,
              ],
            ),
    );

    final dialog = Dialog(
      backgroundColor: themeColors.transparent,
      insetPadding: EdgeInsets.zero,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(ThemeER.radius),
        child: !isScrolling
            ? body
            : SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                child: body,
              ),
      ),
    );

    // if (!isShowedClosed) return SafeArea(child: dialog);

    return SafeArea(
      child: Stack(
        children: [
          dialog,
          Positioned(
            top: 10,
            right: 20,
            child: CloseDialogButton(),
          ),
        ],
      ),
    );
  }
}

Widget dialogERTitle(String title) => Container(
      alignment: Alignment.centerLeft,
      child: Text(
        title,
        style: ThemeER.dialogTitleST,
      ),
    );
