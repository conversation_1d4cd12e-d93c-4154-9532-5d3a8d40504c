import '../_imports.dart';

class DialogERList extends StatelessWidget {
  final String title;
  final List<Widget>? Function(String filter) builder;
  final Widget? aditionalWidget;

  const DialogERList({
    Key? key,
    required this.title,
    required this.builder,
    this.aditionalWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Dialog(
        backgroundColor: themeColors.transparent,
        insetPadding: EdgeInsets.zero,
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Container(
            padding: ThemeER.dialogPadding.get(),
            decoration: BoxDecoration(
              color: themeColors.greyBackground,
              borderRadius:
                  const BorderRadius.all(Radius.circular(ThemeER.radius)),
            ),
            constraints: const BoxConstraints(maxWidth: 700, minHeight: 400),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                CloseDialogButton(),
                dialogERTitle(title),
                SeparatorFiori(),
                DialogERListBody(
                  builder: builder,
                  aditionalWidget: aditionalWidget,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class DialogERListBody extends StatefulWidget {
  final List<Widget>? Function(String filter) builder;
  final Widget? aditionalWidget;

  const DialogERListBody({
    Key? key,
    required this.builder,
    this.aditionalWidget,
  }) : super(key: key);

  @override
  State<DialogERListBody> createState() => _DialogERListBodyState();
}

class _DialogERListBodyState extends State<DialogERListBody> {
  final controller = TextEditingController();
  final delayed =
      DelayedRenewableOperationV2(delay: const Duration(milliseconds: 150));

  @override
  void initState() {
    controller.addListener(() {
      delayed.execute(() => setState(() {}));
    });
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final widgets = widget.builder(controller.text)!;
    final field = TextFormFieldER(
      title: tt(TTShared.buscar),
      controller: controller,
      icon: FontAwesomeIcons.magnifyingGlass,
    );

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Container(
          child: !isNull(widget.aditionalWidget)
              ? Row(children: [
                  Flexible(
                    child: field,
                  ),
                  const SizedBox(width: 10),
                  widget.aditionalWidget!
                ])
              : field,
        ),
        const SizedBox(height: 20),
        ...(isListFilled(widgets)
            ? widgets
            : [H3Text(tt(TTShared.noSeHanEncontradoResultados))]),
      ],
    );
  }
}
