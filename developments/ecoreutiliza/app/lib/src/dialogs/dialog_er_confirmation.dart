import '../_imports.dart';

class DialogERConfirmation extends StatelessWidget {
  final Widget body;
  final bool isHighlightedYesButton;
  final void Function(bool isConfirmed) onResult;

  const DialogERConfirmation({
    required this.body,
    this.isHighlightedYesButton = true,
    required this.onResult,
  });

  @override
  Widget build(BuildContext context) {
    return DialogER(
      // isShowedCloseButton: false,
      child: Container(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            body,
            SeparatorFiori.oneHalf(),
            Container(
              width: double.maxFinite,
              alignment: Alignment.centerRight,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    width: 100,
                    height: 40,
                    child: ButtonER(
                      title: tt(TTShared.no),
                      color: isHighlightedYesButton
                          ? themeColors.white
                          : themeColors.secondaryGreen,
                      colorSplash: isHighlightedYesButton
                          ? themeColors.grey5
                          : themeColors.white,
                      colorText: isHighlightedYesButton
                          ? themeColors.grey5
                          : themeColors.white,
                      onPressed: () {
                        onResult(false);
                        NavigatorService().pop();
                      },
                    ),
                  ),
                  const SizedBox(width: 40),
                  Container(
                    width: 100,
                    height: 40,
                    child: ButtonER(
                      title: tt(TTShared.si),
                      color: !isHighlightedYesButton
                          ? themeColors.white
                          : themeColors.secondaryGreen,
                      colorSplash: !isHighlightedYesButton
                          ? themeColors.grey5
                          : themeColors.white,
                      colorText: !isHighlightedYesButton
                          ? themeColors.grey5
                          : themeColors.white,
                      onPressed: () {
                        onResult(true);
                        NavigatorService().pop();
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
