import '../_imports.dart';

class DialogERLoading extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: themeColors.transparent,
      insetPadding: EdgeInsets.zero,
      child: Center(
        child: Container(
          width: 80,
          height: 80,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: themeColors.greyBackground,
            borderRadius:
                const BorderRadius.all(Radius.circular(ThemeER.radius)),
          ),
          child: LoadingContainer(themeColors.primaryBlue),
        ),
      ),
    );
  }
}
