import '../_imports.dart';

class DialogERTextField extends StatefulWidget {
  final String description;
  final String? buttonTitle;
  final IconData icon;
  final Widget? additionalWidget;
  final void Function(Cancellable<String> result) onCompleted;

  const DialogERTextField({
    required this.onCompleted,
    required this.description,
    required this.icon,
    this.buttonTitle,
    this.additionalWidget,
  });

  @override
  State<DialogERTextField> createState() => _DialogERTextFieldState();
}

class _DialogERTextFieldState extends State<DialogERTextField> {
  final field = TextEditingController();

  @override
  void dispose() {
    field.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DialogER(
      child: WillPopScope(
        onWillPop: () async {
          // ignore: unawaited_futures
          showDialogConfirmation(
            text: tt(TTShared.quieresCancelarLaOperacionPregunta),
          ).then((value) {
            widget.onCompleted(Cancellable.canceled());
            if (value) NavigatorService().pop();
          });
          return false;
        },
        child: Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Visibility(
                visible: !isNull(widget.additionalWidget),
                child: Column(
                  children: [
                    widget.additionalWidget ?? const SizedBox(),
                    SeparatorFiori(),
                  ],
                ),
              ),
              TextFormFieldER(
                isTextArea: true,
                title: widget.description,
                controller: field,
                icon: widget.icon,
              ),
              SeparatorFiori(),
              SeparatorFiori.half(),
              Container(
                width: double.maxFinite,
                alignment: Alignment.centerRight,
                child: Container(
                  width: ThemeER.dialogConfirmButtonWidth.get(),
                  child: ButtonER(
                    title: widget.buttonTitle ?? tt(TTShared.confirmar),
                    color: themeColors.secondaryGreen,
                    colorSplash: themeColors.white,
                    colorText: themeColors.white,
                    icon: FontAwesomeIcons.solidFloppyDisk,
                    onPressed: () {
                      widget.onCompleted(Cancellable.completed(field.text));
                      NavigatorService().pop();
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
