import '../../_imports.dart';

class UserLoginPage extends StatefulWidget {
  @override
  _UserLoginPageState createState() => _UserLoginPageState();
}

class _UserLoginPageState extends State<UserLoginPage> {
  String? version;

  @override
  void initState() {
    getVersion();
    super.initState();
  }

  Future<void> getVersion() async {
    final packageInfo = await packageInfoService.get();
    version = packageInfo?.version;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    void restorePassword() {
      UserRecoverPasswordBloc(
        connection: injector.get<ConnectionServiceBase>(),
        onClosed: () => NavigatorService().pop(),
      ).openPage();
    }

    return LoginPage(
      LoginProvider(
        languages: global.languages,
        loginUseCase: LoginUseCase(
          isValidTicket: () => global.ticket?.user?.id != null,
          pushLoginPage: () {},
          autologinUserEmail: preferences.autologinUserEmail,
          autologinUserPassword: preferences.autologinUserPassword,
          autologinSSOCompanyId: preferences.autologinSSOCompanyId,
          autologinSSORoleId: preferences.autologinSSORoleId,
          ticketUseCase: injector.get<TicketUseCase>(),
          connection: connection,
          applicationId: user.app.id,
          setTicket: (t) => global.ticket = t,
          onCompletedLogin: () {
            // There is a error witch the progress dialog don't disappear.
            if (NavigatorService().canPop()) NavigatorService().pop();
            global.onCompletedLogin();
          },
          getLoggedUser: global.getLoggedUser,
          app: user.app,
          ssoUseCase: ssoUseCase,
        ),
      ),
      appNameLogo: Wrap(
        spacing: 10,
        runSpacing: 10,
        alignment: WrapAlignment.center,
        children: [
          AppNameText("Eco", "Instalaciones"),
        ],
      ),
      onRestorePassword: () => restorePassword(),
      loginWithEmail: true,
      footerWidget: BodyText(
        '${TTShared.version.tt} ${version}',
        color: themeColors.grey8,
      ),
    );
  }
}
