import 'package:database_hive_provider/database_hive_provider.dart';
import 'package:firebase_core_service_global/firebase_core_service_global.dart';
import 'package:pipeline_log/pipeline_log.dart';

import '_imports.dart';

class EcoMovilApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return RestartWidget(
      child: ThemePage(
        builder: (_) => GetMaterialApp(
          scrollBehavior: ScrollBehaviourMultiplatform(),
          localizationsDelegates: [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: [
            const Locale('es', ''),
          ],
          theme: ThemeProvider().theme.themeData,
          home: Builder(
            builder: (___) => StartPage(
              splashScreenContainer:
                  SplashScreenContainer(message: "BIENVENIDO"),
              pipelineController: PipelineController(
                steps: [
                  // Iniciamos Firebase
                  () => PipelineStep(
                        onExecute: () async {
                          debugPrint("INICIANDO FIREBASE CORE");
                          await FirebaseCoreServiceGlobal().start(null);
                        },
                        onErrorFunction: () async {},
                      ),

                  // Habilitamos el log.
                  () => PipelineLog(),

                  () => PipelineStep(
                        onExecute: () async {
                          debugPrint("ASIGNANDO FORMATO DE FECHAS");
                          await initializeDateFormatting("es");
                        },
                        onErrorFunction: () async {
                          Log().error(
                              "Error al inciar la aplicación en la función initializeDateFormatting");
                        },
                      ),

                  // Iniciamos el injector de dependencias
                  () => PipelineStep(
                        onExecute: () async {
                          debugPrint("INICIANDO INJECTOR DE DEPENDENCIAS");
                          await EcoMovilInjector().init();
                        },
                        onErrorFunction: () async {
                          Log().error("ERROR AL INICIAR SERVICIO DE SESIÓN");
                        },
                      ),

                  () => PipelineStep(
                        onExecute: () async {
                          debugPrint("INICIANDO CONNECTION SERVICE");
                          await connection.start();
                        },
                        onErrorFunction: () async {},
                      ),

                  // Inicia la base de datos.
                  () => PipelineStep(
                      timeout: Duration(seconds: 20),
                      onExecute: () async {
                        Log().debug("INICIANDO BASE DE DATOS");
                        await injector.get<DataServiceBase>().startDatabase(
                              () => DatabaseHiveService()
                                  .startDatabase(DatabaseHiveProvider()),
                            );
                      },
                      onErrorFunction: () async {}),

                  () => PipelineNewVersion(
                        packageInfoService:
                            injector.get<PackageInfoServiceBase>(),
                        onNewVersion: (before, current) async {
                          await DatabaseHiveService().repair();
                        },
                      ),

                  // Iniciamos servicio de traducción
                  () => PipelineStep(
                        onExecute: () async {
                          Log().debug("ESTABLECIENDO TRADUCCIONES");
                          await TranslationServiceGlobal().start(
                            defaultLanguageType: LanguageType.spanish,
                            languagePref: injector.get<Preferences>().language,
                            translationProvider: TranslationProviderJson(),
                          );
                        },
                        onErrorFunction: () async {},
                      ),

                  // Comprueba si hay actualizaciones disponibles.
                  // () => injector.get<EcoMovilEnvironment>().pipelineAppUpdate,
                ],
                onCompleted: () async => LoginUseCase(
                  isValidTicket: () => global.isValidTicket(),
                  pushLoginPage: () =>
                      NavigatorService().pushReplacement(LocalLoginPage()),
                  autologinUserEmail: preferences.autologinUserEmail,
                  autologinUserPassword: preferences.autologinUserPassword,
                  autologinSSOCompanyId: preferences.autologinSSOCompanyId,
                  autologinSSORoleId: preferences.autologinSSORoleId,
                  ticketUseCase: injector.get<TicketUseCase>(),
                  connection: connection,
                  applicationId: user.app.id,
                  setTicket: (t) => global.ticket = t,
                  onCompletedLogin: global.onCompletedLogin,
                  getLoggedUser: global.getLoggedUser,
                  app: user.app,
                  ssoUseCase: ssoUseCase,
                ).tryAutologin(""),
                onError: (e, i) async {
                  Log().catastrophe("Error en el pipeline de inicio ($i): $e");
                  Log().info(
                      "Borrando la base de datos y reiniciando la aplicación");
                  try {
                    await DatabaseHiveService().repair(isRemovedAll: true);
                    await DatabaseHiveService().close();
                  } catch (e) {}
                  // restartService.restart();
                },
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class RestartWidget extends StatefulWidget {
  RestartWidget({this.child});

  final Widget? child;

  static void restartApp(BuildContext context) {
    context.findAncestorStateOfType<_RestartWidgetState>()!.restartApp();
  }

  @override
  _RestartWidgetState createState() => _RestartWidgetState();
}

class _RestartWidgetState extends State<RestartWidget> {
  Key key = UniqueKey();

  void restartApp() {
    setState(() {
      key = UniqueKey();
    });
  }

  @override
  Widget build(BuildContext context) {
    return KeyedSubtree(
      key: key,
      child: widget.child!,
    );
  }
}
