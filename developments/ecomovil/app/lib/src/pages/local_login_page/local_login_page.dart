import '../../_imports.dart';

class LocalLoginPage extends StatefulWidget {
  @override
  _LocalLoginPageState createState() => _LocalLoginPageState();
}

class _LocalLoginPageState extends State<LocalLoginPage> {
  final loginBloc = LoginProvider(
    languages: global.languages,
    loginUseCase: LoginUseCase(
      isValidTicket: () => global.isValidTicket(),
      pushLoginPage: () {},
      autologinUserEmail: preferences.autologinUserEmail,
      autologinUserPassword: preferences.autologinUserPassword,
      autologinSSOCompanyId: preferences.autologinSSOCompanyId,
      autologinSSORoleId: preferences.autologinSSORoleId,
      ticketUseCase: injector.get<TicketUseCase>(),
      connection: connection,
      applicationId: user.app.id,
      setTicket: (t) => global.ticket = t,
      onCompletedLogin: global.onCompletedLogin,
      getLoggedUser: global.getLoggedUser,
      app: user.app,
      ssoUseCase: ssoUseCase,
    ),
  );

  @override
  Widget build(BuildContext context) {
    // final createAccount = ClickableText(tt(TTShared.crearCuenta), () {
    //   UserRegisterBloc(
    //     onClosed: () => NavigatorService().pop(),
    //   ).openPage();
    // });

    final restorePassword = ClickableText(
      tt(TTShared.hasOlvidadoTuContrasenia),
      () => UserRecoverPasswordBloc(
        connection: injector.get<ConnectionServiceBase>(),
        onClosed: () => NavigatorService().pop(),
      ).openPage(),
    );

    return LoginPage(
      loginBloc,
      footerWidget: Column(
        children: [
          Container(
            width: double.maxFinite,
            child: Wrap(
              alignment: WrapAlignment.spaceBetween,
              runAlignment: WrapAlignment.center,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: <Widget>[
                // createAccount,
                // const SizedBox(height: 40),
                restorePassword
              ],
            ),
          ),
        ],
      ),
      loginWithEmail: true,
    );
  }
}
