import 'package:connection_provider_gps/connection_provider_gps.dart';
import 'package:connection_provider_internet/connection_provider_internet.dart';
import 'package:connection_state_provider_connectivity_plus/connection_state_provider_connectivity_plus.dart';
import 'package:message_local_hive/message_local_hive.dart';
import 'package:package_info_plus_service/package_info_plus_service.dart';
import 'package:position_util_service/position_util_service.dart';
import 'package:server_time_service/server_time_service.dart';
import 'package:server_version_service/server_version_service.dart';
import 'package:sso_local/sso_local.dart';

import '_imports.dart';

class EcoSatPuntoLimpioInjector {
  Future<void> init() async {
    //==========================================================================
    // USER
    //==========================================================================
    final user = UserEcoSatPuntoLimpio();
    injector.registerSingleton<UserEcoSatPuntoLimpio>(user);
    injector.registerSingleton<UserBase>(user);
    injector.registerSingleton<UserNPBase>(user);

    //==========================================================================
    // HELPERS
    //==========================================================================
    injector.registerSingleton<Preferences>(Preferences());
    injector.registerSingleton<PositionUtilServiceBase>(PositionUtilService());

    //==========================================================================
    // SERVICES 1
    //==========================================================================
    injector.registerSingleton<ConnectionServiceBase>(
      ConnectionService(
        ConnectionProviderGPS(),
        environment.getConnectionProviderLocation(),
        ConnectionProviderInternet(ConnectionStateProviderConnectivity()),
      ),
    );
    injector.registerSingleton<FilePickerServiceBase>(FilePickerService());
    injector.registerSingleton<ExternalLinkServiceBase>(ExternalLinkService());

    //==========================================================================
    // TOKEN
    //==========================================================================
    injector.registerSingleton<TokenBase>(
      Token(
        TokenSourceNP(user),
        preferences.urlServer.get,
        injector.get<ConnectionServiceBase>(),
      ),
    );

    //==========================================================================
    // SERVICES 2
    //==========================================================================
    injector.registerSingleton<DocumentBase64ServiceBase>(
        environment.getDocumentBase64Service());
    injector
        .registerSingleton<PackageInfoServiceBase>(PackageInfoPlusService());
    if (environment.getExcelService != null) {
      injector
          .registerSingleton<ExcelServiceBase>(environment.getExcelService!());
    }
    injector.registerSingleton<DataServiceBase>(DataService());
    if (environment.getQRService != null)
      injector.registerSingleton<QRServiceBase>(environment.getQRService!());
    injector.registerLazySingleton<ImageServiceBase>(
        () => environment.getImageService());
    injector.registerSingleton<NotificationServiceBase>(
        environment.getNotificationService());
    injector.registerSingleton<MapInfoServiceBase>(MapInfoServiceNP(token));
    injector.registerSingleton<SyncServiceBase>(
      SyncService(
        isUserValid: () => injector.get<UserEcoSatPuntoLimpio>().isLogin(),
        connection: injector.get<ConnectionServiceBase>(),
        token: token,
      ),
    );
    injector.registerLazySingleton<RestartServiceBase>(
        () => environment.restartService);
    injector.registerSingleton<ServerTimeServiceBase>(
      ServerTimeService(
        token: token,
        connection: connection,
        diffMicrosecondsPref: preferences.diffMicrosecondsTimeServer,
      ),
    );
    injector.registerSingleton<ServerVersionServiceBase>(
      ServerVersionService(
        token: token,
        connection: connection,
        serverVersionPref: preferences.serverVersion,
      ),
    );

    //==========================================================================
    // INJECTORS
    //==========================================================================
    await PreferenceInjector(
            repository:
                PreferenceRepositoryHive(injector.get<DataServiceBase>()))
        .init();
    await GeographicFrameInjector(user).init();
    await SSOInjector(
      companyApp: preferences.companyApp,
      user: user,
      connection: injector.get<ConnectionServiceBase>(),
      ticketLocal: TicketLocal(injector.get<DataServiceBase>()),
      ssoLocal: SSOLocal(
        injector.get<DataServiceBase>(),
        injector.get<UserBase>(),
      ),
      userSSOLocal: UserSSOLocal(
        injector.get<DataServiceBase>(),
        injector.get<UserBase>(),
      ),
      token: Token(
        TokenSourceSSO(),
        () async => getUrlServerSSO(),
        injector.get<ConnectionServiceBase>(),
      ),
      textSSOLocal: TextSSOLocal(
        injector.get<DataServiceBase>(),
        injector.get<UserNPBase>(),
      ),
      getTicketCached: () => GlobalBloc().ticket!,
      isRemovableRoleId: (int? id) {
        if (SSORole.admin.id == id) return false;
        return true;
      },
      lastSyncUserSSO: preferences.lastSyncUserSSO,
      lastSyncMenuSSO: preferences.lastSyncMenuSSO,
      menuSSOLocal: MenuSSOLocal(dataService, user),
      syncService: syncService,
      unallowedRoles: SSORole.values.where2(
        (m) => !m.isFromApp(SSOApplication.EcoSATPuntoLimpio),
      ),
      municipalityPointLocal: MunicipalityPointLocal(dataService, user),
    ).init();
    ;
    await MessageInjector(
      connection: connection,
      local: MessageLocalSource(dataService, user),
      user: user,
      lastSyncMessages: preferences.lastSyncMessages,
      token: token,
      remote: MessageSharedRemote(token: token, user: user),
      syncService: syncService,
    ).init();
    await EquipmentInjector(preferences.lastSyncEquipment).init();
    await ZoneInjector(
      lastSyncZone: preferences.lastSyncZone,
      connection: connection,
      token: token,
      user: user,
      syncService: syncService,
    ).init();
    await ConfigurationInjector(user.app).init();

    await SettingsInjector(
      user: user,
      connection: connection,
      lastSyncSettings: preferences.lastSyncSettings,
      token: token,
      syncService: syncService,
    ).init();

    await ProductInjector1BeforeCleanPoint(
      lastSyncProductCategory: preferences.lastSyncProductCategory,
    ).init();

    await ElementInjector(
      lastSyncElement: preferences.lastSyncElement,
      paginationElementGuid: preferences.paginationElementGuid,
      paginationElementIndex: preferences.paginationElementIndex,
      paginationElementMaxCount: preferences.paginationElementMaxCount,
      serverTimeService: injector.get<ServerTimeServiceBase>(),
    ).init();
    await ResidueInjector(
      user: user,
      connection: connection,
      lastSyncResidue: preferences.lastSyncResidue,
      token: token,
      syncService: syncService,
    ).init();
    await UserAccessInjector(
      connection: connection,
      lastSyncDate: preferences.lastSyncUserAccess,
      syncService: syncService,
      user: user,
    ).init();

    await CleanPointInjector(
      connection: connection,
      lastSyncCleanPointPref: preferences.lastSyncCleanPoint,
      token: token,
      user: user,
      lastSyncCleanPointPersonPref: preferences.lastSyncCleanPointPerson,
      lastSyncCleanPointCompanyPref: preferences.lastSyncCleanPointCompany,
    ).init();
    await CitizenInjector(preferences.lastSyncCitizen).init();

    await ProductInjector2AfterCleanPoint(
      lastSyncProduct: preferences.lastSyncProduct,
      lastSyncProductRequest: preferences.lastSyncProductRequest,
      citizenUseCase: citizenUseCase,
      lastSyncProductWithdrawalRequest:
          preferences.lastSyncProductWithdrawalRequest,
      user: user,
    ).init();

    await ResidueRegisterInjector(
      lastSyncResidueRegister: preferences.lastSyncResidueRegister,
      lastSyncResidueRegisterElementCalculation:
          preferences.lastSyncResidueRegisterElementCalculation,
    ).init();

    await LopdInjector(preferences.lastSyncLopd).init();

    //==========================================================================
    // OTROS
    //==========================================================================
    UserStateService().addListener((u) async {
      if (u.isLogin()) {
        notificationService.enable();
        // if (home!.hasEcoReutiliza)
        //   ProductRequestNotification.start(
        //     (models) => NotificationData(
        //       id: ESPLNotificationType.productRequest.name,
        //       modelIds: models.map2((e) => e.guid),
        //     ).toString(),
        //     environment.lastSyncProductRequestBackground,
        //     environment.lastSyncIsFromBackground,
        //   );

        await syncService.start();
      } else {
        ProductRequestDeliveryNotification.clean();
        ProductRequestWithdrawalNotification.clean();
        notificationService.disable();
        await injector.get<SyncServiceBase>().reset();
        await injector.get<NotificationServiceBase>().cancelAll();
      }
    });

    // Comentado temporalmente hasta que se añada un botón para mostrar los usuarios.
    //   messageUseCase.onChange.listen((event) {
    //     if (event.type != OnChangedType.insert) return;
    //     final message = event.after;
    //     if (message.isCreator(user)) return;
    //     if (!message.isValidForNotify()) return;
    //     // Si tiene la página de chat abierta, no muestro la alerta...
    //     if (GlobalBloc()
    //         .homeProvider!
    //         .floatingWindowCubit
    //         .exists((ChatPage).toString())) {
    //       // ...Si no es admin, porque solo puede chatear con un  único usuario.
    //       if (!user.isAdmin) return;
    //       // ...Si es admin y tiene el chat del usuario del mensaje abierto.
    //       if (ChatProvider.chatOpenuserId == message.userNoAdminId) return;
    //     }

    //     String m = message.contentType != MessageContentType.text
    //         ? tt(TTShared.imagen)
    //         : message.content;

    //     if (user.isAdmin) {
    //       final userSSO = userSSOManager.getByPk(message.userNoAdminId);
    //       m = "${userSSO?.name ?? tt(TTShared.desconocido)}: $m";
    //     }
    //     notificationService.show(
    //       title: tt(TTShared.nuevoMensaje),
    //       message: m,
    //     );
    //   });
  }
}
