import '../_imports.dart';

extension CleanPointBuildingModelExtension on CleanPointBuildingModel {
  void centerInMap({bool isEntrance = false}) {
    home!.listenMapMovement = false;
    if (isEntrance) {
      centerInMapByEntrance();
    } else {
      centerInMapByPolygonCenter();
    }
    home!.temporalCleanPoint = this;
    home!.onChangedCleanPoint();
  }

  void centerInMapByPolygonCenter() {
    final LatLng? position = getPosition();

    if (position == null) return;

    GlobalBloc().centerMapWithZoom(
      position.latitude,
      position.longitude,
      isAreaZoom: true,
    );
  }

  LatLng? getPosition() {
    List<LatLong>? latLongs = getPolygonPoints(zoneManager.modelsMap);

    return MapUtils.calculatePolygonCenter(
      latLongs?.map((e) => LatLng(e.lat, e.long)).toList(),
      positionUtilService,
    );
  }

  void centerInMapByEntrance() {
    final LatLong? position = getEntrancePosition(zoneManager.modelsMap);
    if (position == null) return;

    GlobalBloc().centerMapWithZoom(
      position.lat,
      position.long,
      isAreaZoom: true,
    );
  }

  List<ResidueModel> acceptedResidues() {
    final residues = <ResidueModel>[];
    List<int> residuesAcceptedIds = residuesAccepted.map2((e) => e.id);
    for (final r in residueManager.models) {
      if (residuesAcceptedIds.contains(r.id)) residues.add(r);
    }
    return residues;
  }
}

extension CleanPointBuildingModelListExtension
    on List<CleanPointBuildingModel> {
  void sortByDistance(Map<String, double?> distances) {
    try {
      sort((m1, m2) {
        // if (!distances.containsKey(m1.guid))
        //   distances[m1.guid] = UtilPosition.getDistanceInMeters(
        //     point1Lat: m1.entryLat,
        //     point1Long: m1.entryLong,
        //     point2Lat: connection.currentLatLng?.lat ?? 0,
        //     point2Long: connection.currentLatLng?.long ?? 0,
        //   );
        // if (!distances.containsKey(m2.guid))
        //   distances[m2.guid] = UtilPosition.getDistanceInMeters(
        //     point1Lat: m2.entryLat,
        //     point1Long: m2.entryLong,
        //     point2Lat: connection.currentLatLng?.lat ?? 0,
        //     point2Long: connection.currentLatLng?.long ?? 0,
        //   );
        if (distances[m1.guid] == null || distances[m2.guid] == null) {
          return 0;
        }
        return distances[m1.guid]!.compareTo(distances[m2.guid]!);
      });
    } catch (e) {
      print(e);
    }
  }
}
