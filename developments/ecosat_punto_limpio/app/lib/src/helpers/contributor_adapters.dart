import '../_imports.dart';

class CleanPointContributorAdapterCitizen implements CleanPointContributorBase {
  final CitizenModel citizen;
  final int companyId;

  CleanPointContributorAdapterCitizen(this.citizen, this.companyId);

  @override
  int get id => citizen.id;

  @override
  String get guid => citizen.guid;

  @override
  String get name => citizen.getNameComplete(LopdFactory());

  @override
  String get email => citizen.getEmail(LopdFactory());

  @override
  String get nameEmailOrId =>
      [name, email, citizen.idViewWithTitle].firstFilled;

  @override
  String get phone => citizen.phone;

  String get subscriberNumber {
    final cps = citizen.addresses;
    // Check if addresses is null or empty
    if (isListNullOrEmpty(cps)) return "";
    if (cps.length == 1) {
      return cps.first.subscriberNumber ?? "";
    }
    // Multiple addresses: join subscriber numbers with comma separator
    return cps.map((address) => address.subscriberNumber ?? "").join(', ');
  }

  @override
  CleanPointContributorType get contributorType =>
      CleanPointContributorType.person;

  @override
  String get filter => citizen.getFilter(LopdFactory());
}
