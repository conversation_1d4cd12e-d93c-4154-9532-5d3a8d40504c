import '_imports.dart';

class UserEcoSatPuntoLimpio implements UserNPBase, UserProductBase {
  final app = SSOApplication.EcoSATPuntoLimpio;

  SSORole? _role;
  SSORole get role => _role!;
  int? _id;
  int? get id => _id;
  int? _companyId;
  int? get companyId => _companyId;
  int? _companyIdSSO;
  int get companyIdSSO => _companyIdSSO!;
  bool? _isInternal;
  bool get isInternal => _isInternal!;
  String? _email;
  String get email => _email!;
  // En esta app no es necesario.
  String get citizenGuid => "";

  bool get isAdmin => _role == SSORole.admin;
  bool get isCitizen => !isAdmin && !isCleanPointManager;
  bool get isCleanPointManager => _role == SSORole.managerCleanPoint;

  void login({
    required int id,
    required int companyId,
    required int companyIdSSO,
    required SSORole role,
    required bool isInternal,
    required String email,
  }) {
    _id ??= id;
    _companyId ??= companyId;
    _companyIdSSO ??= companyIdSSO;
    _role ??= role;
    _isInternal ??= isInternal;
    _email ??= email;
  }

  void logout() {
    _id = null;
    _companyId = null;
    _companyIdSSO = null;
    _role = null;
    _isInternal = null;
    _email = null;
  }

  bool isLogin() {
    if (_role == null) return false;
    if (isNullOrLesserZero(_id)) return false;
    if (isNullOrLesserZero(_companyId)) return false;
    return true;
  }

  @override
  String toString() {
    return 'UserEcoSatPuntoLimpio(_role: $_role, _id: $_id, _companyId: $_companyId, _isInternal: $_isInternal)';
  }
}
