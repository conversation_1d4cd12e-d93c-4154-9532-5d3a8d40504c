import 'package:database_hive_provider/database_hive_provider.dart';
import 'package:pipeline_date_formatting/pipeline_date_formatting.dart';
import 'package:pipeline_log/pipeline_log.dart';
import 'package:pipeline_ticket_id_saver/pipeline_ticket_id_saver.dart';
import 'package:pipeline_new_version/pipeline_new_version.dart';
import 'package:pipeline_sso_environment_changed/pipeline_sso_environment_changed.dart';
import 'package:translation_provider_json/translation_provider_json.dart';

import '_imports.dart';

class EcoSatPuntoLimpioApp extends StatelessWidget {
  final EcoSatPuntoLimpioEnvironment env;
  String? ticketId;
  // This 3 parameters is for plugin device_preview.
  final Locale? locale;
  final bool? useInheritedMediaQuery;
  final TransitionBuilder? builder;

  EcoSatPuntoLimpioApp({
    required this.env,
    required this.ticketId,
    this.locale,
    this.useInheritedMediaQuery,
    this.builder,
  }) {
    injector.registerSingleton<EcoSatPuntoLimpioEnvironment>(env);
  }

  @override
  Widget build(BuildContext context) {
    return ThemePage(
      builder: (_) => GetMaterialApp(
        // This 3 parameters is for plugin device_preview.
        useInheritedMediaQuery: useInheritedMediaQuery ?? false,
        locale: locale,
        builder: builder,
        scrollBehavior: ScrollBehaviourMultiplatform(),
        localizationsDelegates: [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: [
          const Locale('es', ''),
        ],
        theme: ThemeProvider().theme.themeData,
        home: Builder(
          builder: (___) => StartPage(
            splashScreenContainer: SplashScreenContainer(message: "BIENVENIDO"),
            pipelineController: PipelineController(
              steps: [
                () => PipelineLog(),
                () => PipelineTicketIdSaver(
                      ticketId,
                      (newTicketId) => ticketId = newTicketId,
                    ),
                () => PipelineStep(
                      onExecute: () async {
                        await EcoSatPuntoLimpioInjector().init();
                      },
                      onErrorFunction: () async {},
                    ),
                // Inicia la base de datos.
                () => PipelineStep(
                      // La base de datos Hive a veces no arranca y tampoco genera error.
                      timeout: Duration(seconds: 40),
                      onExecute: () async {
                        Log().debug("INICIANDO BASE DE DATOS");
                        await injector.get<DataServiceBase>().startDatabase(
                              () => DatabaseHiveService()
                                  .startDatabase(DatabaseHiveProvider()),
                            );

                        // Log().debug("INICIANDO BACKGROUND");
                        // await environment.backgroundStart();
                      },
                      onErrorFunction: () async {},
                    ),

                () => PipelineDateFormatting(),

                // Iniciamos servicio de traducción.
                () => PipelineStep(
                      onExecute: () async {
                        Log().debug("ESTABLECIENDO TRADUCCIONES");
                        await TranslationServiceGlobal().start(
                          defaultLanguageType: LanguageType.spanish,
                          languagePref: preferences.language,
                          translationProvider: TranslationProviderJson(),
                          translationProviderAdditional:
                              TranslationProviderAdditionalSSO(textSSOManager),
                        );
                      },
                      onErrorFunction: () async {},
                    ),
                () => PipelineStep(
                      onExecute: () async {
                        Log().debug("INICIANDO CONNECTION SERVICE");
                        await injector.get<ConnectionServiceBase>().start();
                      },
                      onErrorFunction: () async {},
                    ),
                () => PipelineSsoEnvironmentChanged(
                      removeDatabase: () async {
                        await DatabaseHiveService().repair(isRemovedAll: true);
                        // await environment.backgroundClear();
                      },
                      restartApp: () async {
                        await environment.restartService.restart();
                      },
                    ),
                () => PipelineNewVersion(
                      packageInfoService: packageInfoService,
                      onNewVersion: (before, current) async {
                        if (before.isEmpty) return;
                        // Eliminamos todo en las versiones de desarrollo para solucionar los problemas en web de que no arraque.
                        await DatabaseHiveService().repair(
                          // We force remove in version 1.0.5 because the equipment database changed de data types.
                          isRemovedAll:
                              current.startsWith("0") || current == "1.0.6",
                        );
                        // await environment.backgroundClear();
                      },
                      onPostNewVersion: (before, current) async {
                        if (before.isEmpty) return;
                        // Siempre que haya una nueva actualización, reiniciamos.
                        // Principalmente se hace para web, para que cargue los ficheros de la nueva versión.
                        // También se hace por si se ha reparado la base de datos, para no olvidarse de reiniciar.
                        environment.restartService.restart();
                      },
                    ),
              ],
              onCompleted: () async => LoginUseCase(
                isValidTicket: () => global.isValidTicket(),
                pushLoginPage: () =>
                    NavigatorService().pushReplacement(LocalLoginPage()),
                autologinUserEmail: preferences.autologinUserEmail,
                autologinUserPassword: preferences.autologinUserPassword,
                autologinSSOCompanyId: preferences.autologinSSOCompanyId,
                autologinSSORoleId: preferences.autologinSSORoleId,
                ticketUseCase: injector.get<TicketUseCase>(),
                connection: connection,
                applicationId: user.app.id,
                setTicket: (t) => global.ticket = t,
                onCompletedLogin: global.onCompletedLogin,
                getLoggedUser: global.getLoggedUser,
                app: user.app,
                ssoUseCase: ssoUseCase,
              ).tryAutologin(ticketId ?? ""),
              onError: (e, i) async {
                final message = "Error en el pipeline de inicio ($i): $e";
                // Lo imprimimos para que se pueda ver en la consola de la web.
                print(message);
                Log().catastrophe(message);
                Log().info(
                    "Borrando la base de datos y reiniciando la aplicación");
                try {
                  await DatabaseHiveService().repair(isRemovedAll: true);
                  await DatabaseHiveService().close();
                  // await environment.backgroundClear();
                } catch (e) {}
                environment.restartService.restart();
              },
            ),
          ),
        ),
      ),
    );
  }
}
