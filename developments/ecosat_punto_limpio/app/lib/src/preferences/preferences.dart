import '../_imports.dart';

class Preferences {
  late final isShowedDebugInfo = PreferenceGlobal<bool>(
    "isShowedDebugInfo",
    false,
  );

  final companyApp = PreferenceGlobal<String>(
    "companyApp",
    "",
  );
  final lastSyncCleanPoint = PreferenceUser(
    "lastSyncCleanPoint",
    PreferenceBase.dateTimeWithRemovedsNP,
  );

  final lastSyncCleanPointManager = PreferenceUser(
    "lastSyncCleanPointManager",
    PreferenceBase.dateTimeWithRemovedsNP,
  );

  final lastSyncCitizen = PreferenceUser(
    "lastSyncCleanPointManager",
    PreferenceBase.dateTimeWithRemovedsNP,
  );

  final language = LanguagePref();

  final selecteCleanPointGuid = PreferenceUser<String>(
    "selecteCleanPointGuid",
    "",
  );

  final hiddenEquipmentMap = PreferenceUser<String>("hiddenEquipmentMap", "");

  final autologinUserEmail = PreferenceGlobal<String>(
    "autologinUserEmail",
    "",
  );
  final autologinUserPassword = PreferenceGlobal<String>(
    "autologinUserPassword",
    "",
  );

  final autologinSSOCompanyId = PreferenceGlobal<int>(
    "autologinSSOCompanyId",
    -1,
  );

  final autologinSSORoleId = PreferenceGlobal<int>(
    "autologinSSORoleId",
    -1,
  );

  final userId = PreferenceGlobal<int>(
    "userId",
    -1,
  );
  final companyId = PreferenceGlobal(
    "companyId",
    -1,
  );
  final diffMicrosecondsTimeServer = PreferenceUser<int>(
    "diffMicrosecondsTimeServer",
    -1,
  );
  final serverVersion = PreferenceUser<String>(
    "serverVersion",
    "",
  );
  final paginationElementGuid =
      PreferenceUser<String>("paginationElementGuid", "");
  final paginationElementIndex =
      PreferenceUser<int>("paginationElementIndex", -1);
  final paginationElementMaxCount =
      PreferenceUser<int>("paginationElementMaxCount", -1);
  final lastSyncElement = PreferenceUser<String>(
    "lastSyncElement",
    PreferenceBase.dateTimeWithRemovedsNP,
  );
  final lastSyncCleanPointPerson = PreferenceUser<String>(
    "lastSyncCleanPointPerson",
    PreferenceBase.dateTimeWithRemovedsNP,
  );
  final lastSyncCleanPointCompany = PreferenceUser<String>(
    "lastSyncCleanPointCompany",
    PreferenceBase.dateTimeWithRemovedsNP,
  );

  final lastSyncMessages = PreferenceUser<String>(
    "lastSyncMessages",
    PreferenceBase.dateTimeNP,
  );

  final lastSyncCadastre = PreferenceUser<String>(
    "lastSyncCadastre",
    PreferenceBase.dateTimeNP,
  );
  final lastSyncUserSSO = PreferenceUser(
    "lastSyncUserSSO",
    PreferenceBase.dateTimeNP,
  );
  final lastSyncEquipment = PreferenceUser<String>(
    "lastSyncEquipment",
    PreferenceBase.dateTimeWithRemovedsNP,
  );
  final lastSyncLocationPoint = PreferenceUser<String>(
    "lastSyncLocationPoint",
    PreferenceBase.dateTimeNP,
  );
  final lastSyncMenuSSO = PreferenceUser(
    "lastSyncMenuSSO",
    PreferenceBase.dateTimeNP,
  );
  final lastSyncUserAccess = PreferenceUser<String>(
    "lastSyncUserAccess",
    PreferenceBase.dateTimeNP,
  );
  final lastSyncResidueRegister = PreferenceUser(
    "lastSyncResidueRegister",
    PreferenceBase.dateTimeNP,
  );
  final lastSyncResidueRegisterElementCalculation = PreferenceUser(
    "lastSyncResidueRegisterElementCalculation",
    PreferenceBase.dateTimeNP,
  );
  final lastSyncResidue = PreferenceUser(
    "lastSyncResidue",
    PreferenceBase.dateTimeWithRemovedsNP,
  );
  final lastSyncSettings = PreferenceUser(
    "lastSyncSettings",
    PreferenceBase.dateTime,
  );
  final lastSyncZone = PreferenceUser(
    "lastSyncZone",
    PreferenceBase.dateTimeWithRemovedsNP,
  );
  final lastSyncProductCategory = PreferenceUser(
    "lastSyncProductCategory",
    PreferenceBase.dateTimeWithRemovedsNP,
  );
  final lastSyncProduct = PreferenceUser(
    "lastSyncProduct",
    PreferenceBase.dateTimeWithRemovedsNP,
  );

  final lastSyncLopd = PreferenceUser(
    "lastSyncLopd",
    PreferenceBase.dateTimeNP,
  );

  final lastSyncProductRequest = PreferenceUser(
    "lastSyncProductRequest",
    PreferenceBase.dateTimeNP,
  );

  final lastSyncProductWithdrawalRequest = PreferenceUser(
    "lastSyncProductWithdrawalRequest",
    PreferenceBase.dateTimeNP,
  );

  final urlServer = PreferenceFake(
    () async => !isStringNullOrEmpty(EnvironmentDebug().urlServer)
        ? EnvironmentDebug().urlServer
        : GlobalBloc().ticket!.urlApi,
    (_) async => null,
  );

  // Guardado de tamaños de columnas de las tablas en Web.
  final communityTableState = PreferenceUser<String>(
    "communityTableState",
    "",
  );
  final userSSOTableState = PreferenceUser<String>(
    "userSSOTableState",
    "",
  );
  final residueRegisterElementCalculationHistoryTableState =
      PreferenceUser<String>(
    "residueRegisterElementCalculationHistoryTableState",
    "",
  );
  final residueRegisterElementCalculationTableState = PreferenceUser<String>(
    "residueRegisterElementCalculationTableState",
    "",
  );
  final residueRegisterHistoryTableState = PreferenceUser<String>(
    "residueRegisterHistoryTableState",
    "",
  );
  final cleanPointCompanyTableState = PreferenceUser<String>(
    "cleanPointCompanyTableState",
    "",
  );
  final cleanPointPersonTableState = PreferenceUser<String>(
    "cleanPointPersonTableState",
    "",
  );
  final cadastreTableState = PreferenceUser<String>(
    "cadastreTableState",
    "",
  );
  final userAccessTableState = PreferenceUser<String>(
    "userAccessTableState",
    "",
  );
  final locationPointTableState = PreferenceUser<String>(
    "locationPointTableState",
    "",
  );

  final residueTableState = PreferenceUser<String>(
    "residueTableState",
    "",
  );
  final residueRegisterTableState = PreferenceUser<String>(
    "residueRegisterTableState",
    "",
  );

  final cleanPointTableState = PreferenceUser<String>(
    "cleanPointTableState",
    "",
  );
  final elementTableState = PreferenceUser<String>(
    "elementTableState",
    "",
  );

  final equipmentTableState = PreferenceUser<String>(
    "equipmentTableState",
    "",
  );

  final lopdTableState = PreferenceUser<String>(
    "lopdTableState",
    "",
  );

  final productTableState = PreferenceUser<String>(
    "productTableState",
    "",
  );

  final productRequestTableState = PreferenceUser<String>(
    "productRequestTableState",
    "",
  );

  final productCategoryTableState = PreferenceUser<String>(
    "productCategoryTableState",
    "",
  );

  final citizenTableState = PreferenceUser<String>(
    "citizenTableState",
    "",
  );

  final expandedPref = PreferenceUser<String>(
    "expandedPref",
    "",
  );

  final windowsSize = PreferenceCached(
    PreferenceUser<String>(
      "windowsSize",
      "",
    ),
  );

  final homeLocation = ConfigurationPreferenceNotifierMapAdapter(user.app);
}
