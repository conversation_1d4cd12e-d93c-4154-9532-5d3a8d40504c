import '../../_imports.dart';

class ResidueRegisterInputPage extends StatelessWidget {
  final ResidueRegisterInputProvider provider;

  ResidueRegisterInputPage(this.provider);
  @override
  Widget build(BuildContext context) {
    return provider.starting(
      (_) => provider.rebuilding(
        (_) => ScaffoldCustom(
          appBar: CustomAppBar(
            TTPL.entrada.tt,
            actions: provider.isRemoved
                ? null
                : [ActionButtonAppBar.options(provider.getOptions() ?? [])],
          ),
          body: ListViewAnimated(
            children: [
              ..._cleanPointContributor(context),
              // El formulario predeterminado, sin redondear por una tarjeta y borde.
              if (provider.data.isOne) ...[
                if (provider.isNew) const Divider(),
                _residue(provider.data.first),
                ..._container(provider.data.first),
                ..._unitAndQuantity(provider.data.first),
                _observations(provider.data.first),
                SeparatorFiori.half(),
                _images(provider.data.first),
                SeparatorFiori.half(),
                if (provider.isNew) const Divider(),
              ]
              // Cuando hay más de uno, los mostramos en una lista de tarjetas.
              else
                ...provider.data
                    .map2((m) => ResidueRegisterMultipleDataWidget(m)),
              ..._buttons(),
            ],
          ),
          footer: provider.canSeeButtons
              ? EditableOptionWindowWidgetWeb(
                  isEditable: provider.isEditing || provider.isNew,
                  onPressedEdit: provider.onPressedEdit,
                  onPressedSave: provider.onPressedSave,
                  options: isMobileOrPWA ? null : provider.getOptions(),
                )
              : null,
        ),
      ),
    );
  }

  List<Widget> _cleanPointContributor(BuildContext context) {
    return [
      if (isDebugMode())
        TestInfo(
            "En las entradas de residuos no se filtran los residuos ni empresa."),
      MultiplatformFieldOption<String, CleanPointContributorBase>(
        title: TTPL.ciudadanoOEmpresa.tt.asterisk(provider.isEditing),
        isEqualsOptions: (o1, o2) => o1.guid == o2.guid,
        isEquals: (s, o) => s == o.guid,
        autoselectionRemoved: provider.autoselectionRemoved,
        selection: provider.contributorGuid,
        options: [
          ...cleanPointCompanyManager.models,
          ...citizenManager.models.map2(
              (e) => CleanPointContributorAdapterCitizen(e, user.companyId!)),
        ].sortByText((o) => o.name),
        isAvailable: (o) {
          if (o is CleanPointContributorAdapterCitizen) {
            return true;
          } else if (o is CleanPointCompanyModel) {
            return o.isInput;
          }
          return false;
        },
        getDisplayValue: (o) => o.nameEmailOrId,
        getTextFilter: (o) {
          if (o is CleanPointContributorAdapterCitizen)
            return [o.name, o.email, o.subscriberNumber].joinFilled(" - ");
          else if (o is CleanPointCompanyModel)
            return [o.name, o.responsible, o.cif].joinFilled(" - ");
          return "";
        },
        builder: (m) {
          String text = "";

          IconData icon = FontAwesomeIcons.circleQuestion;

          if (m is CleanPointContributorAdapterCitizen) {
            text = [m.name, m.email, m.subscriberNumber].joinFilled(" - ");
            if (text.isEmpty) text = m.citizen.idViewWithTitle;
            icon = FontAwesomeIcons.solidUser;
          } else if (m is CleanPointCompanyModel) {
            text = [m.name, m.responsible, m.cif].joinFilled(" - ");
            if (text.isEmpty) text = m.idViewWithTitle;
            icon = FontAwesomeIcons.building;
          }
          return AutocompleteInputItemIcon(
            icon: Icon(
              icon,
              size: 12,
            ),
            text: text,
          );
        },
        isEditable: () => provider.isEditing && provider.isNew,
        onSelected: (o) {
          provider.contributorGuid = o?.guid;
          provider.rebuild();
        },
        icon: FontAwesomeIcons.circlePlus,
      ),
      const SizedBox(height: 2),
      if (provider.isEditing && provider.isNew)
        Container(
          width: double.maxFinite,
          child: NotificationIconTextContainer(
            icon: FontAwesomeIcons.circlePlus,
            getText: () => TTShared.crear.tt.colon(TTPL.ciudadanoOEmpresa.tt),
            onPressed: () {
              WidgetUtil.cleanFocus(context);
              DialogService().showOptionDialogLittle(
                [
                  OptionButton(
                    text: TTShared.ciudadano.tt,
                    onPressed: () {
                      builders.citizenPageFactory.create(
                        cubit,
                        CitizenPageParam(
                          null,
                          (m) => provider.onContributorSaved(
                              CleanPointContributorAdapterCitizen(
                                  m, user.companyId!),
                              context),
                        ),
                      );
                    },
                    icon: FontAwesomeIcons.user,
                  ),
                  OptionButton(
                    text: TTShared.empresa.tt,
                    onPressed: () {
                      builders.cleanPointCompanyPageFactory.create(
                        cubit,
                        CleanPointCompanyPageParam(
                          null,
                          (m) => provider.onContributorSaved(m, context),
                        ),
                      );
                    },
                    icon: FontAwesomeIcons.building,
                  ),
                ],
              );
            },
            iconColor: themeColors.white,
            color: themeColors.primaryBlueLight,
            textColor: themeColors.white,
          ),
        ),
    ];
  }

  List<Widget> _buttons() {
    if (!provider.canSeeButtons) return [];

    List<Widget> children = [];

    if (provider.isNew) {
      children.addAll([
        Container(
          width: double.maxFinite,
          child: NotificationIconTextContainer(
            icon: FontAwesomeIcons.circlePlus,
            getText: () => TTShared.aniadir.tt.colon(TTShared.residuo.tt),
            onPressed: provider.onPressedAddOther,
            iconColor: themeColors.white,
            color: themeColors.primaryBlueLight,
            textColor: themeColors.white,
          ),
        ),
        SeparatorFiori.half(),
      ]);
    }

    return children;
  }
}

class ResidueRegisterMultipleDataWidget extends StatefulWidget {
  final ResidueRegisterInputData data;

  ResidueRegisterMultipleDataWidget(this.data);

  @override
  State<ResidueRegisterMultipleDataWidget> createState() =>
      _ResidueRegisterMultipleDataWidgetState();
}

class _ResidueRegisterMultipleDataWidgetState
    extends State<ResidueRegisterMultipleDataWidget> {
  ResidueRegisterInputData get d => widget.data;
  StreamSubscription? _sub;

  @override
  void initState() {
    super.initState();
    _sub = d.provider.rebuild.listen(() => setState(() {}));
  }

  void dispose() {
    _sub?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final headerRightIcons = [
      if (!d.isEditable) ...[
        TooltipCustom(
          message: TTShared.guardado.tt,
          child: FittedBox(
            child: IconButton(
              iconSize: 50,
              icon: Icon(
                FontAwesomeIcons.solidSave,
                color: themeColors.validGreen,
              ),
              onPressed: () {},
            ),
          ),
        ),
      ] else if (!d.isEditing) ...[
        TooltipCustom(
          message: TTShared.editar.tt,
          child: FittedBox(
            child: IconButton(
              iconSize: 45,
              icon: const Icon(FontAwesomeIcons.pen),
              onPressed: () {
                d.enableEditing();
              },
            ),
          ),
        ),
      ] else ...[
        TooltipCustom(
          message: TTShared.terminarLaEdicion.tt,
          child: FittedBox(
            child: IconButton(
              iconSize: 50,
              icon: const Icon(FontAwesomeIcons.circleCheck),
              onPressed: () {
                d.disableEditing();
              },
            ),
          ),
        ),
      ],
      if (!d.isSaved) ...[
        const SizedBox(width: 10),
        TooltipCustom(
          message: TTShared.eliminar.tt,
          child: FittedBox(
            child: IconButton(
              splashColor: themeColors.grey7,
              iconSize: 45,
              icon: const Icon(FontAwesomeIcons.solidTrashCan),
              onPressed: () {
                d.provider.onPressedRemoveData(d);
              },
            ),
          ),
        ),
      ],
    ];

    final r = d.getResidue();

    Widget content;

    if (d.isEditing) {
      final header = Container(
        height: isMobileOrPWA ? 35 : 20,
        child: Row(
          children: [
            BodyText(d.id.toString()),
            const Spacer(),
            ...headerRightIcons,
          ],
        ),
      );

      content = Column(
        children: [
          header,
          if (d.result?.isError == true) ...[
            if (!isMobileOrPWA) const Divider(),
            _result().separatorHalfBottom(),
          ],
          _residue(d),
          ..._container(d),
          ..._unitAndQuantity(d),
          _observations(d),
          SeparatorFiori.half(),
          _images(d),
        ],
      );
    } else {
      final header = Container(
        height: isMobileOrPWA ? 35 : 20,
        child: Row(
          children: [
            Expanded(
              child: BodyText(
                "${d.id} - ${r?.name ?? ''} - ${r?.lerCode?.code ?? ''} - ${formatPointThousandths(d.quantityField.text)}",
                maxLines: 1,
              ),
            ),
            ...headerRightIcons,
          ],
        ),
      );

      content = Column(
        children: [
          header,
          if (d.result?.isError == true) ...[
            if (!isMobileOrPWA) const Divider(),
            _result().separatorHalfTop(),
          ]
        ],
      );
    }

    if (isMobileOrPWA)
      return FadeInContainer(
        child: Container(
          margin: const EdgeInsets.only(top: 10),
          child: CardItemList(
            color: d.isEditing ? themeColors.grey1 : themeColors.white,
            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
            child: content,
          ),
        ),
      );

    return FadeInContainer(
      child: Container(
        margin: const EdgeInsets.only(top: 10),
        child: Container(
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: themeColors.white,
            borderRadius: BorderRadius.circular(themeContainers.borderRadius),
            border: Border.all(
              color: themeColors.black,
              width: 0.5,
            ),
          ),
          child: content,
        ),
      ),
    );
  }

  Widget _result() => Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            FontAwesomeIcons.solidCircleXmark,
            color: themeColors.errorRed,
            size: 14,
          ),
          const SizedBox(width: 10),
          Expanded(
            child: BodyText(
              d.result!.error ?? TTShared.haOcurridoUnError.tt,
              color: themeColors.errorRed,
            ),
          ),
        ],
      );
}

Widget _residue(ResidueRegisterInputData data) =>
    MultiplatformFieldOption<int, ResidueModel>(
      title: TTPL.residuo.tt.asterisk(data.isEditing),
      selection: data.residueId,
      autoselectionRemoved: data.autoselectionRemoved,
      isEquals: (s, o) => s == o.id,
      isEqualsOptions: (o1, o2) => o1.pk == o2.pk,
      options: residueManager.models.sortByText((o) => o.name),
      isAvailable: (o) =>
          (home?.selectedCleanPoint?.isAcceptedResidue(o) ?? false) &&
          (home?.selectedCleanPoint
                  ?.getElements(elementManager.models)
                  .getByResidue(o, equipmentManager.models)
                  .isFilled ??
              false),
      isEditable: () => data.isEditing,
      onSelected: (o) {
        data.onSelectedResidue(o, false);
        if (o == null) data.provider.stopElementsAnimation();
      },
      getDisplayValue: (o) {
        String displayValue = [o.name, o.lerCode?.code].joinFilled(
            " - "); //Por si el texto se pasa en la lista, que muestre un "..." al final
        if (displayValue.length > 60) {
          return '${displayValue.substring(0, 58)}...';
        }
        return displayValue;
      },
      getTextFilter: (o) => o.filter,
      icon: FontAwesomeIcons.solidTrashCan,
    ).separatorHalfBottom();

Widget _images(ResidueRegisterInputData d,
        [bool Function()? isShowedOnlyImages]) =>
    ImageListServiceBase64Widget(
      onImagesChanged: () {},
      imageService: imageService,
      isEditable: () => d.isEditing,
      maxImages: 3,
      images: d.images,
      isShowedOnlyImages: isShowedOnlyImages,
    );

Widget _observations(ResidueRegisterInputData d) {
  if (d.isEditing) {
    if (isMobileOrPWA)
      return TextAreaFieldV2(
          controller: d.observationsField, title: TTShared.observaciones.tt);

    return TitleInput(
      title: tt(TTShared.observaciones),
      child: FieldInput(
        d.observationsField,
        isEditable: () => d.isEditing,
        hintText: tt(TTShared.observaciones),
      ),
    );
  }
  return isMobileOrPWA
      ? FieldFormReadOnlyMobile(
          title: TTShared.observaciones.tt,
          icon: FontAwesomeIcons.penClip,
          content: d.observationsField.text,
        )
      : FieldFormReadOnlyWeb(
          title: TTShared.observaciones.tt,
          content: d.observationsField.text,
        );
}

List<Widget> _unitAndQuantity(ResidueRegisterInputData d) {
  final r = residueManager.models.firstWhereOrNull(
    (e) => e.id == d.residueId,
  );

  final unit =
      !isStringNullOrEmpty(r?.unit) ? r!.unit! : TTShared.desconocida.tt;

  if (d.isEditing) {
    return [
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: FieldFormReadOnlyMultiplatform(
              title: TTShared.unidadDeMedida.tt,
              icon: FontAwesomeIcons.tag,
              content: unit,
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: isMobileOrPWA
                ? NumberDecimalField(
                    controller: d.quantityField,
                    isEditable: () => d.isEditing,
                    title: TTShared.cantidad.tt,
                    icon: FontAwesomeIcons.weightHanging,
                    minValue: 0,
                    maxValue: 999999,
                  )
                : TitleInput(
                    title: TTShared.cantidad.tt,
                    child: FieldInput(
                      d.quantityField,
                      isEditable: () => d.isEditing,
                      hintText: tt(TTShared.cantidad),
                    ),
                  ),
          ),
        ],
      ),
      const SizedBox(height: 10),
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: isMobileOrPWA
                ? NumberDecimalField(
                    controller: d.unitLumpsField,
                    isEditable: () => d.isEditing,
                    title: TTShared.unidadBultos.tt,
                    icon: FontAwesomeIcons.weightHanging,
                    minValue: 0,
                    maxValue: 999999,
                  )
                : TitleInput(
                    title: tt(TTShared.unidadBultos),
                    child: FieldInput(
                      d.unitLumpsField,
                      isEditable: () => d.isEditing,
                      hintText: tt(TTShared.unidadBultos),
                    ),
                  ),
          ),
          const Spacer(flex: 1),
        ],
      ),
      SeparatorFiori.half(),
    ];
  }

  return [
    Row(
      children: [
        Expanded(
          child: FieldFormReadOnlyMultiplatform(
            title: TTShared.unidadDeMedida.tt,
            icon: FontAwesomeIcons.tag,
            content: unit,
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: FieldFormReadOnlyMultiplatform(
            title: TTShared.cantidad.tt,
            icon: FontAwesomeIcons.weightHanging,
            content: d.quantityField.text,
          ),
        ),
      ],
    ),
    const SizedBox(height: 10),
    Row(
      children: [
        Expanded(
          child: FieldFormReadOnlyMultiplatform(
            title: tt(TTShared.unidadBultos),
            icon: FontAwesomeIcons.weightHanging,
            content: d.unitLumpsField.text,
          ),
        ),
        const Spacer(flex: 1),
      ],
    ),
    SeparatorFiori.half(),
  ];
}

List<Widget> _container(ResidueRegisterInputData d) {
  if (d.residueId == null) return [];

  return [
    MultiplatformFieldOption<ResidueRegisterElementModel, ElementModel>(
      title: TTShared.contenedores.tt.asterisk(d.provider.isEditing),
      selection: d.element,
      options: [
        ...elementManager.models,
        ...elementManager.modelsRemoved,
      ].sortByText((o) => o.name),
      autoselectionRemoved: d.autoselectionRemoved,
      // Esto indica que se vuelve a elegir automáticamente si solo hay una opción icluso tras hacer rebuild
      isRequired: true,
      isAvailable: (o) => d.isAvailableElement(o),
      isEditable: () => d.isEditing,
      getDisplayValue: (o) => o.name,
      getTextFilter: (o) => o.filter,
      isEquals: (s, o) => s.elementGuid == o.guid,
      isEqualsOptions: (o1, o2) => o1.guid == o2.guid,
      onSelected: (o) {
        d.element = null;
        if (o != null) {
          d.element = ResidueRegisterElementModel(
            // This will be replace before save.
            residueRegisterGuid: "",
            elementGuid: o.guid,
            id: 0,
          );

          if (!isMobileOrPWA && o.hasPosition()) {
            global.centerMarkerJumping(
                o.lat, o.lng, o.markerId, o.markerCategory, false);
          }
        } else
          d.provider.stopElementsAnimation();
        d.provider.rebuild();
      },
      icon: FontAwesomeIcons.dumpster,
      builder: (m) => AutocompleteInputItemIcon(
        icon: m.getIcon(m.getEquipment(equipmentManager.modelsMap)),
        text: m.name,
        iconPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 6),
      ),
    ),
    SeparatorFiori.half(),
  ];
}
