import '../../../_imports.dart';

class MapTab extends StatelessWidget {
  static late final _floatingButtonSize = ResponsiveData<double>(
    mobileSmall: 80,
    mobileNormal: 90,
    mobileLarge: 100,
    tabletSmall: 100,
    tabletNormal: 110,
    tabletLarge: 120,
    defaultData: 80,
  );
  final LocalHomeProvider? bloc;
  final GlobalKey mapKey;

  MapTab(this.bloc, this.mapKey);

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<HomeProvider>(context);

    if (isNull(bloc?.mapBloc)) return SizedBox();

    final body = Scaffold(
      floatingActionButton: StreamBuilderMultiple([
        home!.onChangedCleanPoint.stream,
        cleanPointBuildingManager.onChange,
        cleanPointBuildingManager.onStarted,
      ], (_) {
        List<FloatingButtonData> floatingButtons = [];

        if (isMobileOrPWA) {
          if (home!.selectedCleanPoint != null &&
              (home!.cleanPointChangeButton.isVisible?.call() ?? false))
            floatingButtons.add(FloatingButtonData(
              icon: home!.cleanPointChangeButton.icon,
              tooltip: home!.cleanPointChangeButton.getTitle(),
              onPressed: home!.cleanPointChangeButton.onPressed,
            ));

          if (home!.cleanPointInfoButton.isVisible?.call() ?? false)
            floatingButtons.add(FloatingButtonData(
              icon: FontAwesomeIcons.info,
              tooltip: home!.cleanPointInfoButton.getTitle(),
              color: home!.selectedCleanPoint == null
                  ? themeColors.errorRed
                  : null,
              onPressed: home!.cleanPointInfoButton.onPressed,
            ));

          if (home?.selectedCleanPoint != null) {
            floatingButtons.addAll([
              FloatingButtonData(
                  icon: FontAwesomeIcons.minus,
                  tooltip: TTPL.salidaDeResiduos.tt,
                  onPressed: () => builders.residueRegisterPageFactory.create(
                        cubit,
                        ResidueRegisterParam(
                          type: ResidueRegisterType.output,
                          model: null,
                        ),
                      )),
              FloatingButtonData(
                  icon: FontAwesomeIcons.plus,
                  tooltip: TTPL.entradaDeResiduo.tt,
                  onPressed: () => builders.residueRegisterPageFactory.create(
                        cubit,
                        ResidueRegisterParam(
                          type: ResidueRegisterType.input,
                          model: null,
                        ),
                      )),
              // if (isMobile)
              //   FloatingButtonData(
              //     icon: home!.readQRMobileButton.icon,
              //     tooltip: TTPL.leerQRDeReutilizableCiudadanoOSolicitud.tt,
              //     color: themeColors.secondaryGreen,
              //     onPressed: () => home!.readQRMobileButton.onPressed(),
              //   ),
            ]);
          }
        }

        return FloatingActionButtonsColumn(
          floatingButtons,
          size: _floatingButtonSize.get(),
        );
      }),
      body: Stack(
        children: [
          HereMap(
            bloc: bloc!.mapBloc,
            mapKey: mapKey,
          ),
          HereMapInterfaceWidget(
            preferenceMyHouse: preferences.homeLocation,
            bloc: bloc!.mapBloc,
            suggestionStreetHelper: SuggestionStreetHelper(
              connection: connection,
              kilometerSearchRange: 1000,
              mapBloc: bloc!.mapBloc,
              mapInfoService: mapInfoService,
              searchFieldController: bloc!.searchController,
            ),
            additionalPosition: bloc!.getAdditionalPosition(),
          ),
          if (isWeb)
            Align(
              alignment: Alignment.bottomLeft,
              child: FloatingWindowCollapseListWidget(cubit),
            ),
        ],
      ),
    );

    if (isMobileOrPWA) return body;

    return Column(
      children: [
        Expanded(child: body),
        BottomMenuWidget(provider),
      ],
    );
  }
}
