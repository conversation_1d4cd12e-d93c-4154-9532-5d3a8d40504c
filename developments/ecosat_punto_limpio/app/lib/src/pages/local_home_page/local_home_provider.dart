import '../../_imports.dart';

void onPressedChangeLanguage() {
  home!.translationProvider.showLanguagueSelection(global.languages);
}

void onPressedRestartData() async {
  //En web no se la ventana de confirmación sin un delay.
  await Future.delayed(const Duration(milliseconds: 200));
  final isConfirmed = await (DialogService().showConfirmDialog(
    tt(TTShared.quieresRestablecerLosDatosPregunta) +
        "\n\n" +
        tt(TTShared
            .todosLosDatosSeRestableceranYSeraNecesarioVolverAIniciarSesion),
  ));

  if (!isConfirmed) return;

  // ignore: unawaited_futures
  DialogService().showProgressDialog((dialog, context) async {
    await DatabaseHiveService().repair(isRemovedAll: true);
    // await environment.backgroundClear();
    await injector.get<RestartServiceBase>().restart();
  });
}

class LocalHomeProvider {
  static final _tag = "LocalHomeProvider";
  bool _isSettedBackgroundData = false;

  late final DelayedRenewableOperation delayedOnChangedCleanPoint;

  CleanPointBuildingModel? selectedCleanPoint;
  CleanPointBuildingModel? temporalCleanPoint;

  bool listenMapMovement = true;

  LatLng? getAdditionalPosition() {
    return selectedCleanPoint?.getPosition();
  }

  late final cleanPointInfoButton = ActionDataFunction(
      icon: FontAwesomeIcons.circleInfo,
      isVisible: () => cleanPointBuildingManager.models.isNotEmpty,
      getTitle: () =>
          selectedCleanPoint?.name ?? TTPL.seleccionarPuntoLimpio.tt,
      onPressed: () {
        if (home?.selectedCleanPoint != null) {
          builders?.cleanPointPageFactory.create(cubit, selectedCleanPoint!);
        } else {
          home?.onPressedSelectCleanPoint();
        }
      });

  late final cleanPointChangeButton = ActionDataFunction(
    icon: FontAwesomeIcons.retweet,
    isVisible: () => cleanPointBuildingManager.models.isGreaterOne,
    getTitle: () => TTPL.cambiarPuntoLimpio.tt,
    onPressed: onPressedSelectCleanPoint,
  );

  late final readQRMobileButton = ActionDataFunction(
    icon: FontAwesomeIcons.qrcode,
    getTitle: () => TTShared.leerQR.tt,
    onPressed: () async {
      final qr = await qrService.execute();
      if (qr.isEmpty) return;

      if (productManager.models.isQRMine(qr)) {
        final product = productManager.models.getByQR(qr);
        if (product == null) {
          await DialogService().showInfoDialog(TTShared.noSeHaEncontrado.tt);
          return;
        }

        builders!.productPageFactory
            .create(cubit, ProductPageParam.model(product));
        return;
      }

      if (productRequestManager.models.isQRMine(qr)) {
        final request = productRequestManager.models.getByQR(qr);
        if (request == null) {
          await DialogService().showInfoDialog(TTShared.noSeHaEncontrado.tt);
          return;
        }

        builders!.productRequestPageFactory.create(cubit, request);
        return;
      }

      if (citizenManager.models.isQRMine(qr)) {
        final person = citizenManager.models.getByQR(qr);
        if (person == null) {
          await DialogService().showInfoDialog(TTShared.noSeHaEncontrado.tt);
          return;
        }

        builders!.citizenPageFactory.create(
          cubit,
          CitizenPageParam(person),
        );
        return;
      }
    },
  );

  final searchController = FieldController();
  final translationProvider = TranslationProvider();
  final onChangedCleanPoint = StreamCustomEmpty();
  StreamSubscription? subOnUpdatedCleanPointSelectd;

  late MarkerManager<ElementModel, String> elementMarkerManager;
  late MarkerAreaManager<CleanPointBuildingModel, String>
      cleanPointMarkerAreaManager;
  late MenuSSOProvider menuSSOProvider;

  final hiddenElementsMapOnChange =
      PreferenceNotifier(preferences.hiddenEquipmentMap).onChanged;
  final hiddenElementsMap = PreferenceList(preferences.hiddenEquipmentMap);

  Map<ScheduleDay, bool> daysOpen = {
    ScheduleDay.monday: true,
    ScheduleDay.tuesday: true,
    ScheduleDay.wednesday: true,
    ScheduleDay.thursday: true,
    ScheduleDay.friday: true,
    ScheduleDay.saturday: true,
    ScheduleDay.sunday: true,
  };

  late MapBlocBasic mapBloc;
  FloatingWindowCubit get cubit =>
      GlobalBloc().homeProvider!.floatingWindowCubit;
  // Manager<UserSSOModel, int> get userManager =>
  //     injector.get<Manager<UserSSOModel, int>>();

  final _areaPolygonPlugin = AreaPolygonPlugin(positionUtilService);
  final _areaCirclePlugin = AreaCirclePlugin(positionUtilService);

  FloatingWindowBuilders? builders;
  List<StreamSubscription> _subscriptions = [];

  LocalHomeProvider({List<MapType>? mapTypes}) {
    mapBloc = MapBlocBasic(
      connection,
      mapTypes: mapTypes,
      additionalPlugins: [_areaPolygonPlugin, _areaCirclePlugin],
      preferenceHomeLocation: preferences.homeLocation,
      getBoundLimit: _getMapBoundLimit,
      languageType: TranslationServiceGlobal().language,
    );
    menuSSOProvider = MenuSSOProvider(
      expandedPref: preferences.expandedPref,
      isUserInternal: user.isInternal,
      menuSSOManager: menuSSOManager,
      onPressed: _onPressedMenuAction,
      getNotificationCount: _getNotificationCount,
      isVisibleMenuOption: _isVisibleMenuOption,
    );
    mapBloc.markerPlugin.isSleepInBackground = isMobileOrPWA;
    _subscriptions.add(cleanPointBuildingManager.onChange.listen((event) {
      if (selectedCleanPoint == null) return;
      if (selectedCleanPoint!.pk != event.after.pk) return;
      if (event.type == OnChangedType.remove)
        selectedCleanPoint = null;
      else
        selectedCleanPoint = event.after;
      onChangedCleanPoint();
    }));

    onChangedCleanPoint.listen(() {
      menuSSOProvider.rebuild();
      GlobalBloc().homeProvider?.rebuild();
    });

    delayedOnChangedCleanPoint = DelayedRenewableOperation(
      delay: const Duration(milliseconds: 1000),
      function: () {
        onChangedCleanPoint();
      },
    );

    _start();
  }

  List<ActionDataFunction> getAdditionalActionsWeb() => [
        home!.cleanPointInfoButton,
        home!.cleanPointChangeButton,
      ];

  List<Stream> getAdditionalActionsRebuilds() => [
        cleanPointBuildingManager.onChange,
        cleanPointBuildingManager.onStarted,
      ];

  /// Indicates if the project has EcoReutiliza.
  ///
  /// This is done checking one of the EcoReutiliza menus.
  bool get hasEcoReutiliza => menuSSOManager.models
      .any((m) => m.hasMenuAction(MenuAction.gestionEntradaReutilizables));

  Future<MapBoundLimit?> _getMapBoundLimit() async {
    final u = await getMapBoundLimit(connection, syncService);
    if (u == null) return null;
    return MapBoundLimit(
      northEast: u.object1,
      southWest: u.object2,
      positionUtilService: positionUtilService,
    );
  }

  bool _isVisibleMenuOption(MenuSSOOptionModel option) {
    //Opciones del menú que solamente son EcoReutiliza, sin actionId
    switch (option.textId) {
      case "Ecoreutiliza": // 25333:
        return selectedCleanPoint?.hasEcoReutiliza ?? false;
    }

    //Opciones del menú que solamente son EcoReutiliza, con actionId
    switch (option.actionIdEnum) {
      case MenuAction.gestionSalidaReutilizables:
      case MenuAction.gestionEntradaReutilizables:
      case MenuAction.gestionSolicitudReutilizable:
        return selectedCleanPoint?.hasEcoReutiliza ?? false;
      default:
        return true;
    }
  }

  Future<int>? _getNotificationCount(MenuAction a) {
    if (selectedCleanPoint == null) return null;

    switch (a) {
      case MenuAction.gestionSolicitudReutilizable:
        return Future.value(
          productRequestManager.models
              .where2((m) =>
                  m.stateEnum == ProductRequestState.pending &&
                  m.cleanPointGuid == selectedCleanPoint?.guid)
              .length,
        );
      default:
        break;
    }
    return null;
  }

  void _onPressedMenuAction(MenuAction a) async {
    switch (a) {
      case MenuAction.gestionSolicitudReutilizable:
        builders!.productRequestListPage.create(
          cubit,
          ProductRequestListPageParam(
            visibleStates: [ProductRequestState.pending],
            isOnlyWeb: false,
          ),
        );
        break;
      case MenuAction.informacionListadoHistoricoReutilizable:
        builders!.productRequestListPage.create(
          cubit,
          ProductRequestListPageParam(
            visibleStates: [
              ProductRequestState.accepted,
              ProductRequestState.rejected
            ],
            isOnlyWeb: false,
          ),
        );
        break;
      case MenuAction.gestionEntradaReutilizables:
        builders!.productListPage.create(
          cubit,
          ProductListPageParam(
            visibleState: ProductState.available,
            isOnlyWeb: false,
          ),
        );
        break;
      case MenuAction.gestionSalidaReutilizables:
        builders!.productListPage.create(
          cubit,
          ProductListPageParam(
            visibleState: ProductState.acquired,
            isOnlyWeb: false,
          ),
        );
        break;
      case MenuAction.configCategoriaReutilizable:
        builders!.productCategoryListPage.create(cubit, false);
        break;
      case MenuAction.elementos:
        builders!.equipmentListPage.create(cubit, false);
        break;
      case MenuAction.proyecto:
        String url = "https://www.movisat.com/";

        await DialogService().showProgressDialog((dialog, context) async {
          ConfigurationModel? model =
              await configurationManager.getByPkStartingManager(
                  ConfigurationKey.urlProject.getPk(user.app, user));

          if (model == null)
            model = await configurationUseCase
                .getByKey(ConfigurationKey.urlProject);

          url = model?.value ?? url;
        });

        if (url.isFilled) {
          if (kIsWeb)
            injector.get<ExternalLinkServiceBase>().url(url);
          else
            builders!.externalLinkPage.create(
              cubit,
              ExternalLinkParam(url: url, title: TTShared.proyecto.tt),
            );
        }

        break;
      case MenuAction.bdtSincronizar:
        await syncService.forceSync();
        break;
      case MenuAction.gestionEntradas:
        builders!.registerResidueListPage.create(cubit,
            ResidueRegisterListPageParam(ResidueRegisterType.input, false));
        return;
      case MenuAction.gestionSalidas:
        builders!.registerResidueListPage.create(cubit,
            ResidueRegisterListPageParam(ResidueRegisterType.output, false));
        return;
      case MenuAction.configProyectoPdf:
        builders!.settingsPageFactory.create(
            cubit,
            SettingsPageParam(
              settingsManager.getSettings(),
              (m) => m,
            ));
        break;
      case MenuAction.gestionEquipamientos:
        builders!.elementListPage.create(cubit, false);
        break;
      case MenuAction.gestionEmpresas:
        builders!.cleanPointCompanyListPage.create(cubit, false);
        break;
      case MenuAction.cartografiaZonas:
        builders!.cleanPointListPage.create(
            cubit,
            CleanPointListParam(
                false,
                await ssoUC.hasAppTheCompany(
                    user.companyIdSSO, SSOApplication.EcoReutiliza)));
        break;
      case MenuAction.gestionPuntosLimpios:
        // ignore: unawaited_futures
        onPressedSelectCleanPoint();
        break;
      case MenuAction.configUtilidadesRestDat:
        onPressedRestartData();
        break;
      case MenuAction.configUtilidadesUsua:
        builders!.userSSOListPage.create(cubit, false);
        break;
      case MenuAction.configUtilidadesIdio:
        onPressedChangeLanguage();
        break;
      case MenuAction.personas:
        builders!.cleanPointPersonListPage.create(
          cubit,
          CleanPointPersonListPageParam(
            isOnlyWeb: false,
          ),
        );
        break;
      case MenuAction.gestionCiudadanos:
        if (home?.selectedCleanPoint != null) {
          builders!.citizenListPage.create(
            cubit,
            CitizenListPageParam(isOnlyWeb: false),
          );
        } else {
          DialogService()
              .showInfoDialog(TTShared.primeroSeleccionaPuntoLimpio.tt);
        }
        break;
      case MenuAction.configCartografiaCasa:
        onPressedCartographyHome();
        break;
      case MenuAction.configProyectoIntrUrl:
        builders!.urlProjectConfigPage.create(cubit, null);
        break;
      case MenuAction.gestionInventario:
        builders!.registerResidueElementCalculationListPage.create(cubit, null);
        break;
      case MenuAction.informacionListadoHistoricoGestion:
        builders!.registerResidueElementCalculationListHistoryPage
            .create(cubit, null);
        break;
      case MenuAction.informacionListadoAccesos:
        if (user.isAdmin && user.isInternal) {
          builders!.userAccessListPage.create(cubit, null);
        } else {
          // ignore: unawaited_futures
          DialogService()
              .showInfoDialog(TTShared.noTienesAccesoAEsteApartado.tt);
        }
        break;
      case MenuAction.configGestionLimit:
        if (selectedCleanPoint == null) return;
        builders!.cleanPointMaxResidueDayPageFactory
            .create(cubit, selectedCleanPoint!);
        break;
      case MenuAction.ConfigLOPD:
        if (!user.isAdmin) {
          // ignore: unawaited_futures
          DialogService()
              .showInfoDialog(TTShared.noTienesAccesoAEsteApartado.tt);
          return;
        }

        builders!.lopdListPage.create(cubit, null);
        break;
      case MenuAction.configElementosSelec:
        builders!.filterElementsPage.create(cubit, null);
        break;
      case MenuAction.historicoGeneral:
        builders!.residueRegisterHistoryListPage.create(cubit, null);
        break;
      default:
        break;
    }
  }

  void onPressedCartographyHome() {
    builders!.homeLocationConfigPage.create(cubit, null);
  }

  Future<void> onPressedSelectCleanPoint() async {
    if (cleanPointBuildingManager.isEmpty) {
      if (syncService.isSyncInProgress || !syncService.isFirstSyncCompleted) {
        await DialogService().showInfoDialog(TTShared.sincronizandoDatos.tt);
      } else if (user.role == SSORole.managerCleanPoint) {
        await DialogService().showInfoDialog(
            TTPL.noTienesPuntosLimpiosAsignadosContactaConUnAdministrador.tt);
      } else {
        await DialogService().showInfoDialog(TTPL.aunNoExistenPuntosLimpios.tt);
      }
    } else {
      await DialogService().showSelectWidgetDialog<CleanPointBuildingModel>(
        isShowedRemoveOptionButton: true,
        selection: selectedCleanPoint,
        options: cleanPointBuildingManager.models.sortByText((o) => o.name),
        isEquals: (a, b) => a.pk == b.pk,
        onSelected: (o) async {
          selectedCleanPoint = o;
          onChangedCleanPoint();
          selectedCleanPoint?.centerInMapByPolygonCenter();
          await preferences.selecteCleanPointGuid
              .set(selectedCleanPoint?.guid ?? "");
        },
        getTextFilter: (o) => o.filter,
        builder: (o) => CardItemList(
          padding: const EdgeInsets.all(8),
          color: themeColors.white,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              BodyText(o.name),
            ],
          ),
        ),
        getDisplayValue: (o) => o.name,
      );
    }
  }

  Future<void> _start() async {
    if (isDebugMode()) {
      TestInfo.isShowedStream(await preferences.isShowedDebugInfo.get());
      TestInfo.isShowedStream
          .listen((event) => preferences.isShowedDebugInfo.set(event));
    }
    // Lo restablecemos por si ha hecho relogin.
    GlobalBloc().onApplicationStarting.sink(true);
    if (isListNullOrEmpty(appbarFixedActions))
      appbarFixedActions.add(
        ConnectionButtonAppBar(
          connection: connection,
          hasServerConnection: () => token.isValid,
          onChangedServerConnection: token.onChangedIsValid.map((v) => null),
          onSynchronizeModels: syncService.onSyncModels.stream,
          isSynchronize: () => syncService.isSyncInProgress,
          isAnySyncExecuted: () => syncService.isAnySyncExecuted,
          onApplicationStarting: GlobalBloc().onApplicationStarting,
        ),
      );

    elementMarkerManager = MarkerManager<ElementModel, String>(
      markerPlugin: mapBloc.markerPlugin,
      getMarkerId: (m) => m.guid,
      resetAllMarkersStreams: [
        equipmentManager.onChange,
        hiddenElementsMapOnChange,
        // We reset the elements marker because the CleanPoints affect to the visibility of the marker.
        cleanPointBuildingManager.onChange,
        // We show only the containers of the selected CleanPoint in the map.
        onChangedCleanPoint.stream,
      ],
      getMarker: (m) => m.getMarker(),
      manager: elementManager,
      markerCategory: elementMarkerCategory,
    );

    cleanPointMarkerAreaManager =
        MarkerAreaManager<CleanPointBuildingModel, String>(
      circlePlugin: mapBloc.getPlugin<AreaCirclePlugin>()!,
      polygonPlugin: mapBloc.getPlugin<AreaPolygonPlugin>()!,
      getMarkerId: (m) => m.id.toString(),
      getMarker: (m) => m.getMarkerArea(
        zoneManager.models,
        isVisible: () =>
            selectedCleanPoint == null ||
            selectedCleanPoint!.guid == m.guid ||
            temporalCleanPoint?.guid == m.guid,
      ),
      onPressed: (m) => builders!.cleanPointPageFactory.create(cubit, m),
      resetAllMarkersStreams: [
        // We reset all the markers because CleanPoint needs to perform calculations for the marker color.
        cleanPointBuildingManager.onChange,
        // We reset all CleanPoints when the areas are changed.
        zoneManager.onChange,
        // We show only the selected CleanPoint in the map.
        onChangedCleanPoint.stream,
      ],
      manager: cleanPointBuildingManager,
    );

    _subscriptions.add(cleanPointBuildingManager.onStarted.listen((e) {
      preferences.selecteCleanPointGuid.get().then((value) {
        if (value.isEmpty) {
          _autoselectCleanPoint();
          return;
        }
        selectedCleanPoint = cleanPointBuildingManager.getByPk(value);
        onChangedCleanPoint();
      });
    }));

    _subscriptions.add(syncService.onSyncInProgress.listen((e) {
      if (!syncService.isSyncInProgress) {
        _autoselectCleanPoint();

        // We need to set the user clean points to filter the product request notifications.
        if (!_isSettedBackgroundData) {
          _isSettedBackgroundData = true;
          // environment.backgroundSetData();
        }
      }
    }));

    _subscriptions.add(cleanPointBuildingManager.onChange.listen((e) {
      // We need to set the user clean points to filter the product request notifications.
      // environment.backgroundSetData();
    }));

    _subscriptions.add(userSSOManager.onChange.listen((e) {
      // Si es el usuario actual, reconstruyo la página.
      if (e.after.id == user.id) GlobalBloc().homeProvider!.rebuild();
    }));

    _subscriptions.add(productRequestManager.onChange.listen((e) {
      menuSSOProvider.rebuild();
    }));

    _subscriptions.add(productRequestManager.onStarted.listen((e) {
      menuSSOProvider.rebuild();
    }));

    _subscriptions.add(mapBloc.onMovedMap.stream.listen((event) async {
      if (listenMapMovement) {
        if(temporalCleanPoint != null){
          temporalCleanPoint = null;
          delayedOnChangedCleanPoint.execute();
        }
      } else {
        await Future.delayed(const Duration(milliseconds: 2000));
        listenMapMovement = true;
      }
    }));

    _subscriptions.add(translationProvider.onChange.listen((e) {
      if (TranslationServiceGlobal().language == null) return;
      mapBloc.tilePlugin.setLanguage(TranslationServiceGlobal().language!);
    }));

    await settingsManager.start();
    await configurationManager.start();
    await lopdApplicationManager.start();
    await equipmentManager.start();
    await residueManager.start();
    await cleanPointBuildingManager.start();
    await cleanPointPersonManager.start();
    await userSSOManager.start();
    await zoneManager.start();
    await hiddenElementsMap.start();
    await productCategoryManager.start();
    await productManager.start();
    await productRequestManager.start();
    await residueRegisterElementCalculationManager.start();

    // ignore: unawaited_futures
    Future.delayed(
      const Duration(seconds: 2),
      () {
        GlobalBloc().onApplicationStarting.sink(false);
        injector.get<SyncServiceBase>().start();
      },
    );
  }

  void _autoselectCleanPoint() {
    // It has been request to autoselect the cleanpoint when there is only one or the user is a manager.
    if (selectedCleanPoint == null &&
        cleanPointBuildingManager.isNotEmpty &&
        (cleanPointBuildingManager.models.isOne || user.isCleanPointManager)) {
      selectedCleanPoint = cleanPointBuildingManager.models.first;
      onChangedCleanPoint();
      preferences.selecteCleanPointGuid.set(selectedCleanPoint?.guid ?? "");
    }
  }

  void dispose() {
    _subscriptions.forEach((s) => s.cancel());
    mapBloc.dispose();
    elementMarkerManager.dispose();
    searchController.dispose();
  }
}
