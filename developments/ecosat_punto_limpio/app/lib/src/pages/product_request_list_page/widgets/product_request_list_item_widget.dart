import '../../../_imports.dart';

class ProductRequestListItemWidget extends StatelessWidget {
  final ProductRequestModel model;
  final ProductRequestListProvider provider;

  ProductRequestListItemWidget({
    required this.model,
    required this.provider,
  });

  @override
  Widget build(BuildContext context) {
    late Color stateColor;

    switch (model.stateEnum) {
      case ProductRequestState.pending:
        stateColor = themeColors.errorRed;
        break;
      case ProductRequestState.accepted:
      case ProductRequestState.rejected:
      case ProductRequestState.canceled:
      case ProductRequestState.expired:
      case ProductRequestState.ended:
        stateColor = themeColors.grey6;
        break;
    }

    final citizen = citizenManager.getByPk(model.citizenGuid);
    return FullWidthButtonItemListV2(
      onPressed: () => builders.productRequestPageFactory.create(cubit, model),
      child: Container(
        child: Row(
          children: <Widget>[
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  H1Text(
                    model.name,
                    textAlign: TextAlign.left,
                    maxLines: 2,
                    color: themeColors.secondaryGreenDark,
                  ),
                  TooltipCustom(
                    message: TTShared.estado.tt,
                    child: H3Text(
                      model.stateEnum.tt,
                      textAlign: TextAlign.left,
                      color: stateColor,
                      maxLines: 1,
                    ),
                  ),
                  TooltipCustom(
                    message: TTShared.ciudadano.tt,
                    child: H3Text(
                      citizen?.name ?? TTShared.desconocido.tt,
                      textAlign: TextAlign.left,
                      maxLines: 1,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
