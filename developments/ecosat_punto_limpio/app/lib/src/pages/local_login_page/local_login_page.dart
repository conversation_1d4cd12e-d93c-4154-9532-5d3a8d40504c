import '../../_imports.dart';

class LocalLoginPage extends StatefulWidget {
  @override
  _LocalLoginPageState createState() => _LocalLoginPageState();
}

class _LocalLoginPageState extends State<LocalLoginPage> {
  final loginBloc = LoginProvider(
    languages: global.languages,
    loginUseCase: LoginUseCase(
      isValidTicket: () => global.isValidTicket(),
      pushLoginPage: () {},
      autologinUserEmail: preferences.autologinUserEmail,
      autologinUserPassword: preferences.autologinUserPassword,
      autologinSSOCompanyId: preferences.autologinSSOCompanyId,
      autologinSSORoleId: preferences.autologinSSORoleId,
      ticketUseCase: injector.get<TicketUseCase>(),
      connection: connection,
      applicationId: user.app.id,
      setTicket: (t) => global.ticket = t,
      onCompletedLogin: global.onCompletedLogin,
      getLoggedUser: global.getLoggedUser,
      app: user.app,
      ssoUseCase: ssoUseCase,
    ),
  );

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      // When the login page is loaded, we remove all background data.
      // This is because a error can be produced in the autologin.
      // environment.backgroundSetData();
    });

    return LoginPage(
      loginBloc,
      appNameLogo: ImageCacheService()
          .getFromAsset("assets/images/ecosat_punto_limpio.png"),
      onRestorePassword: () => UserRecoverPasswordBloc(
        connection: injector.get<ConnectionServiceBase>(),
        onClosed: () => NavigatorService().pop(),
      ).openPage(),
      loginWithEmail: true,
    );
  }
}
