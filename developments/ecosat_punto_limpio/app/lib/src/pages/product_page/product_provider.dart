import '../../_imports.dart';

class ProductProvider extends ProviderBaseV2 {
  List<StreamSubscription> subscriptions = [];
  List<Type> autoselectionRemoved = [];

  List<String> images = [];

  final nameField = FieldController(isRequired: true);
  final descriptionField = FieldController(isRequired: true);
  String? elementGuid;
  String? citizenDeliveredGuid;
  String? citizenAcquiredGuid;
  int? categoryId;
  int? residueId;
  ProductModel? _savedModel;

  bool isEditing = false;

  ProductModel? get model => param.model;
  ProductRequestModel? get request => param.request;

  final ProductPageParam param;

  ProductProvider(this.param);

  @override
  void internalDispose() {
    nameField.dispose();
    descriptionField.dispose();
    subscriptions.forEach((element) => element.cancel());
  }

  Future<void> internalStart() async {
    await elementManager.start();
    await productCategoryManager.start();

    isEditing = model == null;

    if (request != null) {
      nameField.text = request!.name;
      descriptionField.text = request!.description;
      for (String image in request!.imagesOrIdsMd5) {
        final img = await productRequestUseCase.getImage(image);
        if (isStringFilled(img)) images.add(img);
      }
      categoryId = request!.categoryId;
      citizenDeliveredGuid = request!.citizenGuid;
    }

    if (model != null) {
      descriptionField.text = model!.description;
      nameField.text = model!.name;

      for (String image in model!.imagesOrIdsMd5) {
        final img = await productUseCase.getImage(image);
        if (isStringFilled(img)) images.add(img);
      }

      categoryId = model!.categoryId;
      elementGuid = model!.elementGuid;
      citizenDeliveredGuid = model!.deliveredCitizenGuid;
      citizenAcquiredGuid = model!.acquiredCitizenGuid;
      residueId = model!.residueId;
    }
  }

  bool get isNew => model == null;

  bool get isRemoved => model?.isRemoved ?? false;

  onPressedEdit() {
    isEditing = true;
    rebuild();
  }

  List<ActionDataFunction>? getOptions() {
    if (model == null || !isEditing) return null;
    List<ActionDataFunction> actions = [];

    actions.add(ActionDataFunction(
      icon: HomePrefs.actionRemove.icon,
      getTitle: HomePrefs.actionRemove.getTitle,
      onPressed: () => onPressedProductRemove(model!),
    ));

    actions.add(
      ActionDataFunction(
        icon: HomePrefs.actionPdf.icon,
        getTitle: HomePrefs.actionPdf.getTitle,
        onPressed: () => model!.toPdf(),
      ),
    );

    return actions;
  }

  void onPressedProductRemove(ProductModel product) {
    DialogService().showConfirmResultDialog(
      question: tt(TTShared.quieresEliminarElRegistroPregunta),
      operation: () => productUseCase.removeAll([product]),
      completion: TTShared.registroEliminado.tt,
      onCompleted: () => builders.productPageFactory.remove(
        cubit,
        ProductPageParam(
          model: model,
          request: request,
        ),
      ),
    );
  }

  Future<void> onPressedSave() async {
    final isConfirmed = await DialogService().showConfirmDialog(
      translate(TTShared.quieresGuardarLosCambiosRealizados),
    );
    if (!isConfirmed) return;

    await _createOrModify(citizenAcquiredGuid.isFilled
        ? citizenAcquiredGuid!
        : model?.acquiredCitizenGuid ?? "");
  }

  Future<void> _createOrModify(String acquiredCitizenGuid) async {
    await DialogService().showProgressDialog((dialog, context) async {
      if (_savedModel == null) {
        final guid = model?.guid ?? Guid().get();

        final deliveredUserGuid = [
          model?.deliveredCitizenGuid,
          citizenDeliveredGuid,
        ].firstFilled;

        ProductState state = model != null && acquiredCitizenGuid.isFilled
            ? ProductState.acquired
            : model?.stateEnum ?? ProductState.available;

        _savedModel = ProductModel(
          cleanPointGuid: home!.selectedCleanPoint?.guid ?? "",
          isSync: false,
          isRemoved: false,
          name: nameField.text,
          description: descriptionField.text,
          creatorUserId: model?.creatorUserId ?? user.id!,
          deliveredCitizenGuid: deliveredUserGuid,
          deliveredDate: model?.deliveredDate ??
              (state == ProductState.available ? now : null),
          creationDate: model?.creationDate ?? now,
          modifyDate: now,
          imagesOrIdsMd5: images.distinct(),
          companyId: user.companyId!,
          id: model?.id ?? 0,
          guid: guid,
          categoryId: categoryId ?? 0,
          elementGuid: elementGuid ?? "",
          acquiredCitizenGuid: acquiredCitizenGuid,
          acquiredDate: model?.acquiredDate ??
              (state == ProductState.acquired ? now : null),
          reservedDate: model?.reservedDate,
          reservedCitizenGuid: model?.reservedCitizenGuid ?? "",
          state: state.id,
          productRequestGuid: isStringFilled(model?.productRequestGuid)
              ? model!.productRequestGuid
              : request?.guid ?? "",
          residueId: residueId,
          inputSignatureMd5: model?.inputSignatureMd5,
          outputSignatureMd5: model?.outputSignatureMd5,
          inputPdfMd5: model?.inputPdfMd5,
          outputPdfMd5: model?.outputPdfMd5,
          favoritesCitizens: model?.favoritesCitizens ?? [],
        );

        final result = await productUseCase.save(
          ProductUseCaseValidator(
            model: _savedModel!,
            productUseCase: productUseCase,
            cleanPointBuildingUseCase: cleanPointBuildingUseCase,
            citizenUseCase: citizenUseCase,
            userSSOUseCase: userSSOUseCase,
            user: user,
            elementUseCase: elementUseCase,
            equipmentUseCase: equipmentUseCase,
            residueUseCase: residueUseCase,
          ),
        );

        if (result.isError) {
          dialog.error(result.error);
          _savedModel = null;
          return;
        }
      }

      if (_savedModel == null) return;

      // Mostramos el mensaje de exportación a PDF solo en la creación o cuando el producto es adquirido.
      if (model == null || model!.stateEnum != _savedModel!.stateEnum) {
        String? lastSig;
        if (param.model != null && param.model!.inputSignatureMd5.isFilled) {
          lastSig = await productUseCase
              .getAttachment(param.model!.inputSignatureMd5!);
        }

        final signatureBase64 =
            await SignatureDialog().show(currentSignatureBase64: lastSig);

        if (signatureBase64.isNullOrEmpty)
          return dialog.error(
              TTShared.noSeHaPodidoGuardarElPdf.tt.point().jump() +
                  TTShared.noSeHaIndicadoUnaFirma.tt);

        final signatureSaved = await productUseCase.saveAttachment(
          _savedModel!.guid,
          ProductAttachmentAction.input,
          ProductAttachmentType.signature,
          signatureBase64!,
        );

        if (!signatureSaved)
          return dialog.error(
            TTShared.error.tt.jump() + TTShared.noSeHaPodidoGuardarLaFirma.tt,
          );

        final isConfirmedPdf = await DialogService().showConfirmDialog(
            tt(TTShared.cambiosGuardados).point().jump().jump() +
                TTShared.quieresVerElPdfPregunta.tt);

        final Uint8List? pdf = await _savedModel!
            .toPdf(print: false, signatureBase64: signatureBase64);

        if (pdf == null)
          return dialog.error(
            TTShared.error.tt.jump() + TTShared.noSeHaPodidoGenerarElPdf.tt,
          );

        final pdfBase64 = base64Encode(pdf);

        final pdfSaved = await productUseCase.saveAttachment(
          _savedModel!.guid,
          ProductAttachmentAction.input,
          ProductAttachmentType.pdf,
          pdfBase64,
        );

        if (!pdfSaved)
          return dialog.error(
            TTShared.error.tt.jump() + TTShared.noSeHaPodidoGuardarElPdf.tt,
          );

        if (isConfirmedPdf)
          PdfExporter([Map()], isCheckboxChecked: false).print(pdf);
      }

      dialog.valid(tt(TTShared.cambiosGuardados), () {
        builders.productPageFactory.remove(
          cubit,
          ProductPageParam(
            model: model,
            request: request,
          ),
        );
      });
    }, timeout: Duration(minutes: 6));
  }

  Future<void> onPressedReadQRDelivered() async {
    final person = await _onPressedReadQR();
    if (person == null) return;

    citizenAcquiredGuid = person.guid;
    rebuild();
  }

  static Future<CitizenModel?> _onPressedReadQR() async {
    final qr = await qrService.execute();
    if (qr.isEmpty) return null;

    if (!citizenManager.models.isQRMine(qr)) {
      // ignore: unawaited_futures
      DialogService().showInfoDialog(TTShared.noEsValida.tt);
      return null;
    }

    final person = citizenManager.models.getByQR(qr);
    if (person == null) {
      await DialogService().showInfoDialog(TTShared.noSeHaEncontrado.tt);
      return null;
    }

    person;
    return null;
  }

  static Future<void> onPressedChangeProductToAcquired({
    required void Function() onCompleted,
    required ProductModel model,
  }) async {
    bool isPressedQR = false;
    String? acquiredCitizenGuid;

    if (!model.canBeAcquired) {
      await DialogService().showInfoDialog(
          tt(TTProduct.elReutilizableEnEstadoXNoSePuedeCambiarAlEstadoX, [
        model.stateEnum.tt,
        ProductState.acquired.tt,
      ]));
      return;
    }

    await DialogService().showSelectWidgetDialog<CitizenModel>(
      isEquals: (a, b) => a.pk == b.pk,
      builder: (m) => m.getSimpleListItemWidget(true),
      selection: citizenManager.getByPk(acquiredCitizenGuid ?? ""),
      options: citizenManager.models.sortByText((o) => o.name),
      getDisplayValue: null,
      getTextFilter: (o) => [
        o.idView,
        o.getNameComplete(LopdFactory()),
        o.getEmail(LopdFactory()),
      ].joinFilled(" - "),
      onSelected: (o) {
        acquiredCitizenGuid = o?.guid;
      },
      isShowedRadioButton: false,
      topWidget: !isMobile
          ? null
          : Container(
              width: double.maxFinite,
              child: NotificationIconTextContainer(
                icon: home!.readQRMobileButton.icon,
                getText: () => isMobileOrPWA
                    ? home!.readQRMobileButton.getTitle().toUpperCase()
                    : home!.readQRMobileButton.getTitle(),
                onPressed: () {
                  isPressedQR = true;
                  NavigatorService().pop();
                },
                iconColor: themeColors.white,
                color: themeColors.primaryBlueLight,
                textColor: themeColors.white,
              ),
            ),
    );

    if (isPressedQR) await _onPressedReadQR();

    final selected = citizenManager.getByPk(acquiredCitizenGuid ?? "");

    if (selected != null) {
      final isConfirmed = await DialogService().showConfirmDialog(
        tt(
          TTProduct.quieresEntregarReutilizableXAUsuarioXPregunta,
          [
            model.name,
            selected.name,
          ],
        ),
      );

      if (isConfirmed) {
        // ignore: unawaited_futures
        _modifyToAcquired(
          model: model,
          acquiredCitizenGuid: acquiredCitizenGuid!,
          onCompleted: onCompleted,
        );
      }
    }
  }

  static Future<void> _modifyToAcquired({
    required ProductModel model,
    required String acquiredCitizenGuid,
    required void Function() onCompleted,
  }) async {
    Result<String?>? result;
    ProductModel? newModel;

    await DialogService().showProgressDialog((dialog, context) async {
      newModel = model.copyWith(
        acquiredCitizenGuid: acquiredCitizenGuid,
        modifyDate: now,
        state: ProductState.acquired.id,
        acquiredDate: now,
      );

      result = await productUseCase.save(
        ProductUseCaseValidator(
          model: newModel!,
          productUseCase: productUseCase,
          cleanPointBuildingUseCase: cleanPointBuildingUseCase,
          citizenUseCase: citizenUseCase,
          userSSOUseCase: userSSOUseCase,
          user: user,
          elementUseCase: elementUseCase,
          equipmentUseCase: equipmentUseCase,
          residueUseCase: residueUseCase,
        ),
      );

      if (result!.isError) {
        dialog.error(result!.error);
        return;
      }

      dialog.valid(tt(TTShared.cambiosGuardados));
    });

    if (isTrue(result?.isValid)) {
      // We show the pdf export message only in creation or when the product is acquired.
      if (model.stateEnum != newModel!.stateEnum) {
        final isConfirmedPdf = await DialogService()
            .showConfirmDialog(TTShared.quieresGenerarElPdf.tt);

        if (isConfirmedPdf) {
          // ignore: unawaited_futures
          newModel!.toPdf();
        }
      }

      onCompleted();
    }
  }
}
