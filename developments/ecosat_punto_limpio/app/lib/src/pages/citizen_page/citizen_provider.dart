import '../../_imports.dart';

class CitizenProvider extends ProviderBaseV2 {
  List<CitizenAddressInputData> addresses = [];

  StreamCustomValue<int> tabSelectedStream = StreamCustomValue<int>(0);

  final nameField = FieldController(isRequired: true);
  final surnameField = FieldController();
  final emailField = FieldController(isRequired: true);
  final dniField = FieldController();
  final observationsField = FieldController();
  final phoneField = FieldController();
  final pointsField = FieldController();

  bool isEditing = false;
  bool isNew = false;

  final void Function(LocationSelectionPageParam param)
      onPressedLocationSelection;
  CitizenModel? get model => param.model;

  final CitizenPageParam param;

  CitizenProvider({
    required this.onPressedLocationSelection,
    required this.param,
  });

  @override
  Future<void> internalStart() async {
    isNew = model == null;
    nameField.text = model?.name ?? '';
    surnameField.text = model?.surname ?? '';
    emailField.text = model?.email ?? '';
    phoneField.text = model?.phone ?? '';
    dniField.text = model?.nif ?? '';
    observationsField.text = model?.observations ?? '';
    pointsField.text = model?.cleanPointInfo?.points.toString() ?? '';

    addresses = model?.addresses
            .map2((m) => CitizenAddressInputData(this, m))
            .toList() ??
        [];

    if (model == null) {
      isEditing = true;
    }
  }

  bool get isMine => model?.id == user.id;
  bool get isEditableByAdmin => user.isAdmin;

  /// When there are only 1 model, we don't need to validate individually.
  /// So we can try to save it directly.
  bool get isAnyModelEditing =>
      addresses.length > 1 && addresses.any((m) => m.isEditing);
  bool get isAnyModelNotSaved => addresses.any((m) => !m.isSaved);

  Future<Result<String>> canOpenPage() async {
    if (!user.isAdmin && model?.id != user.id)
      return Result.error(
        tt(TTShared.noTienesAccesoAEsteApartado),
      );

    return Result.valid();
  }

  void onPressedSave() {
    DialogService().showProgressDialog((dialog, context) async {
      final guid = model?.guid ?? Guid().get();

      List<CitizenAddressModel> addressModels = [];
      List<CitizenUseCaseValidatorAddress> addressControllers = [];

      for (final a in addresses) {
        final m = a.generateModel();
        addressModels.add(m);
        addressControllers.add(CitizenUseCaseValidatorAddress(
          addressPK: m.pk,
          lopdFactory: LopdFactory(),
        ));
      }

      final m = CitizenModel(
        name: nameField.text,
        surname: surnameField.text,
        nif: dniField.text,
        phone: phoneField.text,
        email: emailField.text,
        observations: observationsField.text,
        creationDate: model?.creationDate ?? now,
        modifyDate: now,
        id: model?.id ?? 0,
        guid: guid,
        isRemoved: false,
        isSync: false,
        cadastralNumber: model?.cadastralNumber ?? '',
        cardIds: model?.cardIds ?? [],
        validationStatus:
            model?.validationStatus ?? CitizenValidationStatus.pending.id,
        zoneId: model?.zoneId ?? 0,
        address: "",
        autonomousCommunity: "",
        door: "",
        floor: "",
        municipality: "",
        number: "",
        postalCode: "",
        province: "",
        town: "",
        district: "",
        neighborhood: "",
        cleanPointInfo: model?.cleanPointInfo ??
            CitizenCleanPointInfoModel(
              id: 0,
              guid: Guid().get(),
              companyId: user.companyId!,
              citizenGuid: guid,
              points: 0,
              favorites: [],
            ),
        cards: model?.cards ?? [],
        addresses: addressModels,
      );

      final result = await citizenUseCase.save(
        CitizenUseCaseValidator(
          model: m,
          user: user,
          nameController: LopdIdType.ciudadanos_nombre.controller,
          surnameController: LopdIdType.ciudadanos_apellidos.controller,
          emailController: LopdIdType.ciudadanos_correoElectronico.controller,
          dniController: LopdIdType.ciudadanos_nif.controller,
          phoneController: LopdIdType.ciudadanos_telefono.controller,
          addressControllers: addressControllers,
        ),
      );

      if (result.isError) return dialog.error(result.error);

      dialog.valid(
        tt(model != null
            ? TTShared.registroModificado
            : TTShared.registroCreado),
        () {
          builders.citizenPageFactory.remove(cubit, param);
          param.onSaved?.call(m);
        },
      );
    });
  }

  void onPressedEdit() {
    isEditing = true;
    rebuild();
  }

  void onUserRemovePressed() {
    DialogService().showConfirmResultDialog(
      question: tt(TTShared.quieresEliminarElRegistroPregunta),
      operation: () => citizenUseCase.remove(model!),
      completion: tt(TTShared.registroEliminado),
      onCompleted: () => builders.citizenPageFactory.remove(cubit, param),
    );
  }

  Future<void> onPressedAddOther() async {
    addresses.add(CitizenAddressInputData(this, null));
    rebuild();
  }

  Future<void> onPressedRemoveData(CitizenAddressInputData d) async {
    final isConfirmed = await DialogService().showConfirmDialog(
      TTShared.quieresDescartarElRegistro.tt
          .jump()
          .jump()
          .concat(d.getAddressComplete()),
    );

    if (!isConfirmed) return;

    d.isRemoved = true;
    rebuild();
    d.dispose();
  }

  Future<void> onPressedSwitchDefault(CitizenAddressInputData d) async {
    if (addresses.isOneOrLess) return;

    addresses.forEach2((d) => d.isDefault = false);
    d.isDefault = true;
    rebuild();
  }

  @override
  void internalDispose() {
    phoneField.dispose();
    emailField.dispose();
    surnameField.dispose();
    observationsField.dispose();
    nameField.dispose();
    dniField.dispose();
    pointsField.dispose();
    rebuild.close();
  }
}
