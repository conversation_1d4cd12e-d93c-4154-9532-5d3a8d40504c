import '../_imports.dart';

class GetInspectionTemplatesApi {
  final String urlServer;
  final String token;
  final String imei;
  final String lastSynchronization;

  GetInspectionTemplatesApi({
    required this.urlServer,
    required this.token,
    required this.imei,
    required this.lastSynchronization,
  });

  Future<List<GetInspectionTemplatesDto>> execute() async {
    final response = await Api().post(
      url: "$urlServer${Prefs().getInspectionTemplatesUrl}",
      body: {
        "imei": imei,
        "ultFechaMod": lastSynchronization,
      },
      header: Api().getHeaderToken(token),
    );

    if (response.isError) return [];

    return List<GetInspectionTemplatesDto>.generate(response.response.length,
        (i) {
      return GetInspectionTemplatesDto.fromJson(response.response[i]);
    });
  }
}
