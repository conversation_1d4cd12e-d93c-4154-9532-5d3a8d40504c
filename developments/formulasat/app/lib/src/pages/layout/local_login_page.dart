import '../../_imports.dart';

class LocalLoginPage extends StatefulWidget {
  @override
  _LocalLoginPageState createState() => _LocalLoginPageState();
}

class _LocalLoginPageState extends State<LocalLoginPage> {
  @override
  Widget build(BuildContext context) {
    final loginBloc = LoginProvider(
      languages: [
        LanguageType.spanish,
        LanguageType.english,
        LanguageType.valencian,
      ],
      loginUseCase: LoginUseCase(
        isValidTicket: () => global.isValidTicket(),
        pushLoginPage: () {
          NavigatorService().pushReplacement(LocalLoginPage());
        },
        autologinUserEmail: preferences.autologinUserEmail,
        autologinUserPassword: preferences.autologinUserPassword,
        autologinSSOCompanyId: preferences.autologinSSOCompanyId,
        autologinSSORoleId: preferences.autologinSSORoleId,
        ticketUseCase: injector.get<TicketUseCase>(),
        connection: connection,
        applicationId: user.app.id,
        setTicket: (t) => global.ticket = t,
        onCompletedLogin: () {
          NavigatorService().pushReplacement(
            LayoutPage(child: FormListPage()),
          );
        },
        getLoggedUser: global.getLoggedUser,
        app: user.app,
        ssoUseCase: ssoUseCase,
      ),
    );

    // Usamos la pagina de login compartida por todas las aplicaciones
    return LoginPage(
      // TODO - Implementar recuperar contraseña
      // TODO - Implementar registro de usuario
      loginBloc,
      loginWithEmail: true,
      // onRestorePassword: () {},
      // appNameLogo:
      //     ImageCacheService().getFromAsset("assets/images/logoCompleto.png"),
    );
  }
}
