import 'package:database_hive_provider/database_hive_provider.dart';
import 'package:pipeline_date_formatting/pipeline_date_formatting.dart';
import 'package:pipeline_log/pipeline_log.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;

import '_imports.dart';

class FormulasatApp extends StatelessWidget {
  final FormulasatEnvironment env;
  final String? ticketId;
  final String? path;
  String _formGuid = "";

  FormulasatApp({
    required this.env,
    required this.ticketId,
    required this.path,
  }) {
    injector.registerSingleton<FormulasatEnvironment>(env);
  }

  bool isTryingToAccessForm() {
    bool isGuid = false;
    if (path != null && path!.isNotEmpty) {
      final pathWithoutSlash = path!.replaceAll("/", "");
      final regExp = RegExp(globalRegexGuid);
      isGuid = regExp.hasMatch(pathWithoutSlash);
      if (isGuid) {
        _formGuid = pathWithoutSlash;
      }
    }
    return isGuid;
  }

  @override
  Widget build(BuildContext context) {
    return RestartWidget(
      child: ThemePage(
        builder: (_) => riverpod.ProviderScope(
          child: GetMaterialApp(
            scrollBehavior: ScrollBehaviourMultiplatform(),
            title: 'Formulasat',
            getPages: NavigationRoutesNamed.getPages(),
            initialRoute: '/',
            unknownRoute:
                GetPage(name: '/notfound', page: () => const NotFoundPage()),
            navigatorKey: Get.key,
            localizationsDelegates: [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: [
              const Locale('es', ''),
            ],
            theme: ThemeData(
              visualDensity: VisualDensity.adaptivePlatformDensity,
              scaffoldBackgroundColor: colorGrisFondo,
              colorSchemeSeed: colorPrimarioOscuro,
            ),
            // theme: ThemeProvider().theme.themeData,
            home: Builder(
              builder: (___) => StartPage(
                splashScreenContainer:
                    SplashScreenContainer(message: "BIENVENIDO"),
                pipelineController: PipelineController(
                  steps: [
                    // Iniciamos el servicio de log.
                    () => PipelineLog(),

                    // Iniciamos los servicios internos de la app
                    () => PipelineStep(
                          onExecute: () async {
                            await FormulasatInjector().init();
                          },
                          onErrorFunction: () async {},
                        ),

                    // Inicia la base de datos.
                    () => PipelineStep(
                        // La base de datos Hive a veces no arranca y tampoco genera error.
                        timeout: Duration(seconds: 10),
                        onExecute: () async {
                          Log().debug("INICIANDO BASE DE DATOS");

                          await injector.get<DataServiceBase>().startDatabase(
                                () => DatabaseHiveService()
                                    .startDatabase(DatabaseHiveProvider()),
                              );

                          // await DatabaseHydratedBlocService().startDatabase();
                        },
                        onErrorFunction: () async {
                          print("ERROR EN LA BASE DE DATOS");
                        }),

                    // Iniciamos el servicio de formateo de fechas
                    () => PipelineDateFormatting(),

                    // Iniciamos servicio de traducciónes.
                    () => PipelineStep(
                          onExecute: () async {
                            Log().debug("ESTABLECIENDO TRADUCCIONES");
                            await TranslationServiceGlobal().start(
                              defaultLanguageType: LanguageType.spanish,
                              languagePref: PreferenceFake<int>(
                                //TODO: en un futuro habrá que hacer un mapeo entre idiomas.
                                () async =>
                                    0, // Comentado para que asigne el idioma castellano. GlobalBloc().ticket?.user?.language?.id ?? 0,
                                // Todavía no se puede modificar las preferencias del usuario vía API en el SSO.
                                (v) async {},
                              ),
                              translationProvider: TranslationProviderJson(),
                              translationProviderAdditional:
                                  TranslationProviderAdditionalSSO(
                                textSSOManager,
                              ),
                            );
                          },
                          onErrorFunction: () async {},
                        ),

                    // Iniciamos el servicio de conexiónes.
                    () => PipelineStep(
                          onExecute: () async {
                            Log().debug("INICIANDO CONNECTION SERVICE");
                            // TODO - Validar el error de conexión. se ha quitado el await para continuar trabajando
                            injector.get<ConnectionServiceBase>().start();
                            Log().debug("CONNECTION SERVICE INICIADO");
                          },
                          onErrorFunction: () async {
                            Log().error("ERROR EN CONNECTION SERVICE");
                          },
                        ),

                    () => PipelineStep(
                          onExecute: () async {
                            // Ajustamos los colores iniciales

                            colorPrimario = colorPrimarioOscuroAlt;

                            if (isWebReal) {
                              if (MediaQuery.of(context).size.width <
                                  globalBreakToMovilSize) {
                                globalScale = 0.85;
                              } else if (MediaQuery.of(context).size.width <
                                  globalBreakToTabletSize) {
                                globalScale = 0.85;
                              } else if (MediaQuery.of(context).size.width <
                                  globalBreakToDesktopSize) {
                                globalScale = 0.85;
                              } else {
                                globalScale = 1.0;
                              }
                            } else {
                              globalScale = 0.95;
                            }
                          },
                          onErrorFunction: () async {},
                        ),
                  ],
                  onCompleted: () async {
                    await LoginUseCase(
                      isValidTicket: () => global.ticket?.user?.id != null,
                      pushLoginPage: () {
                        // Si intentamos acceder a un formulario directamente, lo abrimos.
                        if (isTryingToAccessForm()) {
                          NavigationRoutesNamed.replaceWithForm(_formGuid);
                          return;
                        }

                        // TODO - Revisar como se maneja el sistema interno de la app, ya que esta app no emite estados, sino que se maneja con el sistema de rutas.
                        NavigatorService().pushReplacement(LocalLoginPage());
                        // NavigatorService().pushReplacement(FinishedPage());

                        // GoRouter.of(context).go('/login');
                      },
                      autologinUserEmail: preferences.autologinUserEmail,
                      autologinUserPassword: preferences.autologinUserPassword,
                      autologinSSOCompanyId: preferences.autologinSSOCompanyId,
                      autologinSSORoleId: preferences.autologinSSORoleId,
                      ticketUseCase: injector.get<TicketUseCase>(),
                      connection: connection,
                      applicationId: user.app.id,
                      setTicket: (t) => global.ticket = t,
                      onCompletedLogin: () {
                        if (isTryingToAccessForm()) {
                          NavigationRoutesNamed.replaceWithForm(_formGuid);
                          return;
                        }
                        NavigatorService().pushReplacement(
                          LayoutPage(child: FormListPage()),
                        );
                        // GoRouter.of(context).go('/');
                      },
                      getLoggedUser: global.getLoggedUser,
                      app: user.app,
                      ssoUseCase: ssoUseCase,
                    ).tryAutologin(ticketId ?? "");
                  },
                  onError: (e, i) async {
                    final message = "Error en el pipeline de inicio ($i): $e";
                    // Lo imprimimos para que se pueda ver en la consola de la web.
                    print(message);
                    Log().catastrophe(message);
                    Log().info(
                        "Borrando la base de datos y reiniciando la aplicación");
                    try {
                      await DatabaseHiveService().repair(isRemovedAll: true);
                      await DatabaseHiveService().close();
                    } catch (e) {}
                    env.restartService.restart();
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class RestartWidget extends StatefulWidget {
  RestartWidget({this.child});

  final Widget? child;

  static void restartApp(BuildContext context) {
    context.findAncestorStateOfType<_RestartWidgetState>()!.restartApp();
  }

  @override
  _RestartWidgetState createState() => _RestartWidgetState();
}

class _RestartWidgetState extends State<RestartWidget> {
  Key key = UniqueKey();

  void restartApp() {
    setState(() {
      key = UniqueKey();
    });
  }

  @override
  Widget build(BuildContext context) {
    return KeyedSubtree(
      key: key,
      child: widget.child!,
    );
  }
}
