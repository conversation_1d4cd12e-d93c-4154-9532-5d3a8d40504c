import 'package:database_hive_provider/database_hive_provider.dart';
import 'package:flutter/services.dart';
import 'package:pipeline_date_formatting/pipeline_date_formatting.dart';
import 'package:pipeline_log/pipeline_log.dart';
import 'package:pipeline_new_version/pipeline_new_version.dart';
import 'package:pipeline_sso_environment_changed/pipeline_sso_environment_changed.dart';
import 'package:translation_provider_json/translation_provider_json.dart';
import 'package:pipeline_ticket_id_saver/pipeline_ticket_id_saver.dart';

import '_imports.dart';

class EcoCompostajeApp extends StatelessWidget {
  final EcoCompostajeEnvironment env;
  String? ticketId;

  EcoCompostajeApp({
    super.key,
    required this.env,
    required this.ticketId,
  }) {
    injector.registerSingleton<EcoCompostajeEnvironment>(env);
  }

  @override
  Widget build(BuildContext context) {
    return RestartWidget(
      child: ThemePage(
        builder: (_) => GetMaterialApp(
          scrollBehavior: ScrollBehaviourMultiplatform(),
          debugShowCheckedModeBanner: false,
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('es', ''),
          ],
          theme: ThemeProvider().theme.themeData,
          home: Builder(
            builder: (___) => StartPage(
              splashScreenContainer:
                  SplashScreenContainer(message: TTShared.bienvenido.tt),
              pipelineController: PipelineController(
                steps: [
                  () => PipelineLog(),
                  () => PipelineTicketIdSaver(
                        ticketId,
                        (newTicketId) => ticketId = newTicketId,
                      ),
                  () => PipelineStep(
                        onExecute: () async {
                          await EcoCompostajeInjector().init();
                        },
                        onErrorFunction: () async {},
                      ),
                  // Inicia la base de datos.
                  () => PipelineStep(
                      // La base de datos Hive a veces no arranca y tampoco genera error.
                      timeout: const Duration(seconds: 20),
                      onExecute: () async {
                        Log().debug("INICIANDO BASE DE DATOS");

                        await injector.get<DataServiceBase>().startDatabase(
                              () => DatabaseHiveService()
                                  .startDatabase(DatabaseHiveProvider()),
                            );

                        // await DatabaseHydratedBlocService().startDatabase();
                      },
                      onErrorFunction: () async {
                        print("ERROR EN LA BASE DE DATOS");
                      }),

                  () => PipelineDateFormatting(),

                  // Iniciamos servicio de traducción.
                  () => PipelineStep(
                        onExecute: () async {
                          Log().debug("ESTABLECIENDO TRADUCCIONES");
                          await TranslationServiceGlobal().start(
                            defaultLanguageType: LanguageType.spanish,
                            languagePref: preferences.language,
                            translationProvider: TranslationProviderJson(),
                            translationProviderAdditional:
                                TranslationProviderAdditionalSSO(
                                    textSSOManager),
                          );
                        },
                        onErrorFunction: () async {},
                      ),
                  () => PipelineStep(
                        onExecute: () async {
                          Log().debug("INICIANDO CONNECTION SERVICE");
                          await injector.get<ConnectionServiceBase>().start();
                        },
                        onErrorFunction: () async {},
                      ),

                  () => PipelineSsoEnvironmentChanged(
                        removeDatabase: () async {
                          await DatabaseHiveService()
                              .repair(isRemovedAll: true);
                        },
                        restartApp: () async {
                          await env.restartService.restart();
                        },
                      ),

                  () => PipelineNewVersion(
                        packageInfoService: packageInfoService,
                        onNewVersion: (before, current) async {
                          if (before.isEmpty) return;
                          await DatabaseHiveService().repair();
                        },
                        onPostNewVersion: (before, current) async {
                          if (before.isEmpty) return;
                          // Siempre que haya una nueva actualización, reiniciamos.
                          // Principalmente se hace para web, para que cargue los ficheros de la nueva versión.
                          // También se hace por si se ha reparado la base de datos, para no olvidarse de reiniciar.
                          env.restartService.restart();
                        },
                      ),

                  // Banner informativo
                  () => PipelineStep(
                        onExecute: () async {
                          if (kIsWeb) return;
                          Log().debug("MOSTRANDO BANNER INFORMATIVO");
                          // Necesario para obtener la info del banner
                          await FirebaseCoreServiceGlobal().start(null);
                          await GlobalBloc().showBanner();
                        },
                        onErrorFunction: () async {},
                      ),

                  // Pipeline para escalar segun el tipo de dispositivo
                  // Definir los colores y cambios especificos para el sistema
                  () => PipelineStep(
                        onExecute: () async {
                          SystemChrome.setSystemUIOverlayStyle(
                            SystemUiOverlayStyle(
                              statusBarColor: colorPrimarioOscuroAlt,
                              systemNavigationBarColor: colorBlanco,
                              systemNavigationBarDividerColor: colorBlanco,
                              systemNavigationBarIconBrightness:
                                  Brightness.dark,
                            ),
                          );

                          if (isWeb) {
                            final screenSize =
                                MediaQuery.of(context).size.width;
                            globalScale = switch (screenSize) {
                              < globalBreakToMovilSize => 0.85,
                              < globalBreakToTabletSize => 0.85,
                              < globalBreakToDesktopSize => 0.85,
                              _ => 0.85,
                            };
                          } else {
                            globalScale = 0.95;
                          }
                        },
                        onErrorFunction: () async {},
                      ),
                ],
                onCompleted: () async {
                  await LoginUseCase(
                    isValidTicket: () => global.ticket?.user?.id != null,
                    pushLoginPage: () {
                      NavigatorService().push(const LocalLoginPage());
                    },
                    autologinUserEmail: preferences.autologinUserEmail,
                    autologinUserPassword: preferences.autologinUserPassword,
                    autologinSSOCompanyId: preferences.autologinSSOCompanyId,
                    autologinSSORoleId: preferences.autologinSSORoleId,
                    ticketUseCase: injector.get<TicketUseCase>(),
                    connection: connection,
                    applicationId: user.app.id,
                    setTicket: (t) => global.ticket = t,
                    onCompletedLogin: () {
                      global.onCompletedLogin(
                        page: LocalHomePage(),
                        pageOnboard: const OnboardPage(),
                      );
                    },
                    getLoggedUser: global.getLoggedUser,
                    app: user.app,
                    ssoUseCase: ssoUseCase,
                  ).tryAutologin(ticketId ?? "");
                },
                onError: (e, i) async {
                  final message = "Error en el pipeline de inicio ($i): $e";
                  // Lo imprimimos para que se pueda ver en la consola de la web.
                  print(message);
                  Log().catastrophe(message);
                  Log().info(
                      "Borrando la base de datos y reiniciando la aplicación");
                  try {
                    await DatabaseHiveService().repair(isRemovedAll: true);
                    await DatabaseHiveService().close();
                  } catch (e) {}
                  env.restartService.restart();
                },
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class RestartWidget extends StatefulWidget {
  const RestartWidget({super.key, this.child});

  final Widget? child;

  static void restartApp(BuildContext context) {
    context.findAncestorStateOfType<_RestartWidgetState>()!.restartApp();
  }

  @override
  _RestartWidgetState createState() => _RestartWidgetState();
}

class _RestartWidgetState extends State<RestartWidget> {
  Key key = UniqueKey();

  void restartApp() {
    setState(() {
      key = UniqueKey();
    });
  }

  @override
  Widget build(BuildContext context) {
    return KeyedSubtree(
      key: key,
      child: widget.child!,
    );
  }
}
