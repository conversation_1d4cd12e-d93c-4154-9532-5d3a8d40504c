import '../../../_imports.dart';

class LocalLoginPage extends StatefulWidget {
  const LocalLoginPage({super.key});

  @override
  _LocalLoginPageState createState() => _LocalLoginPageState();
}

class _LocalLoginPageState extends State<LocalLoginPage> {
  final loginBloc = LoginProvider(
    languages: global.languages,
    loginUseCase: LoginUseCase(
      isValidTicket: () => global.isValidTicket(),
      pushLoginPage: () =>
          NavigatorService().pushReplacement(const LocalLoginPage()),
      autologinUserEmail: preferences.autologinUserEmail,
      autologinUserPassword: preferences.autologinUserPassword,
      autologinSSOCompanyId: preferences.autologinSSOCompanyId,
      autologinSSORoleId: preferences.autologinSSORoleId,
      ticketUseCase: injector.get<TicketUseCase>(),
      connection: connection,
      applicationId: user.app.id,
      setTicket: (t) => global.ticket = t,
      onCompletedLogin: () => global.onCompletedLogin(
        page: LocalHomePage(),
        pageOnboard: const OnboardPage(),
      ),
      // onPreLoging: (email) => environment.onTryingLogin(email),
      getLoggedUser: global.getLoggedUser, 
      app: user.app,
      ssoUseCase: ssoUseCase,
    ),
  );

  void createAccount() async {
    List<MunicipalityModel> municipalities = [];

    await DialogService().showProgressDialog((dialog, context) async {
      final either = await MunicipalityGetUseCaseView.execute(
        connection: connection,
        ssoUseCase: ssoUseCase,
        app: user.app,
        isInPolygon: (ll, p) =>
            positionUtilService.isInPolygon(position: ll, points: p),
      );

      if (either.isRight) {
        dialog.error(either.right);
        return;
      }

      // Ignoramos los servidores de movisat (companyIdSSO = 2 o 1).
      municipalities = either.left
              ?.where2((m) => m.companyIdSSO != 2 && m.companyIdSSO != 1) ??
          [];

      if (municipalities.isEmpty) {
        dialog.error(TTShared.noSeHanEncontradoMunicipiosCercanos.tt);
        return;
      }
      dialog.hide();
    });

    if (municipalities.isEmpty) return;

    if (municipalities.isOne) {
      NavigatorService().push(
        UserRegisterPage(
          UserRegisterBloc(
            onClosed: () {
              NavigatorService().pushReplacement(const LocalLoginPage());
            },
            municipality: municipalities.first,
          ),
        ),
      );

      return;
    }

    NavigatorService().push(
      MunicipalitySelectionPage(
        MunicipalitySelectionProvider(municipalities),
      ),
    );
  }

  void restorePassword() {
    UserRecoverPasswordBloc(
      connection: injector.get<ConnectionServiceBase>(),
      onClosed: () => NavigatorService().pop(),
    ).openPage();
  }

  @override
  Widget build(BuildContext context) {
    return LoginPage(
      loginBloc,
      onCreateAccount: () => createAccount(),
      onRestorePassword: () => restorePassword(),
      appNameLogo:
          ImageCacheService().getFromAsset("assets/images/ecocompostaje.svg"),
      loginWithEmail: true,
    );
  }
}
